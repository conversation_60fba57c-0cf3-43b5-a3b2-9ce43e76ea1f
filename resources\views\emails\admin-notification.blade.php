<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>رسالة جديدة من العميل - ميتاء تك</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            direction: rtl;
            text-align: right;
            margin: 0;
            padding: 0;
            background-color: #f8fafc;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
        }
        .content {
            padding: 30px;
        }
        .alert {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .alert-icon {
            color: #dc2626;
            font-size: 20px;
            margin-left: 10px;
        }
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background-color: #f9fafb;
            border-radius: 6px;
            overflow: hidden;
        }
        .info-table td {
            padding: 12px 15px;
            border-bottom: 1px solid #e5e7eb;
        }
        .info-table td:first-child {
            font-weight: bold;
            color: #374151;
            width: 30%;
            background-color: #f3f4f6;
        }
        .message-content {
            background-color: #f8fafc;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 20px;
            margin: 15px 0;
            line-height: 1.6;
        }
        .btn {
            display: inline-block;
            background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
            color: white;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: bold;
            margin: 10px 5px;
        }
        .btn-danger {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
        }
        .footer {
            background-color: #1f2937;
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <div class="header">
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 10px;">
                <img src="{{ asset('images/zdeuj.png') }}" alt="ميتاء تك" style="width: 50px; height: 50px; margin-left: 15px; background: rgba(255,255,255,0.1); padding: 6px; border-radius: 10px;">
                <div>
                    <h1 style="margin: 0;">🚨 رسالة جديدة من العميل</h1>
                    <p style="margin: 5px 0 0 0; opacity: 0.9;">تحتاج إلى مراجعة فورية</p>
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="content">
            <div class="alert">
                <span class="alert-icon">⚠️</span>
                <strong>تنبيه:</strong> تم استلام رسالة جديدة من العميل وتحتاج إلى مراجعة ورد سريع.
            </div>

            <h2 style="color: #1f2937; margin-bottom: 20px;">تفاصيل العميل والرسالة:</h2>

            <!-- Customer Details -->
            <table class="info-table">
                <tr>
                    <td>اسم العميل:</td>
                    <td><strong>{{ $contact->name }}</strong></td>
                </tr>
                <tr>
                    <td>البريد الإلكتروني:</td>
                    <td><a href="mailto:{{ $contact->email }}">{{ $contact->email }}</a></td>
                </tr>
                @if($contact->phone)
                <tr>
                    <td>رقم الهاتف:</td>
                    <td><a href="tel:{{ $contact->phone }}">{{ $contact->phone }}</a></td>
                </tr>
                @endif
                @if($contact->company)
                <tr>
                    <td>نوع الخدمة المطلوبة:</td>
                    <td>{{ $contact->company }}</td>
                </tr>
                @endif
                <tr>
                    <td>موضوع الرسالة:</td>
                    <td><strong>{{ $contact->subject }}</strong></td>
                </tr>
                <tr>
                    <td>تاريخ الإرسال:</td>
                    <td>{{ $contact->created_at->format('Y-m-d H:i:s') }}</td>
                </tr>
            </table>

            <!-- Message Content -->
            <h3 style="color: #1f2937;">محتوى الرسالة:</h3>
            <div class="message-content">
                {{ $contact->message }}
            </div>

            <!-- Priority Assessment -->
            <div style="background-color: #fffbeb; border: 1px solid #fbbf24; border-radius: 6px; padding: 15px; margin: 20px 0;">
                <h4 style="color: #92400e; margin-top: 0;">تقييم الأولوية:</h4>
                <p style="color: #92400e; margin-bottom: 0;">
                    @if(str_contains(strtolower($contact->subject), 'عاجل') || str_contains(strtolower($contact->message), 'عاجل'))
                        🔴 <strong>أولوية عالية</strong> - يحتوي على كلمة "عاجل"
                    @elseif($contact->phone)
                        🟡 <strong>أولوية متوسطة</strong> - العميل ترك رقم هاتف
                    @else
                        🟢 <strong>أولوية عادية</strong>
                    @endif
                </p>
            </div>

            <!-- Action Buttons -->
            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ route('admin.contacts.show', $contact->id) }}" class="btn">
                    📧 عرض الرسالة والرد
                </a>
                <a href="mailto:{{ $contact->email }}" class="btn btn-danger">
                    ✉️ رد سريع بالإيميل
                </a>
                @if($contact->phone)
                <a href="tel:{{ $contact->phone }}" class="btn">
                    📞 اتصال مباشر
                </a>
                @endif
            </div>

            <!-- Quick Response Templates -->
            <div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 6px; padding: 15px; margin: 20px 0;">
                <h4 style="color: #0c4a6e; margin-top: 0;">قوالب الرد السريع:</h4>
                <ul style="color: #0c4a6e; margin-bottom: 0;">
                    <li>شكراً لتواصلك معنا، سنراجع طلبك ونرد عليك خلال 24 ساعة</li>
                    <li>تم استلام طلبك، سيتواصل معك أحد المختصين قريباً</li>
                    <li>نحتاج لمزيد من التفاصيل، هل يمكنك التواصل معنا هاتفياً؟</li>
                </ul>
            </div>

            <!-- Statistics -->
            <div style="background-color: #f3f4f6; border-radius: 6px; padding: 15px; margin: 20px 0;">
                <h4 style="color: #374151; margin-top: 0;">إحصائيات سريعة:</h4>
                <p style="color: #6b7280; margin-bottom: 0;">
                    📊 إجمالي الرسائل اليوم: {{ \App\Models\Contact::whereDate('created_at', today())->count() }}<br>
                    ⏰ الرسائل غير المقروءة: {{ \App\Models\Contact::where('status', 'new')->count() }}<br>
                    ✅ الرسائل المحلولة هذا الأسبوع: {{ \App\Models\Contact::where('status', 'resolved')->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count() }}
                </p>
            </div>
        </div>

        <!-- Footer -->
        <div class="footer">
            <p style="margin: 0;">
                <strong>ميتاء تك - نظام إدارة الرسائل</strong><br>
                تم إرسال هذا التنبيه تلقائياً من نظام إدارة الرسائل
            </p>
        </div>
    </div>
</body>
</html>
