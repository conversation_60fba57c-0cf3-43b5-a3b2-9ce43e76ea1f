<?php

namespace Database\Seeders;

use App\Models\BlogPost;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class BlogPostSeeder extends Seeder
{
    private $categories = [
        'البرمجة', 'التصميم', 'التقنية', 'الأمان', 'تحليلات', 'التسويق',
        'قواعد البيانات', 'تصميم الجوال', 'الحوسبة السحابية', 'تطوير API',
        'التجارة الإلكترونية', 'DevOps', 'الذكاء الاصطناعي', 'البلوك تشين',
        'إمكانية الوصول', 'ريادة الأعمال', 'اختبار البرمجيات', 'Git وGitHub',
        'الشبكات', 'أنظمة التشغيل', 'الألعاب', 'الواقع المعزز', 'إنترنت الأشياء'
    ];

    private $authors = [
        // أسماء عربية
        'أحمد محمد الأحمدي', 'سارة أحمد العلي', 'محمد عبدالله الزهراني', 'فاطمة سالم القحطاني',
        'خالد عمر العمري', 'نورا حسن المالكي', 'عبدالله يوسف الشهري', 'مريم علي الدوسري',
        'سامي محمد الغامدي', 'هند عبدالرحمن السعدي', 'يوسف أحمد الحربي', 'زينب محمود العتيبي',
        'عمر سعد الفيصل', 'ليلى حمد الراشد', 'حسام طارق البلوي', 'رنا فهد المطيري',

        // أسماء أجنبية
        'John Smith', 'Sarah Johnson', 'Michael Brown', 'Emily Davis', 'David Wilson',
        'Jessica Miller', 'Christopher Moore', 'Ashley Taylor', 'Matthew Anderson', 'Amanda Thomas',
        'Daniel Jackson', 'Jennifer White', 'James Harris', 'Lisa Martin', 'Robert Thompson',
        'Maria Garcia', 'William Rodriguez', 'Elizabeth Martinez', 'Richard Lee', 'Linda Walker',

        // أسماء متنوعة
        'Ahmed Hassan', 'Fatima Al-Zahra', 'Hiroshi Tanaka', 'Yuki Sato', 'Mei Chen',
        'Wei Zhang', 'Raj Patel', 'Priya Sharma', 'Pierre Dubois', 'Marie Martin',
        'Hans Mueller', 'Anna Schmidt', 'Marco Rossi', 'Giulia Ferrari', 'Carlos Rodriguez',
        'Ana Martinez', 'Ivan Petrov', 'Natasha Volkov'
    ];

    private $techTopics = [
        'React', 'Vue.js', 'Angular', 'Laravel', 'Django', 'Node.js', 'Python',
        'JavaScript', 'TypeScript', 'PHP', 'Java', 'C#', 'Go', 'Rust',
        'Docker', 'Kubernetes', 'AWS', 'Azure', 'Google Cloud', 'MongoDB',
        'PostgreSQL', 'MySQL', 'Redis', 'Elasticsearch', 'GraphQL', 'REST API',
        'Flutter', 'React Native', 'Swift', 'Kotlin', 'Unity', 'Unreal Engine'
    ];

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Disable foreign key checks for faster insertion
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        // Clear existing posts
        BlogPost::truncate();

        $this->command->info('🚀 بدء إنشاء المقالات...');

        // Create posts in batches for better performance
        $batchSize = 5000; // Increase batch size for speed
        $totalPosts = 1000000; // 1 million posts as requested
        $batches = ceil($totalPosts / $batchSize);

        for ($i = 0; $i < $batches; $i++) {
            $this->command->info("📝 إنشاء الدفعة " . ($i + 1) . " من {$batches}");

            $posts = [];
            for ($j = 0; $j < $batchSize && ($i * $batchSize + $j) < $totalPosts; $j++) {
                $posts[] = $this->generatePost($i * $batchSize + $j + 1);
            }

            // Insert batch
            BlogPost::insert($posts);

            // Clear memory
            unset($posts);

            // Show progress
            $completed = min(($i + 1) * $batchSize, $totalPosts);
            $percentage = round(($completed / $totalPosts) * 100, 1);
            $this->command->info("✅ تم إنشاء {$completed} مقال من أصل {$totalPosts} ({$percentage}%)");
        }

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        $this->command->info('🎉 تم إنشاء جميع المقالات بنجاح!');
        $this->command->info("📊 إجمالي المقالات: {$totalPosts}");
    }

    private function generatePost($index): array
    {
        $category = $this->categories[array_rand($this->categories)];
        $author = $this->authors[array_rand($this->authors)];
        $tech = $this->techTopics[array_rand($this->techTopics)];

        $titleTemplates = [
            "دليل شامل لتعلم {$tech}",
            "أساسيات {$tech} للمبتدئين",
            "تطوير تطبيقات حديثة باستخدام {$tech}",
            "أفضل الممارسات في {$tech}",
            "مشروع عملي باستخدام {$tech}",
            "تحسين الأداء في {$tech}",
            "أمان التطبيقات مع {$tech}",
            "اختبار التطبيقات في {$tech}",
            "مقدمة في {$category}",
            "أساسيات {$category} المتقدمة",
            "نصائح وحيل في {$category}",
            "دليل المبتدئين لـ {$category}",
            "تطوير مشاريع {$category} احترافية",
            "أدوات {$category} الأساسية",
            "مستقبل {$category} في 2024",
            "تعلم {$category} خطوة بخطوة"
        ];

        $title = $titleTemplates[array_rand($titleTemplates)];
        if ($index > 1) {
            $title .= " - الجزء {$index}";
        }

        $slug = $this->generateSlug($title, $index);

        $excerpts = [
            'تعلم أساسيات التطوير وكيفية بناء تطبيقات قوية ومرنة باستخدام أحدث التقنيات والأدوات المتاحة في السوق.',
            'اكتشف أفضل الممارسات في التطوير وتعلم كيفية كتابة كود نظيف وقابل للصيانة والتطوير المستمر.',
            'دليل شامل يغطي جميع جوانب التطوير من الأساسيات إلى المفاهيم المتقدمة والتطبيقات العملية الحديثة.',
            'تعلم كيفية بناء مشاريع حقيقية وحل المشاكل التقنية بطريقة فعالة ومبتكرة تواكب التطورات الحديثة.',
            'اكتشف أحدث التقنيات والأدوات المستخدمة في التطوير الحديث وكيفية تطبيقها في مشاريعك بكفاءة عالية.',
            'نصائح وحيل من خبراء المجال لتحسين مهاراتك التقنية وزيادة إنتاجيتك في العمل والمشاريع الشخصية.',
            'استكشف عالم التقنية الحديثة وتعلم كيفية استخدام الأدوات المتطورة لتطوير حلول مبتكرة وفعالة.',
            'دليل عملي يساعدك على فهم المفاهيم المعقدة وتطبيقها في مشاريع حقيقية بطريقة سهلة ومفهومة.'
        ];

        $excerpt = $excerpts[array_rand($excerpts)];

        $content = $this->generateContent($category, $tech, $author);

        $images = [
            'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1558655146-9f40138edfeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1563206767-5b18f218e8de?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1518432031352-d6fc5c10da5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
        ];

        $tags = ['تقنية', 'برمجة', 'تطوير', $category, $tech];
        $selectedTags = array_slice(array_unique($tags), 0, rand(3, 5));

        return [
            'title' => $title,
            'slug' => $slug,
            'excerpt' => $excerpt,
            'content' => $content,
            'image' => $images[array_rand($images)],
            'category' => $category,
            'author' => $author,
            'read_time' => rand(3, 25) . ' دقيقة',
            'is_featured' => rand(1, 100) <= 3, // 3% featured
            'is_published' => rand(1, 100) <= 97, // 97% published
            'views' => rand(0, 15000),
            'tags' => json_encode($selectedTags),
            'meta_data' => json_encode([
                'seo_title' => $title,
                'seo_description' => $excerpt,
                'difficulty_level' => ['مبتدئ', 'متوسط', 'متقدم', 'خبير'][rand(0, 3)],
                'estimated_completion' => rand(30, 240) . ' دقيقة',
                'language' => 'ar',
                'keywords' => implode(', ', $selectedTags)
            ]),
            'created_at' => now()->subDays(rand(0, 730))->subHours(rand(0, 23))->subMinutes(rand(0, 59))->format('Y-m-d H:i:s'),
            'updated_at' => now()->subDays(rand(0, 30))->format('Y-m-d H:i:s')
        ];
    }

    private function generateSlug($title, $index): string
    {
        // Convert Arabic to English transliteration
        $arabicToEnglish = [
            'أ' => 'a', 'ب' => 'b', 'ت' => 't', 'ث' => 'th', 'ج' => 'j', 'ح' => 'h',
            'خ' => 'kh', 'د' => 'd', 'ذ' => 'dh', 'ر' => 'r', 'ز' => 'z', 'س' => 's',
            'ش' => 'sh', 'ص' => 's', 'ض' => 'd', 'ط' => 't', 'ظ' => 'z', 'ع' => 'a',
            'غ' => 'gh', 'ف' => 'f', 'ق' => 'q', 'ك' => 'k', 'ل' => 'l', 'م' => 'm',
            'ن' => 'n', 'ه' => 'h', 'و' => 'w', 'ي' => 'y', 'ة' => 'h', 'ى' => 'a',
            'إ' => 'i', 'آ' => 'a', 'ؤ' => 'o', 'ئ' => 'e', ' ' => '-', 'ـ' => ''
        ];

        $slug = strtr($title, $arabicToEnglish);
        $slug = preg_replace('/[^a-zA-Z0-9\-]/', '', $slug);
        $slug = strtolower($slug);
        $slug = preg_replace('/-+/', '-', $slug);
        $slug = trim($slug, '-');

        return $slug . '-' . $index;
    }

    private function generateContent($category, $tech, $author): string
    {
        $sections = [
            'مقدمة' => "في هذا المقال، سنتعلم أساسيات {$category} وكيفية استخدام {$tech} في مشاريعنا التقنية. سنغطي جميع الجوانب المهمة من الأساسيات إلى المفاهيم المتقدمة التي يحتاجها كل مطور.",

            'الأساسيات' => "لفهم {$category} بشكل صحيح، نحتاج أولاً إلى فهم المفاهيم الأساسية. {$tech} يوفر أدوات قوية تساعدنا في تحقيق أهدافنا التقنية بكفاءة عالية ودقة متناهية.",

            'التطبيق العملي' => "الآن سنتعلم كيفية تطبيق ما تعلمناه في مشاريع حقيقية. سنبني مثالاً عملياً يوضح قوة {$tech} في حل المشاكل التقنية المعقدة بطريقة أنيقة وفعالة.",

            'أفضل الممارسات' => "هناك مجموعة من أفضل الممارسات التي يجب اتباعها عند العمل مع {$tech}. هذه الممارسات ستضمن كتابة كود نظيف وقابل للصيانة والتطوير المستمر.",

            'نصائح متقدمة' => "للمطورين المتقدمين، هناك تقنيات وحيل متقدمة يمكن استخدامها لتحسين الأداء والكفاءة. سنستكشف هذه التقنيات وكيفية تطبيقها في مشاريعك.",

            'الخلاصة' => "في هذا المقال، تعلمنا أساسيات {$category} وكيفية استخدام {$tech} بفعالية. هذه المعرفة ستساعدك في تطوير مشاريع أكثر احترافية وجودة عالية."
        ];

        $content = '';
        foreach ($sections as $title => $text) {
            $content .= "<h2>{$title}</h2>\n<p>{$text}</p>\n\n";

            // Add some variety to content
            if (rand(1, 3) === 1) {
                $content .= "<ul>\n";
                $listItems = [
                    "تحسين الأداء والسرعة",
                    "ضمان الأمان والحماية",
                    "سهولة الصيانة والتطوير",
                    "التوافق مع المعايير الحديثة",
                    "دعم المجتمع والوثائق"
                ];
                foreach (array_slice($listItems, 0, rand(3, 5)) as $item) {
                    $content .= "<li>{$item}</li>\n";
                }
                $content .= "</ul>\n\n";
            }
        }

        $content .= "<p><em>كتب هذا المقال: {$author}</em></p>";

        return $content;
    }
}
