@extends('layouts.app')

@section('title', 'احصل على دومين جديد - ميتاء تك')

@push('styles')
<link rel="stylesheet" href="{{ asset('css/domain-search.css') }}">
@endpush

@section('content')
<!-- Hero Section -->
<section class="domain-search-container py-20 min-h-screen">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-800 mb-8 arabic-text">احصل على دومين جديد</h1>

            <!-- Toggle Buttons -->
            <div class="flex justify-center mb-8">
                <div class="bg-white rounded-full p-1 flex shadow-lg border border-gray-200">
                    <button id="existingDomainBtn" class="toggle-button bg-purple-600 text-white px-6 py-3 rounded-full text-sm font-medium transition-all duration-300">
                        هل عندك دومين جديد
                    </button>
                    <button id="aiDomainBtn" class="toggle-button text-gray-600 px-6 py-3 rounded-full text-sm font-medium hover:text-gray-800 transition-all duration-300">
                        أنشئ دومينًا باستخدام الذكاء الاصطناعي
                        <span class="bg-pink-500 text-white text-xs px-2 py-1 rounded-full mr-2">🔥</span>
                    </button>
                </div>
            </div>

            <p class="text-gray-600 text-lg mb-8 max-w-4xl mx-auto leading-relaxed arabic-text">
                اكتب اسم الدومين الذي ترغب في البحث عن توفره أو اختر من الامتدادات الشعبية لا يمكن أن تبدأ أو تنتهي بـ (-) أو أن تحتوي على مسافات أو رموز خاصة
            </p>
        </div>

        <!-- Domain Search -->
        <div class="max-w-4xl mx-auto mb-16">
            <form id="domainSearchForm" class="flex flex-col sm:flex-row gap-2 bg-white rounded-xl shadow-xl border border-gray-200 p-2">
                <div class="flex-1 relative">
                    <input type="text" id="domainInput" placeholder="ادخل اسم الدومين المطلوب"
                           class="search-input w-full px-6 py-4 text-gray-800 focus:outline-none text-lg rounded-xl arabic-text">
                    <div class="absolute left-4 top-1/2 transform -translate-y-1/2">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                </div>
                <button type="submit"
                        class="search-button text-white px-8 py-4 rounded-xl font-semibold transition duration-300 shadow-lg">
                    بحث
                </button>
            </form>
        </div>

        <!-- Search Results -->
        <div id="searchResults" class="hidden mb-12">
            <div class="bg-white rounded-xl shadow-xl border border-gray-200 p-8">
                <h3 class="text-2xl font-bold text-gray-800 mb-6 arabic-text">نتائج البحث</h3>
                <div id="resultsContainer" class="space-y-4">
                    <!-- Results will be populated by JavaScript -->
                </div>
            </div>
        </div>

        <!-- Domain Extensions Grid -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6 max-w-6xl mx-auto">
            <!-- .com Domain -->
            <div class="domain-card p-6 text-center cursor-pointer" onclick="selectDomain('com')">
                <div class="domain-extension text-3xl mb-3">.com</div>
                <div class="price-text text-lg font-semibold mb-1">4.99 دولار أمريكي</div>
                <div class="text-sm text-gray-500 arabic-text">السنة الأولى</div>
            </div>

            <!-- .shop Domain -->
            <div class="domain-card p-6 text-center cursor-pointer" onclick="selectDomain('shop')">
                <div class="domain-extension text-3xl mb-3">.shop</div>
                <div class="price-text text-lg font-semibold mb-1">11.99 دولار أمريكي</div>
                <div class="text-sm text-gray-500 arabic-text">السنة الأولى</div>
            </div>

            <!-- .io Domain -->
            <div class="domain-card p-6 text-center cursor-pointer" onclick="selectDomain('io')">
                <div class="domain-extension text-3xl mb-3">.io</div>
                <div class="price-text text-lg font-semibold mb-1">31.99 دولار أمريكي</div>
                <div class="text-sm text-gray-500 arabic-text">السنة الأولى</div>
            </div>

            <!-- .org Domain -->
            <div class="domain-card p-6 text-center cursor-pointer" onclick="selectDomain('org')">
                <div class="domain-extension text-3xl mb-3">.org</div>
                <div class="price-text text-lg font-semibold mb-1">7.99 دولار أمريكي</div>
                <div class="text-sm text-gray-500 arabic-text">السنة الأولى</div>
            </div>

            <!-- .منصة Domain -->
            <div class="domain-card p-6 text-center cursor-pointer" onclick="selectDomain('منصة')">
                <div class="domain-extension text-3xl mb-3">.منصة</div>
                <div class="price-text text-lg font-semibold mb-1">1.99 دولار أمريكي</div>
                <div class="text-sm text-gray-500 arabic-text">السنة الأولى</div>
            </div>

            <!-- .محل Domain -->
            <div class="domain-card p-6 text-center cursor-pointer" onclick="selectDomain('محل')">
                <div class="domain-extension text-3xl mb-3">.محل</div>
                <div class="price-text text-lg font-semibold mb-1">0.99 دولار أمريكي</div>
                <div class="text-sm text-gray-500 arabic-text">السنة الأولى</div>
            </div>
        </div>
    </div>
</section>



<script>
// Toggle buttons functionality
document.getElementById('existingDomainBtn').addEventListener('click', function() {
    this.classList.add('bg-purple-600', 'text-white');
    this.classList.remove('text-gray-600');
    document.getElementById('aiDomainBtn').classList.remove('bg-purple-600', 'text-white');
    document.getElementById('aiDomainBtn').classList.add('text-gray-600');
});

document.getElementById('aiDomainBtn').addEventListener('click', function() {
    this.classList.add('bg-purple-600', 'text-white');
    this.classList.remove('text-gray-600');
    document.getElementById('existingDomainBtn').classList.remove('bg-purple-600', 'text-white');
    document.getElementById('existingDomainBtn').classList.add('text-gray-600');

    // Show AI domain suggestion feature (placeholder)
    alert('ميزة إنشاء الدومين بالذكاء الاصطناعي قريباً!');
});

// Domain selection function
function selectDomain(extension) {
    const domainInput = document.getElementById('domainInput');
    const currentValue = domainInput.value.trim();

    if (currentValue) {
        // If there's already a domain name, trigger search with this extension
        searchDomainWithExtension(currentValue, extension);
    } else {
        // If no domain name, focus on input and show placeholder
        domainInput.focus();
        domainInput.placeholder = `ادخل اسم الدومين للبحث عن .${extension}`;
    }
}

// Search domain with specific extension
function searchDomainWithExtension(domainName, extension) {
    const resultsSection = document.getElementById('searchResults');
    const resultsContainer = document.getElementById('resultsContainer');

    resultsContainer.innerHTML = '<div class="text-center py-4"><div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div><p class="mt-2 text-gray-600">جاري البحث...</p></div>';
    resultsSection.classList.remove('hidden');

    // Simulate API call for specific extension
    fetch('{{ route("domains.search") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            domain: domainName,
            extensions: [extension]
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayResults(data.results);
        } else {
            resultsContainer.innerHTML = '<div class="text-center py-4 text-red-600">حدث خطأ أثناء البحث</div>';
        }
    })
    .catch(error => {
        resultsContainer.innerHTML = '<div class="text-center py-4 text-red-600">حدث خطأ أثناء البحث</div>';
    });
}

// Main domain search form
document.getElementById('domainSearchForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const domain = document.getElementById('domainInput').value.trim();
    if (!domain) {
        alert('يرجى إدخال اسم النطاق للبحث');
        return;
    }

    // Show loading
    const resultsSection = document.getElementById('searchResults');
    const resultsContainer = document.getElementById('resultsContainer');

    resultsContainer.innerHTML = '<div class="text-center py-4"><div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div><p class="mt-2 text-gray-600">جاري البحث...</p></div>';
    resultsSection.classList.remove('hidden');

    // API call for all extensions
    fetch('{{ route("domains.search") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({ domain: domain })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayResults(data.results);
        } else {
            resultsContainer.innerHTML = '<div class="text-center py-4 text-red-600">حدث خطأ أثناء البحث</div>';
        }
    })
    .catch(error => {
        resultsContainer.innerHTML = '<div class="text-center py-4 text-red-600">حدث خطأ أثناء البحث</div>';
    });
});

function displayResults(results) {
    const container = document.getElementById('resultsContainer');
    let html = '';

    results.forEach(result => {
        const statusClass = result.available ? 'border-green-200 bg-green-50 hover:bg-green-100' : 'border-red-200 bg-red-50';
        const statusIcon = result.available ? 'text-green-600' : 'text-red-600';
        const statusText = result.available ? 'متاح' : 'محجوز';
        const buttonHtml = result.available ?
            `<button onclick="orderDomain('${result.domain}', '${result.extension}', ${result.price})" class="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition duration-300 text-sm font-semibold shadow-md">
                اطلب الآن - $${result.price}
            </button>` :
            `<span class="text-gray-500 text-sm font-medium">غير متاح</span>`;

        html += `
            <div class="border rounded-xl p-6 ${statusClass} transition-all duration-300 hover:shadow-lg">
                <div class="flex justify-between items-center">
                    <div class="flex items-center">
                        <div class="w-4 h-4 rounded-full ${result.available ? 'bg-green-500' : 'bg-red-500'} ml-4 shadow-sm"></div>
                        <div>
                            <div class="text-xl font-bold text-gray-800">${result.domain}</div>
                            <div class="text-sm ${statusIcon} font-medium">${statusText}</div>
                            ${result.description ? `<div class="text-xs text-gray-500 mt-1">${result.description}</div>` : ''}
                        </div>
                    </div>
                    <div class="text-left">
                        ${buttonHtml}
                        ${result.available && result.renewal_price ? `<div class="text-xs text-gray-500 mt-1">التجديد: $${result.renewal_price}</div>` : ''}
                    </div>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// Order domain function
function orderDomain(domain, extension, price) {
    // Redirect to domain store page
    const domainName = domain.replace('.' + extension, '');
    window.location.href = `{{ route('user.domains.store') }}?search=${domainName}&extension=${extension}`;
}
</script>
@endsection
