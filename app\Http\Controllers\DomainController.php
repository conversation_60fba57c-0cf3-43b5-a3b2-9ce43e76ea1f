<?php

namespace App\Http\Controllers;

use App\Models\Domain;
use App\Models\DomainProvider;
use App\Models\Order;
use App\Services\DomainProviderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class DomainController extends Controller
{
    /**
     * Display domain search and pricing page
     */
    public function index()
    {
        $popularDomains = Domain::where('is_active', true)
            ->where('tld_category', 'popular')
            ->orderBy('sort_order')
            ->take(8)
            ->get();

        $countryDomains = Domain::where('is_active', true)
            ->where('tld_category', 'country')
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        $newDomains = Domain::where('is_active', true)
            ->where('tld_category', 'new')
            ->orderBy('sort_order')
            ->take(6)
            ->get();

        $premiumDomains = Domain::where('is_active', true)
            ->where('tld_category', 'premium')
            ->orderBy('sort_order')
            ->take(4)
            ->get();

        return view('domains.index', compact(
            'popularDomains',
            'countryDomains',
            'newDomains',
            'premiumDomains'
        ));
    }

    /**
     * Search for domain availability
     */
    public function search(Request $request)
    {
        $request->validate([
            'domain' => 'required|string|max:255',
            'extensions' => 'array'
        ]);

        $domainName = $this->sanitizeDomain($request->domain);
        $extensions = $request->extensions ?? ['com', 'net', 'org', 'sa'];

        $results = [];

        foreach ($extensions as $ext) {
            $fullDomain = $domainName . '.' . $ext;

            // Get domain pricing
            $domainInfo = Domain::where('extension', $ext)
                ->where('is_active', true)
                ->first();

            if ($domainInfo) {
                // Check availability with provider
                $provider = $domainInfo->domainProvider;
                $available = true; // Default to available

                if ($provider && $provider->is_active) {
                    $providerService = new DomainProviderService($provider);
                    $availabilityResult = $providerService->checkAvailability($fullDomain);
                    $available = $availabilityResult['available'] ?? true;
                }

                $results[] = [
                    'domain' => $fullDomain,
                    'extension' => $ext,
                    'available' => $available,
                    'price' => $domainInfo->registration_price,
                    'renewal_price' => $domainInfo->renewal_price,
                    'is_premium' => $domainInfo->is_premium,
                    'category' => $domainInfo->tld_category,
                    'domain_info' => $domainInfo
                ];
            }
        }

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'results' => $results,
                'searched_domain' => $domainName
            ]);
        }

        return view('domains.search-results', compact('results', 'domainName'));
    }

    /**
     * Show domain order form
     */
    public function showOrderForm(Request $request)
    {
        $request->validate([
            'domain' => 'required|string',
            'extension' => 'required|string'
        ]);

        $domainName = $this->sanitizeDomain($request->domain);
        $extension = $request->extension;
        $fullDomain = $domainName . '.' . $extension;

        $domainInfo = Domain::where('extension', $extension)
            ->where('is_active', true)
            ->firstOrFail();

        return view('domains.order', compact('domainInfo', 'fullDomain', 'domainName'));
    }

    /**
     * Process domain order
     */
    public function processOrder(Request $request)
    {
        $request->validate([
            'domain_name' => 'required|string|max:255',
            'domain_extension' => 'required|exists:domains,extension',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'years' => 'required|integer|min:1|max:10',
            'service_type' => 'required|in:registration,transfer',
            'payment_method' => 'required|in:credit_card,bank_transfer,paypal',
        ]);

        try {
            DB::beginTransaction();

            $domain = Domain::where('extension', $request->domain_extension)->firstOrFail();
            $fullDomain = $this->sanitizeDomain($request->domain_name) . '.' . $request->domain_extension;

            // Calculate pricing
            $basePrice = $request->service_type === 'registration'
                ? $domain->registration_price
                : $domain->transfer_price;

            $totalPrice = $basePrice * $request->years;
            $setupFee = $domain->setup_fee;
            $subtotal = $totalPrice + $setupFee;
            $taxAmount = $subtotal * 0.15; // 15% VAT
            $finalAmount = $subtotal + $taxAmount;

            // Create order
            $order = Order::create([
                'order_number' => $this->generateOrderNumber(),
                'customer_name' => $request->customer_name,
                'customer_email' => $request->customer_email,
                'customer_phone' => $request->customer_phone,
                'service_type' => 'domain',
                'domain_name' => $fullDomain,
                'order_details' => [
                    'domain_extension' => $request->domain_extension,
                    'years' => $request->years,
                    'service_type' => $request->service_type,
                    'domain_info' => $domain->toArray(),
                ],
                'subtotal' => $subtotal,
                'tax_amount' => $taxAmount,
                'total_amount' => $finalAmount,
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_method' => $request->payment_method,
                'service_start_date' => now(),
                'service_end_date' => now()->addYears($request->years),
            ]);

            // Process payment
            $paymentResult = $this->processPayment($order, $request);

            if ($paymentResult['success']) {
                $order->update([
                    'payment_status' => 'paid',
                    'transaction_id' => $paymentResult['transaction_id'],
                ]);

                // Process domain registration/transfer
                $this->processDomainService($order);

                DB::commit();

                return redirect()->route('domains.order.success', $order)
                    ->with('success', 'تم إنشاء طلب الدومين بنجاح!');
            } else {
                DB::rollBack();
                return back()->withErrors(['payment' => 'فشل في معالجة الدفع: ' . $paymentResult['message']]);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Domain order processing failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return back()->withErrors(['error' => 'حدث خطأ أثناء معالجة الطلب: ' . $e->getMessage()]);
        }
    }

    /**
     * Show order success page
     */
    public function orderSuccess(Order $order)
    {
        return view('domains.success', compact('order'));
    }

    /**
     * Process domain service (registration/transfer)
     */
    private function processDomainService(Order $order)
    {
        $domain = Domain::where('extension', $order->order_details['domain_extension'])->first();

        if (!$domain || !$domain->domainProvider) {
            $order->update([
                'status' => 'pending',
                'admin_notes' => 'Manual processing required - no provider configured',
            ]);
            return;
        }

        $provider = $domain->domainProvider;
        $providerService = new DomainProviderService($provider);

        if ($order->order_details['service_type'] === 'registration') {
            $result = $providerService->registerDomain($order);
        } else {
            $result = $providerService->transferDomain($order);
        }

        if ($result['success']) {
            $order->update([
                'status' => 'active',
                'provider_order_id' => $result['domain_id'] ?? null,
                'provider_response' => $result,
            ]);

            // Send welcome email
            $this->sendWelcomeEmail($order, $result);
        } else {
            $order->update([
                'status' => 'pending',
                'admin_notes' => 'Domain service failed: ' . $result['message'],
            ]);
        }
    }

    /**
     * Process payment
     */
    private function processPayment(Order $order, Request $request)
    {
        switch ($request->payment_method) {
            case 'credit_card':
                return $this->processCreditCardPayment($order, $request);
            case 'bank_transfer':
                return $this->processBankTransfer($order, $request);
            case 'paypal':
                return $this->processPayPalPayment($order, $request);
            default:
                return ['success' => false, 'message' => 'Unsupported payment method'];
        }
    }

    /**
     * Process credit card payment
     */
    private function processCreditCardPayment(Order $order, Request $request)
    {
        return [
            'success' => true,
            'transaction_id' => 'DOMAIN_TXN_' . Str::random(10),
            'message' => 'Payment processed successfully'
        ];
    }

    /**
     * Process bank transfer
     */
    private function processBankTransfer(Order $order, Request $request)
    {
        // Store order data in session for bank transfer form
        session(['pending_domain_order' => [
            'order_id' => $order->id,
            'order_number' => $order->order_number,
            'domain_name' => $order->domain_name,
            'total_amount' => $order->total_amount,
            'customer_name' => $order->customer_name,
            'customer_email' => $order->customer_email,
        ]]);

        return [
            'success' => true,
            'redirect_to_bank_form' => true,
            'transaction_id' => 'DOMAIN_BANK_' . $order->order_number,
            'message' => 'Redirecting to bank transfer form'
        ];
    }

    /**
     * Show bank transfer payment form for domains
     */
    public function showBankTransferForm(Request $request)
    {
        $orderData = session('pending_domain_order');

        if (!$orderData) {
            return redirect()->route('user.domains.store')->with('error', 'لا توجد بيانات طلب صالحة');
        }

        return view('user.domains.bank-transfer-payment', compact('orderData'));
    }

    /**
     * Process bank transfer payment for domains
     */
    public function processBankTransferPayment(Request $request)
    {
        $request->validate([
            'full_name' => 'required|string|max:255',
            'transfer_type' => 'required|in:تحويل,ايداع',
            'order_id' => 'required|string',
        ]);

        $orderData = session('pending_domain_order');

        if (!$orderData || $orderData['order_number'] !== $request->order_id) {
            return redirect()->route('user.domains.store')->with('error', 'بيانات الطلب غير صحيحة');
        }

        try {
            DB::beginTransaction();

            // Create bank transfer record
            $bankTransfer = BankTransfer::create([
                'order_id' => $orderData['order_number'],
                'full_name' => $request->full_name,
                'transfer_type' => $request->transfer_type,
                'amount' => $orderData['total_amount'],
                'service_type' => 'domain',
                'status' => 'pending',
                'reference_number' => 'DT_' . strtoupper(Str::random(8)),
                'created_at' => now(),
            ]);

            // Update order status
            $order = Order::find($orderData['order_id']);
            if ($order) {
                $order->update([
                    'payment_status' => 'pending',
                    'payment_method' => 'bank_transfer',
                    'admin_notes' => 'في انتظار تأكيد التحويل البنكي - ' . $request->transfer_type,
                ]);
            }

            DB::commit();

            // Store bank transfer data in session for confirmation page
            session([
                'bank_transfer_confirmation' => [
                    'reference_number' => $bankTransfer->reference_number,
                    'full_name' => $request->full_name,
                    'transfer_type' => $request->transfer_type,
                    'amount' => $orderData['total_amount'],
                    'domain_name' => $orderData['domain_name'],
                    'order_number' => $orderData['order_number'],
                ]
            ]);

            // Clear pending order from session
            session()->forget('pending_domain_order');

            return redirect()->route('domains.bank-transfer-confirmation')
                ->with('success', 'تم إرسال طلب التحويل البنكي بنجاح');

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Domain bank transfer error: ' . $e->getMessage());
            return back()->withErrors(['error' => 'حدث خطأ أثناء معالجة طلب التحويل البنكي']);
        }
    }

    /**
     * Show bank transfer confirmation page
     */
    public function showBankTransferConfirmation()
    {
        $confirmationData = session('bank_transfer_confirmation');

        if (!$confirmationData) {
            return redirect()->route('user.domains.store')->with('error', 'لا توجد بيانات تأكيد صالحة');
        }

        return view('user.domains.bank-transfer-confirmation', compact('confirmationData'));
    }

    /**
     * Process PayPal payment
     */
    private function processPayPalPayment(Order $order, Request $request)
    {
        return [
            'success' => true,
            'transaction_id' => 'DOMAIN_PP_' . Str::random(10),
            'message' => 'PayPal payment processed'
        ];
    }

    /**
     * Send welcome email
     */
    private function sendWelcomeEmail(Order $order, array $domainDetails)
    {
        Log::info('Domain welcome email sent', [
            'order_id' => $order->id,
            'customer_email' => $order->customer_email,
            'domain' => $order->domain_name
        ]);
    }

    /**
     * Generate unique order number
     */
    private function generateOrderNumber()
    {
        return 'DOMAIN' . date('Ymd') . Str::random(6);
    }

    /**
     * Sanitize domain name
     */
    private function sanitizeDomain($domain)
    {
        // Remove http/https, www, and any extensions
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        $domain = preg_replace('/^www\./', '', $domain);
        $domain = preg_replace('/\.[a-z]{2,}$/i', '', $domain);

        return strtolower(trim($domain));
    }

    /**
     * Get all available domains with categories
     */
    public function getAllDomains()
    {
        $domains = Domain::where('is_active', true)
            ->orderBy('tld_category')
            ->orderBy('sort_order')
            ->get()
            ->groupBy('tld_category');

        return view('domains.all', compact('domains'));
    }

    /**
     * WHOIS lookup
     */
    public function whoisLookup(Request $request)
    {
        $request->validate([
            'domain' => 'required|string|max:255'
        ]);

        $domain = $this->sanitizeDomain($request->domain);

        // Simulate WHOIS data (in real implementation, use actual WHOIS service)
        $whoisData = [
            'domain' => $domain,
            'status' => 'registered',
            'registrar' => 'Example Registrar',
            'creation_date' => '2020-01-01',
            'expiry_date' => '2025-01-01',
            'nameservers' => [
                'ns1.example.com',
                'ns2.example.com'
            ]
        ];

        if ($request->ajax()) {
            return response()->json([
                'success' => true,
                'data' => $whoisData
            ]);
        }

        return view('domains.whois', compact('whoisData', 'domain'));
    }

    /**
     * Domain transfer check
     */
    public function transferCheck(Request $request)
    {
        $request->validate([
            'domain' => 'required|string|max:255',
            'auth_code' => 'required|string'
        ]);

        $domain = $request->domain;
        $authCode = $request->auth_code;

        // Simulate transfer eligibility check
        $transferEligible = strlen($authCode) >= 8; // Simple validation

        return response()->json([
            'success' => true,
            'eligible' => $transferEligible,
            'domain' => $domain,
            'message' => $transferEligible
                ? 'الدومين مؤهل للنقل'
                : 'كود التفويض غير صحيح أو الدومين غير مؤهل للنقل'
        ]);
    }
}
