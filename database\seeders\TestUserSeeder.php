<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // حذف المستخدمين الموجودين لتجنب التكرار
        User::where('email', '<EMAIL>')->delete();
        User::where('email', '<EMAIL>')->delete();
        User::where('email', '<EMAIL>')->delete();

        // إنشاء مستخدم تجريبي
        User::create([
            'name' => 'مستخدم تجريبي',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'email_verified_at' => now(),
        ]);

        // إنشاء مستخدم آخر
        User::create([
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'email_verified_at' => now(),
        ]);

        // إنشاء مستخدم بسيط للاختبار
        User::create([
            'name' => 'User Test',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        echo "تم إنشاء المستخدمين التجريبيين بنجاح:\n";
        echo "1. <EMAIL> - كلمة المرور: 123456\n";
        echo "2. <EMAIL> - كلمة المرور: 123456\n";
        echo "3. <EMAIL> - كلمة المرور: password\n";
    }
}
