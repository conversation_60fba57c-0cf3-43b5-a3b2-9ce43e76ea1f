<?php
// Demo page for admin dashboard
?>
<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>لوحة التحكم الإدارية - عرض تجريبي</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .main-wrapper {
            background: white;
            min-height: 100vh;
            margin: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
            min-height: 100vh;
            width: 280px;
            position: fixed;
            right: 0;
            top: 0;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .sidebar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem;
            border-radius: 8px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            margin: 0.25rem 1rem;
        }
        
        .nav-link:hover,
        .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }
        
        .nav-link i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
        }
        
        .topbar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e5e7eb;
            margin: -2rem -2rem 2rem -2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
        }
        
        .stat-card.danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(102, 126, 234, 0.3);
        }
        
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
            }
            .main-content {
                margin-right: 0;
            }
            .main-wrapper {
                margin: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="main-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <a href="#" class="sidebar-brand">
                    <i class="fas fa-crown me-2"></i>
                    لوحة التحكم
                </a>
            </div>
            
            <div class="sidebar-nav">
                <a href="#dashboard" class="nav-link active" onclick="showSection('dashboard')">
                    <i class="fas fa-tachometer-alt"></i>
                    الرئيسية
                </a>
                <a href="#settings" class="nav-link" onclick="showSection('settings')">
                    <i class="fas fa-cog"></i>
                    إعدادات النظام
                </a>
                <a href="#reports" class="nav-link" onclick="showSection('reports')">
                    <i class="fas fa-chart-bar"></i>
                    التقارير والإحصائيات
                </a>
                <a href="#files" class="nav-link" onclick="showSection('files')">
                    <i class="fas fa-folder"></i>
                    إدارة الملفات
                </a>
                <a href="#activity" class="nav-link" onclick="showSection('activity')">
                    <i class="fas fa-history"></i>
                    سجل النشاط
                </a>
                <a href="#backups" class="nav-link" onclick="showSection('backups')">
                    <i class="fas fa-database"></i>
                    النسخ الاحتياطية
                </a>
                <a href="#notifications" class="nav-link" onclick="showSection('notifications')">
                    <i class="fas fa-bell"></i>
                    إدارة الإشعارات
                </a>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <div class="topbar">
                <h1 id="pageTitle">لوحة التحكم الرئيسية</h1>
                <div>
                    <button class="btn btn-primary btn-sm">
                        <i class="fas fa-user me-2"></i>
                        المدير
                    </button>
                </div>
            </div>
            
            <!-- Dashboard Section -->
            <div id="dashboard" class="content-section">
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">إجمالي المستخدمين</h6>
                                        <h3 class="mb-0">1,247</h3>
                                    </div>
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">المبيعات اليوم</h6>
                                        <h3 class="mb-0">$12,450</h3>
                                    </div>
                                    <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">الطلبات المعلقة</h6>
                                        <h3 class="mb-0">23</h3>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card danger">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">التنبيهات</h6>
                                        <h3 class="mb-0">5</h3>
                                    </div>
                                    <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts -->
                <div class="row">
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">إحصائيات المبيعات</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">توزيع المستخدمين</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="usersChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Other Sections (Hidden by default) -->
            <div id="settings" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">إعدادات النظام</h5>
                    </div>
                    <div class="card-body">
                        <p>هنا يمكنك إدارة جميع إعدادات النظام...</p>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم الموقع</label>
                                    <input type="text" class="form-control" value="موقعي الرائع">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" value="<EMAIL>">
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary">حفظ الإعدادات</button>
                    </div>
                </div>
            </div>
            
            <div id="reports" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">التقارير والإحصائيات</h5>
                    </div>
                    <div class="card-body">
                        <p>تقارير مفصلة عن أداء النظام...</p>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-chart-line fa-3x text-primary mb-3"></i>
                                        <h6>تقرير المبيعات</h6>
                                        <button class="btn btn-sm btn-primary">عرض</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-users fa-3x text-success mb-3"></i>
                                        <h6>تقرير المستخدمين</h6>
                                        <button class="btn btn-sm btn-success">عرض</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card bg-light">
                                    <div class="card-body text-center">
                                        <i class="fas fa-cog fa-3x text-warning mb-3"></i>
                                        <h6>تقرير النظام</h6>
                                        <button class="btn btn-sm btn-warning">عرض</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Add more sections as needed -->
            <div id="files" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">إدارة الملفات</h5>
                    </div>
                    <div class="card-body">
                        <p>إدارة وتنظيم ملفات النظام...</p>
                        <button class="btn btn-success">
                            <i class="fas fa-upload me-2"></i>
                            رفع ملف جديد
                        </button>
                    </div>
                </div>
            </div>
            
            <div id="activity" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">سجل النشاط</h5>
                    </div>
                    <div class="card-body">
                        <p>تتبع جميع أنشطة النظام...</p>
                        <div class="list-group">
                            <div class="list-group-item">
                                <i class="fas fa-user text-primary me-2"></i>
                                تسجيل دخول مستخدم جديد
                                <small class="text-muted float-end">منذ 5 دقائق</small>
                            </div>
                            <div class="list-group-item">
                                <i class="fas fa-edit text-warning me-2"></i>
                                تعديل إعدادات النظام
                                <small class="text-muted float-end">منذ 15 دقيقة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div id="backups" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">النسخ الاحتياطية</h5>
                    </div>
                    <div class="card-body">
                        <p>إدارة النسخ الاحتياطية للنظام...</p>
                        <button class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>
                            إنشاء نسخة احتياطية
                        </button>
                    </div>
                </div>
            </div>
            
            <div id="notifications" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">إدارة الإشعارات</h5>
                    </div>
                    <div class="card-body">
                        <p>إرسال وإدارة الإشعارات...</p>
                        <button class="btn btn-success">
                            <i class="fas fa-bell me-2"></i>
                            إرسال إشعار جديد
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });
            
            // Remove active class from all nav links
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // Show selected section
            document.getElementById(sectionId).style.display = 'block';
            
            // Add active class to clicked nav link
            event.target.classList.add('active');
            
            // Update page title
            const titles = {
                'dashboard': 'لوحة التحكم الرئيسية',
                'settings': 'إعدادات النظام',
                'reports': 'التقارير والإحصائيات',
                'files': 'إدارة الملفات',
                'activity': 'سجل النشاط',
                'backups': 'النسخ الاحتياطية',
                'notifications': 'إدارة الإشعارات'
            };
            document.getElementById('pageTitle').textContent = titles[sectionId];
        }
        
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            // Sales Chart
            const salesCtx = document.getElementById('salesChart').getContext('2d');
            new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'المبيعات',
                        data: [12000, 19000, 15000, 25000, 22000, 30000],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
            
            // Users Chart
            const usersCtx = document.getElementById('usersChart').getContext('2d');
            new Chart(usersCtx, {
                type: 'doughnut',
                data: {
                    labels: ['مستخدمين نشطين', 'مستخدمين غير نشطين'],
                    datasets: [{
                        data: [847, 400],
                        backgroundColor: ['#10b981', '#ef4444']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        });
    </script>
</body>
</html>
