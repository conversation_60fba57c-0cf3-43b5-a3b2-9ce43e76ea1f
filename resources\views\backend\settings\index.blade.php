@extends('backend.layouts.app')

@section('title', 'إعدادات النظام')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-cogs me-2 text-primary"></i>
                        إعدادات النظام
                    </h1>
                    <p class="text-muted mb-0">إدارة وتخصيص إعدادات النظام العامة</p>
                </div>
                <div>
                    <button class="btn btn-success" onclick="saveAllSettings()">
                        <i class="fas fa-save me-2"></i>
                        حفظ جميع الإعدادات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Settings Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <ul class="nav nav-tabs card-header-tabs" id="settingsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="general-tab" data-bs-toggle="tab" data-bs-target="#general" type="button" role="tab">
                                <i class="fas fa-cog me-2"></i>
                                الإعدادات العامة
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="email-tab" data-bs-toggle="tab" data-bs-target="#email" type="button" role="tab">
                                <i class="fas fa-envelope me-2"></i>
                                إعدادات البريد الإلكتروني
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="security-tab" data-bs-toggle="tab" data-bs-target="#security" type="button" role="tab">
                                <i class="fas fa-shield-alt me-2"></i>
                                الأمان والحماية
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="appearance-tab" data-bs-toggle="tab" data-bs-target="#appearance" type="button" role="tab">
                                <i class="fas fa-palette me-2"></i>
                                المظهر والتخصيص
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="advanced-tab" data-bs-toggle="tab" data-bs-target="#advanced" type="button" role="tab">
                                <i class="fas fa-tools me-2"></i>
                                الإعدادات المتقدمة
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="card-body">
                    <div class="tab-content" id="settingsTabContent">
                        <!-- General Settings -->
                        <div class="tab-pane fade show active" id="general" role="tabpanel">
                            <form id="generalSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم الموقع</label>
                                            <input type="text" class="form-control" name="site_name" value="{{ config('app.name') }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">وصف الموقع</label>
                                            <input type="text" class="form-control" name="site_description" value="موقع إلكتروني متقدم">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني للإدارة</label>
                                            <input type="email" class="form-control" name="admin_email" value="<EMAIL>">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" name="phone" value="+966123456789">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">المنطقة الزمنية</label>
                                            <select class="form-select" name="timezone">
                                                <option value="Asia/Riyadh" selected>آسيا/الرياض</option>
                                                <option value="Asia/Dubai">آسيا/دبي</option>
                                                <option value="Asia/Kuwait">آسيا/الكويت</option>
                                                <option value="UTC">UTC</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اللغة الافتراضية</label>
                                            <select class="form-select" name="default_language">
                                                <option value="ar" selected>العربية</option>
                                                <option value="en">English</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">عنوان الشركة</label>
                                            <textarea class="form-control" name="address" rows="3">الرياض، المملكة العربية السعودية</textarea>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Email Settings -->
                        <div class="tab-pane fade" id="email" role="tabpanel">
                            <form id="emailSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">خادم البريد الإلكتروني</label>
                                            <select class="form-select" name="mail_driver">
                                                <option value="smtp">SMTP</option>
                                                <option value="mailgun">Mailgun</option>
                                                <option value="ses">Amazon SES</option>
                                                <option value="sendmail">Sendmail</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">خادم SMTP</label>
                                            <input type="text" class="form-control" name="mail_host" value="smtp.gmail.com">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">منفذ SMTP</label>
                                            <input type="number" class="form-control" name="mail_port" value="587">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">التشفير</label>
                                            <select class="form-select" name="mail_encryption">
                                                <option value="tls">TLS</option>
                                                <option value="ssl">SSL</option>
                                                <option value="">بدون تشفير</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المستخدم</label>
                                            <input type="text" class="form-control" name="mail_username">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">كلمة المرور</label>
                                            <input type="password" class="form-control" name="mail_password">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني للمرسل</label>
                                            <input type="email" class="form-control" name="mail_from_address">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">اسم المرسل</label>
                                            <input type="text" class="form-control" name="mail_from_name">
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="alert alert-info">
                                            <i class="fas fa-info-circle me-2"></i>
                                            يمكنك اختبار إعدادات البريد الإلكتروني بإرسال رسالة تجريبية.
                                        </div>
                                        <button type="button" class="btn btn-outline-primary" onclick="testEmailSettings()">
                                            <i class="fas fa-paper-plane me-2"></i>
                                            إرسال رسالة تجريبية
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Security Settings -->
                        <div class="tab-pane fade" id="security" role="tabpanel">
                            <form id="securitySettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="two_factor_enabled" id="twoFactorEnabled">
                                                <label class="form-check-label" for="twoFactorEnabled">
                                                    تفعيل المصادقة الثنائية
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="force_https" id="forceHttps">
                                                <label class="form-check-label" for="forceHttps">
                                                    إجبار استخدام HTTPS
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">مدة انتهاء الجلسة (بالدقائق)</label>
                                            <input type="number" class="form-control" name="session_lifetime" value="120">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">عدد محاولات تسجيل الدخول المسموحة</label>
                                            <input type="number" class="form-control" name="login_attempts" value="5">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">مدة الحظر بعد المحاولات الفاشلة (بالدقائق)</label>
                                            <input type="number" class="form-control" name="lockout_duration" value="15">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="password_reset_enabled" id="passwordResetEnabled" checked>
                                                <label class="form-check-label" for="passwordResetEnabled">
                                                    السماح بإعادة تعيين كلمة المرور
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">عناوين IP المحظورة (واحد في كل سطر)</label>
                                            <textarea class="form-control" name="blocked_ips" rows="4" placeholder="***********&#10;********"></textarea>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Appearance Settings -->
                        <div class="tab-pane fade" id="appearance" role="tabpanel">
                            <form id="appearanceSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">لون النظام الأساسي</label>
                                            <input type="color" class="form-control form-control-color" name="primary_color" value="#667eea">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">لون النظام الثانوي</label>
                                            <input type="color" class="form-control form-control-color" name="secondary_color" value="#764ba2">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">نمط الشريط الجانبي</label>
                                            <select class="form-select" name="sidebar_style">
                                                <option value="light">فاتح</option>
                                                <option value="dark" selected>داكن</option>
                                                <option value="colored">ملون</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="dark_mode" id="darkMode">
                                                <label class="form-check-label" for="darkMode">
                                                    الوضع الليلي
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="rtl_enabled" id="rtlEnabled" checked>
                                                <label class="form-check-label" for="rtlEnabled">
                                                    تفعيل الكتابة من اليمين لليسار
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">خط النظام</label>
                                            <select class="form-select" name="system_font">
                                                <option value="Cairo" selected>Cairo</option>
                                                <option value="Tajawal">Tajawal</option>
                                                <option value="Amiri">Amiri</option>
                                                <option value="Noto Sans Arabic">Noto Sans Arabic</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="mb-3">
                                            <label class="form-label">شعار الموقع</label>
                                            <input type="file" class="form-control" name="site_logo" accept="image/*">
                                            <div class="form-text">الحد الأقصى: 2MB، الصيغ المدعومة: JPG, PNG, SVG</div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Advanced Settings -->
                        <div class="tab-pane fade" id="advanced" role="tabpanel">
                            <form id="advancedSettingsForm">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="debug_mode" id="debugMode">
                                                <label class="form-check-label" for="debugMode">
                                                    وضع التطوير (Debug)
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="maintenance_mode" id="maintenanceMode">
                                                <label class="form-check-label" for="maintenanceMode">
                                                    وضع الصيانة
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">مستوى التسجيل</label>
                                            <select class="form-select" name="log_level">
                                                <option value="debug">Debug</option>
                                                <option value="info" selected>Info</option>
                                                <option value="warning">Warning</option>
                                                <option value="error">Error</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">مدة الاحتفاظ بالسجلات (بالأيام)</label>
                                            <input type="number" class="form-control" name="log_retention_days" value="30">
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" name="cache_enabled" id="cacheEnabled" checked>
                                                <label class="form-check-label" for="cacheEnabled">
                                                    تفعيل التخزين المؤقت
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">مدة التخزين المؤقت (بالدقائق)</label>
                                            <input type="number" class="form-control" name="cache_duration" value="60">
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="alert alert-warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            <strong>تحذير:</strong> تغيير هذه الإعدادات قد يؤثر على أداء النظام. يرجى التأكد من فهم تأثير كل إعداد قبل التغيير.
                                        </div>
                                    </div>
                                    <div class="col-12">
                                        <div class="d-flex gap-2">
                                            <button type="button" class="btn btn-outline-danger" onclick="clearAllCache()">
                                                <i class="fas fa-broom me-2"></i>
                                                مسح جميع الذاكرة المؤقتة
                                            </button>
                                            <button type="button" class="btn btn-outline-warning" onclick="clearLogs()">
                                                <i class="fas fa-trash me-2"></i>
                                                مسح السجلات
                                            </button>
                                            <button type="button" class="btn btn-outline-info" onclick="optimizeDatabase()">
                                                <i class="fas fa-database me-2"></i>
                                                تحسين قاعدة البيانات
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('after-styles')
<style>
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: #6c757d;
    font-weight: 500;
    padding: 1rem 1.5rem;
    margin-bottom: -2px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

.nav-tabs .nav-link.active {
    background: #667eea;
    color: white;
    border-bottom: 2px solid #667eea;
}

.tab-content {
    padding: 2rem 0;
}

.form-label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.form-control, .form-select {
    border-radius: 8px;
    border: 1px solid #d1d5db;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.form-check-input:focus {
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-control-color {
    width: 60px;
    height: 45px;
    border-radius: 8px;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.alert {
    border-radius: 8px;
    border: none;
    padding: 1rem 1.5rem;
}

.alert-info {
    background: rgba(13, 202, 240, 0.1);
    color: #055160;
    border-left: 4px solid #0dcaf0;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #664d03;
    border-left: 4px solid #ffc107;
}

.card {
    border-radius: 12px;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.card-header {
    border-radius: 12px 12px 0 0;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.settings-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.settings-section h6 {
    color: #667eea;
    font-weight: 600;
    margin-bottom: 1rem;
}

@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
    }

    .nav-tabs .nav-link {
        text-align: center;
        margin-bottom: 0.5rem;
        border-radius: 8px;
    }

    .tab-content {
        padding: 1rem 0;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success Animation */
.success-animation {
    animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>
@endpush

@push('after-scripts')
<script>
function saveAllSettings() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    // Show loading state
    btn.classList.add('loading');
    btn.disabled = true;

    // Collect all form data
    const allSettings = {};

    // Get data from all forms
    const forms = ['generalSettingsForm', 'emailSettingsForm', 'securitySettingsForm', 'appearanceSettingsForm', 'advancedSettingsForm'];

    forms.forEach(formId => {
        const form = document.getElementById(formId);
        if (form) {
            const formData = new FormData(form);
            for (let [key, value] of formData.entries()) {
                allSettings[key] = value;
            }
        }
    });

    // Include checkbox values (they're not included if unchecked)
    const checkboxes = document.querySelectorAll('input[type="checkbox"]');
    checkboxes.forEach(checkbox => {
        allSettings[checkbox.name] = checkbox.checked;
    });

    // Send to server
    fetch('{{ route("admin.settings.update") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(allSettings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success animation
            btn.classList.add('success-animation');

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم حفظ جميع الإعدادات بنجاح');
            } else {
                alert('تم حفظ جميع الإعدادات بنجاح');
            }

            // Update any dynamic elements if needed
            updateUIWithNewSettings(allSettings);
        } else {
            throw new Error(data.message || 'حدث خطأ أثناء حفظ الإعدادات');
        }
    })
    .catch(error => {
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.error(error.message);
        } else {
            alert(error.message);
        }
    })
    .finally(() => {
        btn.classList.remove('loading');
        btn.disabled = false;

        setTimeout(() => {
            btn.classList.remove('success-animation');
        }, 600);
    });
}

function testEmailSettings() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
    btn.disabled = true;

    // Get email settings
    const form = document.getElementById('emailSettingsForm');
    const formData = new FormData(form);
    const emailSettings = {};

    for (let [key, value] of formData.entries()) {
        emailSettings[key] = value;
    }

    fetch('{{ route("admin.settings.test-email") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify(emailSettings)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم إرسال الرسالة التجريبية بنجاح');
            } else {
                alert('تم إرسال الرسالة التجريبية بنجاح');
            }
        } else {
            throw new Error(data.message || 'فشل في إرسال الرسالة التجريبية');
        }
    })
    .catch(error => {
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.error(error.message);
        } else {
            alert(error.message);
        }
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function clearAllCache() {
    if (!confirm('هل أنت متأكد من مسح جميع الذاكرة المؤقتة؟')) {
        return;
    }

    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المسح...';
    btn.disabled = true;

    fetch('{{ route("admin.cache.clear-all") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم مسح جميع الذاكرة المؤقتة بنجاح');
            } else {
                alert('تم مسح جميع الذاكرة المؤقتة بنجاح');
            }
        } else {
            throw new Error(data.message || 'حدث خطأ أثناء مسح الذاكرة المؤقتة');
        }
    })
    .catch(error => {
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.error(error.message);
        } else {
            alert(error.message);
        }
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function clearLogs() {
    if (!confirm('هل أنت متأكد من مسح جميع السجلات؟')) {
        return;
    }

    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري المسح...';
    btn.disabled = true;

    fetch('{{ route("admin.logs.clear") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم مسح السجلات بنجاح');
            } else {
                alert('تم مسح السجلات بنجاح');
            }
        } else {
            throw new Error(data.message || 'حدث خطأ أثناء مسح السجلات');
        }
    })
    .catch(error => {
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.error(error.message);
        } else {
            alert(error.message);
        }
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function optimizeDatabase() {
    if (!confirm('هل أنت متأكد من تحسين قاعدة البيانات؟ قد تستغرق هذه العملية بعض الوقت.')) {
        return;
    }

    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحسين...';
    btn.disabled = true;

    fetch('{{ route("admin.database.optimize") }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم تحسين قاعدة البيانات بنجاح');
            } else {
                alert('تم تحسين قاعدة البيانات بنجاح');
            }
        } else {
            throw new Error(data.message || 'حدث خطأ أثناء تحسين قاعدة البيانات');
        }
    })
    .catch(error => {
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.error(error.message);
        } else {
            alert(error.message);
        }
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function updateUIWithNewSettings(settings) {
    // Update primary color if changed
    if (settings.primary_color) {
        document.documentElement.style.setProperty('--primary-color', settings.primary_color);
    }

    // Update secondary color if changed
    if (settings.secondary_color) {
        document.documentElement.style.setProperty('--secondary-color', settings.secondary_color);
    }

    // Update RTL if changed
    if (settings.rtl_enabled !== undefined) {
        document.documentElement.dir = settings.rtl_enabled ? 'rtl' : 'ltr';
    }

    // Update dark mode if changed
    if (settings.dark_mode !== undefined) {
        document.body.classList.toggle('dark-mode', settings.dark_mode);
    }
}

// Auto-save functionality
let autoSaveTimeout;

function setupAutoSave() {
    const inputs = document.querySelectorAll('input, select, textarea');

    inputs.forEach(input => {
        input.addEventListener('change', () => {
            clearTimeout(autoSaveTimeout);
            autoSaveTimeout = setTimeout(() => {
                autoSaveSettings();
            }, 2000); // Auto-save after 2 seconds of inactivity
        });
    });
}

function autoSaveSettings() {
    // Show subtle indication of auto-save
    const indicator = document.createElement('div');
    indicator.className = 'position-fixed top-0 end-0 m-3 alert alert-info alert-dismissible fade show';
    indicator.innerHTML = '<i class="fas fa-save me-2"></i>تم الحفظ التلقائي';
    indicator.style.zIndex = '9999';

    document.body.appendChild(indicator);

    // Remove after 3 seconds
    setTimeout(() => {
        indicator.remove();
    }, 3000);
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Setup auto-save
    setupAutoSave();

    // Load saved settings from localStorage for preview
    loadPreviewSettings();

    // Setup color picker previews
    setupColorPickers();
});

function loadPreviewSettings() {
    const savedSettings = localStorage.getItem('admin_settings_preview');
    if (savedSettings) {
        const settings = JSON.parse(savedSettings);
        updateUIWithNewSettings(settings);
    }
}

function setupColorPickers() {
    const colorInputs = document.querySelectorAll('input[type="color"]');

    colorInputs.forEach(input => {
        input.addEventListener('input', (e) => {
            // Live preview of color changes
            const property = e.target.name === 'primary_color' ? '--primary-color' : '--secondary-color';
            document.documentElement.style.setProperty(property, e.target.value);

            // Save to localStorage for preview
            const currentSettings = JSON.parse(localStorage.getItem('admin_settings_preview') || '{}');
            currentSettings[e.target.name] = e.target.value;
            localStorage.setItem('admin_settings_preview', JSON.stringify(currentSettings));
        });
    });
}
</script>
@endpush
