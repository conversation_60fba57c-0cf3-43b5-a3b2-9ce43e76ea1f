// ميتاء تك - لوحة التحكم الحديثة - JavaScript

class MetaDashboard {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupMobileMenu();
        this.setupSearch();
        this.setupNotifications();
        this.initializeAnimations();
    }

    setupEventListeners() {
        // أزرار الشريط العلوي
        document.querySelectorAll('.header-btn').forEach(btn => {
            btn.addEventListener('click', this.handleHeaderButtonClick.bind(this));
        });

        // زر التصفية
        const filterBtn = document.querySelector('.filter-btn');
        if (filterBtn) {
            filterBtn.addEventListener('click', this.handleFilterClick.bind(this));
        }

        // البحث
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', this.handleSearch.bind(this));
            searchInput.addEventListener('focus', this.handleSearchFocus.bind(this));
            searchInput.addEventListener('blur', this.handleSearchBlur.bind(this));
        }

        // عناصر القائمة الجانبية
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('mouseenter', this.handleMenuItemHover.bind(this));
            item.addEventListener('mouseleave', this.handleMenuItemLeave.bind(this));
        });
    }

    setupMobileMenu() {
        // إنشاء زر القائمة للموبايل
        const mobileMenuBtn = document.createElement('button');
        mobileMenuBtn.className = 'mobile-menu-btn';
        mobileMenuBtn.innerHTML = '<i class="fas fa-bars"></i>';
        mobileMenuBtn.style.display = 'none';
        
        const topHeader = document.querySelector('.top-header');
        if (topHeader) {
            topHeader.insertBefore(mobileMenuBtn, topHeader.firstChild);
        }

        // التحكم في القائمة الجانبية للموبايل
        mobileMenuBtn.addEventListener('click', () => {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        });

        // إغلاق القائمة عند النقر خارجها
        document.addEventListener('click', (e) => {
            const sidebar = document.querySelector('.sidebar');
            const mobileBtn = document.querySelector('.mobile-menu-btn');
            
            if (!sidebar.contains(e.target) && !mobileBtn.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        });

        // إظهار/إخفاء زر القائمة حسب حجم الشاشة
        this.checkScreenSize();
        window.addEventListener('resize', this.checkScreenSize.bind(this));
    }

    checkScreenSize() {
        const mobileBtn = document.querySelector('.mobile-menu-btn');
        if (window.innerWidth <= 1024) {
            mobileBtn.style.display = 'block';
        } else {
            mobileBtn.style.display = 'none';
            document.querySelector('.sidebar').classList.remove('open');
        }
    }

    setupSearch() {
        // إعداد البحث المتقدم
        this.searchResults = [];
        this.searchTimeout = null;
    }

    setupNotifications() {
        // إعداد الإشعارات
        this.notifications = [];
        this.updateNotificationBadges();
    }

    initializeAnimations() {
        // تفعيل الرسوم المتحركة
        this.observeElements();
    }

    observeElements() {
        // مراقبة العناصر للرسوم المتحركة
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, {
            threshold: 0.1
        });

        // مراقبة البطاقات
        document.querySelectorAll('.stat-card, .chart-card, .balance-card').forEach(card => {
            observer.observe(card);
        });
    }

    handleHeaderButtonClick(event) {
        const btn = event.currentTarget;
        const icon = btn.querySelector('i');
        
        // تأثير النقر
        btn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            btn.style.transform = 'scale(1)';
        }, 150);

        // معالجة أنواع الأزرار المختلفة
        if (icon.classList.contains('fa-bell')) {
            this.showNotifications();
        } else if (icon.classList.contains('fa-envelope')) {
            this.showMessages();
        } else if (icon.classList.contains('fa-globe')) {
            this.showLanguageMenu();
        } else if (icon.classList.contains('fa-user')) {
            this.showUserMenu();
        }
    }

    handleFilterClick(event) {
        // معالجة زر التصفية
        console.log('Filter clicked');
        // يمكن إضافة منطق التصفية هنا
    }

    handleSearch(event) {
        const query = event.target.value;
        
        // إلغاء البحث السابق
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // بحث جديد بعد تأخير قصير
        this.searchTimeout = setTimeout(() => {
            this.performSearch(query);
        }, 300);
    }

    handleSearchFocus(event) {
        event.target.parentElement.classList.add('focused');
    }

    handleSearchBlur(event) {
        event.target.parentElement.classList.remove('focused');
    }

    handleMenuItemHover(event) {
        const item = event.currentTarget;
        if (!item.classList.contains('active')) {
            item.style.transform = 'translateX(-3px)';
        }
    }

    handleMenuItemLeave(event) {
        const item = event.currentTarget;
        if (!item.classList.contains('active')) {
            item.style.transform = 'translateX(0)';
        }
    }

    performSearch(query) {
        if (query.length < 2) {
            this.hideSearchResults();
            return;
        }

        // محاكاة البحث
        console.log('Searching for:', query);
        // يمكن إضافة منطق البحث الحقيقي هنا
    }

    hideSearchResults() {
        // إخفاء نتائج البحث
        const resultsContainer = document.querySelector('.search-results');
        if (resultsContainer) {
            resultsContainer.style.display = 'none';
        }
    }

    showNotifications() {
        console.log('Showing notifications');
        // يمكن إضافة منطق عرض الإشعارات هنا
    }

    showMessages() {
        console.log('Showing messages');
        // يمكن إضافة منطق عرض الرسائل هنا
    }

    showLanguageMenu() {
        console.log('Showing language menu');
        // يمكن إضافة منطق تغيير اللغة هنا
    }

    showUserMenu() {
        console.log('Showing user menu');
        // يمكن إضافة منطق قائمة المستخدم هنا
    }

    updateNotificationBadges() {
        // تحديث شارات الإشعارات
        const badges = document.querySelectorAll('.notification-badge');
        badges.forEach(badge => {
            // يمكن تحديث الأرقام من API
        });
    }

    // دوال مساعدة للرسوم البيانية
    createChart(canvasId, type, data, options = {}) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        return new Chart(ctx, {
            type: type,
            data: data,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                ...options
            }
        });
    }

    // تحديث البيانات في الوقت الفعلي
    startRealTimeUpdates() {
        setInterval(() => {
            this.updateDashboardData();
        }, 30000); // كل 30 ثانية
    }

    updateDashboardData() {
        // تحديث بيانات لوحة التحكم
        console.log('Updating dashboard data...');
        // يمكن إضافة منطق تحديث البيانات من API هنا
    }

    // تأثيرات بصرية
    addRippleEffect(element, event) {
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.width = ripple.style.height = size + 'px';
        ripple.style.left = x + 'px';
        ripple.style.top = y + 'px';
        ripple.classList.add('ripple');
        
        element.appendChild(ripple);
        
        setTimeout(() => {
            ripple.remove();
        }, 600);
    }
}

// تشغيل لوحة التحكم عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.metaDashboard = new MetaDashboard();
});

// إضافة CSS للتأثيرات
const style = document.createElement('style');
style.textContent = `
    .mobile-menu-btn {
        width: 44px;
        height: 44px;
        border-radius: 12px;
        background: rgba(255, 255, 255, 0.8);
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--gray-600);
        cursor: pointer;
        transition: all 0.3s ease;
        margin-left: 16px;
    }

    .mobile-menu-btn:hover {
        background: white;
        color: var(--primary-blue);
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }

    .search-container.focused {
        transform: scale(1.02);
    }

    .animate-in {
        animation: slideInUp 0.6s ease-out;
    }

    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .ripple {
        position: absolute;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.6);
        transform: scale(0);
        animation: ripple-animation 0.6s linear;
        pointer-events: none;
    }

    @keyframes ripple-animation {
        to {
            transform: scale(4);
            opacity: 0;
        }
    }
`;
document.head.appendChild(style);
