<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Database\Eloquent\Relations\HasMany;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'phone',
        'address',
        'google_id',
        'avatar',
        'email_verified_at',
        'firebase_uid',
        'provider',
        'facebook_id',
        'github_id',
        'bio',
        'company',
        'website',
        'location',
        'email_notifications',
        'sms_notifications',
        'marketing_emails',
        'order_updates',
        'last_login_at',
        'birth_date',
        'gender',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'last_login_at' => 'datetime',
            'birth_date' => 'date',
            'password' => 'hashed',
            'email_notifications' => 'boolean',
            'sms_notifications' => 'boolean',
            'marketing_emails' => 'boolean',
            'order_updates' => 'boolean',
        ];
    }

    /**
     * Get the service orders for the user.
     */
    public function serviceOrders(): HasMany
    {
        return $this->hasMany(\App\Models\ServiceOrder::class);
    }

    /**
     * Get the user's domains.
     */
    public function userDomains(): HasMany
    {
        return $this->hasMany(\App\Models\UserDomain::class);
    }

    /**
     * Get the user's avatar URL.
     */
    public function getAvatarUrlAttribute()
    {
        if ($this->avatar) {
            return asset('storage/' . $this->avatar);
        }

        return 'https://ui-avatars.com/api/?name=' . urlencode($this->name) . '&color=7F9CF5&background=EBF4FF';
    }

    /**
     * Get the user's full name with company.
     */
    public function getDisplayNameAttribute()
    {
        return $this->company ? $this->name . ' - ' . $this->company : $this->name;
    }
}
