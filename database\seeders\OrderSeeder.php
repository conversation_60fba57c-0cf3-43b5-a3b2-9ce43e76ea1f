<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\ServiceOrder;
use App\Models\User;

class OrderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $users = User::all();
        
        if ($users->count() > 0) {
            // طلب للمستخدم الأول
            ServiceOrder::create([
                'user_id' => $users->first()->id,
                'service_type' => 'web_development',
                'service_name' => 'تطوير موقع إلكتروني للشركة',
                'description' => 'موقع إلكتروني متكامل مع لوحة تحكم إدارية وتصميم متجاوب',
                'budget' => 1200,
                'deadline' => now()->addDays(30),
                'requirements' => 'تصميم حديث، متجاوب، لوحة تحكم، نظام إدارة المحتوى',
                'contact_method' => 'email',
                'status' => 'in_progress',
                'progress' => 65,
                'created_at' => now()->subDays(15),
                'updated_at' => now()->subDays(2),
            ]);

            // طلب مكتمل للمستخدم الثاني
            if ($users->count() > 1) {
                ServiceOrder::create([
                    'user_id' => $users->skip(1)->first()->id,
                    'service_type' => 'graphic_design',
                    'service_name' => 'تصميم شعار وهوية بصرية',
                    'description' => 'تصميم شعار احترافي مع دليل الهوية البصرية الكامل',
                    'budget' => 300,
                    'deadline' => now()->subDays(5),
                    'requirements' => 'شعار حديث، ألوان جذابة، ملفات عالية الجودة',
                    'contact_method' => 'whatsapp',
                    'status' => 'completed',
                    'progress' => 100,
                    'rating' => 5,
                    'review' => 'خدمة ممتازة وتصميم رائع، أنصح بالتعامل معهم',
                    'rated_at' => now()->subDays(3),
                    'created_at' => now()->subDays(20),
                    'updated_at' => now()->subDays(3),
                ]);
            }

            // طلب في الانتظار للمستخدم الثالث
            if ($users->count() > 2) {
                ServiceOrder::create([
                    'user_id' => $users->skip(2)->first()->id,
                    'service_type' => 'mobile_app',
                    'service_name' => 'تطوير تطبيق جوال',
                    'description' => 'تطبيق iOS و Android للتجارة الإلكترونية',
                    'budget' => 2500,
                    'deadline' => now()->addDays(45),
                    'requirements' => 'تطبيق متجر إلكتروني، نظام دفع، إشعارات',
                    'contact_method' => 'phone',
                    'status' => 'pending',
                    'progress' => 0,
                    'created_at' => now()->subDays(3),
                    'updated_at' => now()->subDays(3),
                ]);
            }

            // طلب ملغي للمستخدم الرابع
            if ($users->count() > 3) {
                ServiceOrder::create([
                    'user_id' => $users->skip(3)->first()->id,
                    'service_type' => 'digital_marketing',
                    'service_name' => 'حملة تسويق رقمي',
                    'description' => 'إدارة وسائل التواصل الاجتماعي وحملات إعلانية',
                    'budget' => 800,
                    'deadline' => now()->addDays(60),
                    'requirements' => 'إدارة فيسبوك وإنستغرام، حملات مدفوعة',
                    'contact_method' => 'email',
                    'status' => 'cancelled',
                    'progress' => 10,
                    'created_at' => now()->subDays(10),
                    'updated_at' => now()->subDays(8),
                ]);
            }

            // طلبات إضافية للمستخدم الأول
            ServiceOrder::create([
                'user_id' => $users->first()->id,
                'service_type' => 'web_development',
                'service_name' => 'تطوير متجر إلكتروني',
                'description' => 'متجر إلكتروني متكامل مع نظام دفع وإدارة المخزون',
                'budget' => 1800,
                'deadline' => now()->addDays(40),
                'requirements' => 'نظام دفع، إدارة مخزون، تقارير مبيعات',
                'contact_method' => 'email',
                'status' => 'pending',
                'progress' => 0,
                'created_at' => now()->subDays(1),
                'updated_at' => now()->subDays(1),
            ]);

            // طلب تصميم للمستخدم الثاني
            if ($users->count() > 1) {
                ServiceOrder::create([
                    'user_id' => $users->skip(1)->first()->id,
                    'service_type' => 'graphic_design',
                    'service_name' => 'تصميم بروشور تسويقي',
                    'description' => 'تصميم بروشور احترافي للشركة',
                    'budget' => 150,
                    'deadline' => now()->addDays(7),
                    'requirements' => 'تصميم جذاب، معلومات الشركة، صور عالية الجودة',
                    'contact_method' => 'whatsapp',
                    'status' => 'in_progress',
                    'progress' => 30,
                    'created_at' => now()->subDays(5),
                    'updated_at' => now()->subDays(1),
                ]);
            }
        }
    }
}
