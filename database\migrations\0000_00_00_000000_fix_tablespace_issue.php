<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        try {
            // محاولة حذف جدول migrations إذا كان موجوداً مع مشاكل tablespace
            DB::statement('DROP TABLE IF EXISTS migrations');
            
            // إنشاء جدول migrations جديد
            Schema::create('migrations', function (Blueprint $table) {
                $table->id();
                $table->string('migration');
                $table->integer('batch');
            });
            
            echo "✅ تم إنشاء جدول migrations بنجاح\n";
            
        } catch (\Exception $e) {
            // في حالة فشل الطريقة الأولى، جرب SQL مباشر
            try {
                DB::statement('SET foreign_key_checks = 0');
                DB::statement('DROP TABLE IF EXISTS migrations');
                DB::statement('SET foreign_key_checks = 1');
                
                DB::statement("
                    CREATE TABLE migrations (
                        id int unsigned NOT NULL AUTO_INCREMENT,
                        migration varchar(255) NOT NULL,
                        batch int NOT NULL,
                        PRIMARY KEY (id)
                    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                ");
                
                echo "✅ تم إنشاء جدول migrations بـ SQL مباشر\n";
                
            } catch (\Exception $e2) {
                echo "❌ فشل في إنشاء جدول migrations: " . $e2->getMessage() . "\n";
                throw $e2;
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('migrations');
    }
};
