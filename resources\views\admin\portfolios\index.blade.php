@extends('layouts.admin')

@section('title', 'إدارة الأعمال')

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">إدارة الأعمال</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">الأعمال</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Stats Cards -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ \App\Models\Portfolio::count() }}</h3>
                            <p>إجمالي الأعمال</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-briefcase"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ \App\Models\Portfolio::where('status', 'published')->count() }}</h3>
                            <p>أعمال منشورة</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ \App\Models\Portfolio::where('status', 'draft')->count() }}</h3>
                            <p>مسودات</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-edit"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-primary">
                        <div class="inner">
                            <h3>{{ \App\Models\Portfolio::whereDate('created_at', today())->count() }}</h3>
                            <p>أعمال اليوم</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-calendar-day"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Portfolio Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">قائمة الأعمال</h3>
                            <div class="card-tools">
                                <a href="{{ route('admin.portfolios.create') }}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> إضافة عمل جديد
                                </a>
                            </div>
                        </div>
                        <div class="card-body table-responsive p-0">
                            <table class="table table-hover text-nowrap">
                                <thead>
                                    <tr>
                                        <th>الصورة</th>
                                        <th>العنوان</th>
                                        <th>الفئة</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإنشاء</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse(\App\Models\Portfolio::latest()->take(10)->get() as $portfolio)
                                    <tr>
                                        <td>
                                            @if($portfolio->image)
                                                <img src="{{ asset('storage/' . $portfolio->image) }}" alt="{{ $portfolio->title }}" class="img-thumbnail" style="width: 50px; height: 50px;">
                                            @else
                                                <div class="bg-secondary d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                                    <i class="fas fa-image text-white"></i>
                                                </div>
                                            @endif
                                        </td>
                                        <td>{{ Str::limit($portfolio->title, 30) }}</td>
                                        <td>{{ $portfolio->category ?? 'غير محدد' }}</td>
                                        <td>
                                            @if($portfolio->status == 'published')
                                                <span class="badge badge-success">منشور</span>
                                            @else
                                                <span class="badge badge-warning">مسودة</span>
                                            @endif
                                        </td>
                                        <td>{{ $portfolio->created_at->format('Y-m-d') }}</td>
                                        <td>
                                            <a href="#" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="fas fa-briefcase fa-3x mb-3"></i>
                                            <br>
                                            لا توجد أعمال مضافة حالياً
                                            <br>
                                            <a href="{{ route('admin.portfolios.create') }}" class="btn btn-primary mt-2">
                                                <i class="fas fa-plus"></i> إضافة عمل جديد
                                            </a>
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    console.log('Portfolio page loaded');
});
</script>
@endsection
