@extends('backend.layouts.app')

@section('title', 'لوحة التحكم الرئيسية')

@section('content')
<div class="container-fluid">
    <!-- Welcome Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="welcome-header bg-gradient-primary text-white p-4 rounded-3 shadow-sm">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h1 class="h3 mb-2">
                            <i class="fas fa-tachometer-alt me-2"></i>
                            مرحباً بك، {{ $logged_in_user->name }}
                        </h1>
                        <p class="mb-0 opacity-75">
                            <i class="fas fa-calendar me-2"></i>
                            {{ now()->format('l, F j, Y') }}
                            <span class="ms-3">
                                <i class="fas fa-clock me-2"></i>
                                {{ now()->format('g:i A') }}
                            </span>
                        </p>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="quick-actions">
                            <a href="{{ route('admin.users.create') }}" class="btn btn-light btn-sm me-2">
                                <i class="fas fa-plus me-1"></i>
                                مستخدم جديد
                            </a>
                            <button class="btn btn-outline-light btn-sm" onclick="refreshDashboard()">
                                <i class="fas fa-sync-alt me-1"></i>
                                تحديث
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <x-backend.stats-cards
                :cards="[
                    [
                        'title' => 'إجمالي المستخدمين',
                        'value' => \App\Domains\Auth\Models\User::count(),
                        'previous_value' => \App\Domains\Auth\Models\User::where('created_at', '<', now()->subMonth())->count(),
                        'change' => 12.5,
                        'change_period' => 'من الشهر الماضي',
                        'icon' => 'fas fa-users',
                        'gradient' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        'action_url' => route('admin.auth.user.index'),
                        'action_text' => 'إدارة المستخدمين',
                        'additional_info' => 'المستخدمين النشطين: ' . \App\Domains\Auth\Models\User::where('active', true)->count()
                    ],
                    [
                        'title' => 'الأدوار والصلاحيات',
                        'value' => \App\Domains\Auth\Models\Role::count(),
                        'icon' => 'fas fa-shield-alt',
                        'gradient' => 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                        'action_url' => route('admin.auth.role.index'),
                        'action_text' => 'إدارة الأدوار'
                    ],
                    [
                        'title' => 'جلسات اليوم',
                        'value' => 156,
                        'previous_value' => 142,
                        'change' => 9.9,
                        'change_period' => 'من أمس',
                        'icon' => 'fas fa-chart-line',
                        'gradient' => 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                        'action_url' => '#',
                        'action_text' => 'عرض التقارير'
                    ],
                    [
                        'title' => 'معدل الأداء',
                        'value' => 98.5,
                        'previous_value' => 96.2,
                        'change' => 2.4,
                        'change_period' => 'من الأسبوع الماضي',
                        'icon' => 'fas fa-tachometer-alt',
                        'gradient' => 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                        'action_url' => '#',
                        'action_text' => 'تفاصيل الأداء'
                    ]
                ]"
                :animated="true"
                :realtime="false"
                :chart-enabled="false"
                :compare-enabled="true" />
        </div>
    </div>

    <!-- Main Content Row -->
    <div class="row">
        <!-- Recent Users -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-users text-primary me-2"></i>
                            المستخدمين الجدد
                        </h5>
                        <a href="{{ route('admin.auth.user.index') }}" class="btn btn-sm btn-outline-primary">
                            عرض الكل
                            <i class="fas fa-arrow-left ms-1"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>المستخدم</th>
                                    <th>البريد الإلكتروني</th>
                                    <th>الدور</th>
                                    <th>تاريخ التسجيل</th>
                                    <th>الحالة</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse(\App\Domains\Auth\Models\User::latest()->take(5)->get() as $user)
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary rounded-circle d-flex align-items-center justify-content-center text-white me-3">
                                                {{ substr($user->name, 0, 1) }}
                                            </div>
                                            <div>
                                                <h6 class="mb-0">{{ $user->name }}</h6>
                                                <small class="text-muted">{{ $user->email }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>{{ $user->email }}</td>
                                    <td>
                                        @if($user->roles->count())
                                            <span class="badge bg-info">{{ $user->roles->first()->name }}</span>
                                        @else
                                            <span class="badge bg-secondary">لا يوجد دور</span>
                                        @endif
                                    </td>
                                    <td>{{ $user->created_at->diffForHumans() }}</td>
                                    <td>
                                        @if($user->active)
                                            <span class="badge bg-success">نشط</span>
                                        @else
                                            <span class="badge bg-danger">غير نشط</span>
                                        @endif
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="5" class="text-center text-muted py-4">
                                        <i class="fas fa-users fa-2x mb-2"></i>
                                        <p>لا توجد مستخدمين جدد</p>
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions & System Info -->
        <div class="col-lg-4 mb-4">
            <!-- Quick Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('admin.auth.user.create') }}" class="btn btn-outline-primary">
                            <i class="fas fa-user-plus me-2"></i>
                            إضافة مستخدم جديد
                        </a>
                        <a href="{{ route('admin.auth.role.create') }}" class="btn btn-outline-success">
                            <i class="fas fa-shield-alt me-2"></i>
                            إنشاء دور جديد
                        </a>
                        <button class="btn btn-outline-info" onclick="showSystemInfo()">
                            <i class="fas fa-info-circle me-2"></i>
                            معلومات النظام
                        </button>
                        <button class="btn btn-outline-warning" onclick="clearCache()">
                            <i class="fas fa-broom me-2"></i>
                            مسح الذاكرة المؤقتة
                        </button>
                    </div>
                </div>
            </div>

            <!-- System Status -->
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server text-success me-2"></i>
                        حالة النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="system-status">
                        <div class="status-item d-flex justify-content-between align-items-center mb-3">
                            <span>حالة الخادم</span>
                            <span class="badge bg-success">متصل</span>
                        </div>
                        <div class="status-item d-flex justify-content-between align-items-center mb-3">
                            <span>قاعدة البيانات</span>
                            <span class="badge bg-success">متصلة</span>
                        </div>
                        <div class="status-item d-flex justify-content-between align-items-center mb-3">
                            <span>الذاكرة المستخدمة</span>
                            <span class="badge bg-warning">{{ round(memory_get_usage(true) / 1024 / 1024, 2) }} MB</span>
                        </div>
                        <div class="status-item d-flex justify-content-between align-items-center">
                            <span>إصدار Laravel</span>
                            <span class="badge bg-info">{{ app()->version() }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-history text-info me-2"></i>
                        النشاط الأخير
                    </h5>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-success"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تم إنشاء مستخدم جديد</h6>
                                <p class="timeline-description text-muted">تم تسجيل مستخدم جديد في النظام</p>
                                <small class="text-muted">منذ 5 دقائق</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تحديث الصلاحيات</h6>
                                <p class="timeline-description text-muted">تم تحديث صلاحيات المدير</p>
                                <small class="text-muted">منذ 15 دقيقة</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">تسجيل دخول جديد</h6>
                                <p class="timeline-description text-muted">تم تسجيل دخول من عنوان IP جديد</p>
                                <small class="text-muted">منذ 30 دقيقة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('after-styles')
<style>
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.welcome-header {
    border-radius: 15px !important;
    position: relative;
    overflow: hidden;
}

.welcome-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
}

.avatar-sm {
    width: 40px;
    height: 40px;
    font-size: 0.875rem;
    font-weight: 600;
}

.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    margin-bottom: 30px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 5px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.timeline-content {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    border-left: 3px solid #667eea;
}

.timeline-title {
    margin-bottom: 5px;
    font-size: 0.9rem;
    font-weight: 600;
}

.timeline-description {
    margin-bottom: 5px;
    font-size: 0.85rem;
}

.system-status .status-item {
    padding: 8px 0;
    border-bottom: 1px solid #f1f3f4;
}

.system-status .status-item:last-child {
    border-bottom: none;
}

.card {
    border-radius: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.quick-actions .btn {
    border-radius: 8px;
    font-weight: 500;
}

.table th {
    font-weight: 600;
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
}

.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

@media (max-width: 768px) {
    .welcome-header .quick-actions {
        margin-top: 1rem;
        text-align: center !important;
    }

    .timeline {
        padding-left: 20px;
    }

    .timeline-marker {
        left: -17px;
    }
}
</style>
@endpush

@push('after-scripts')
<script>
function refreshDashboard() {
    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري التحديث...';
    btn.disabled = true;

    // Simulate refresh
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function showSystemInfo() {
    const systemInfo = {
        'إصدار PHP': '{{ PHP_VERSION }}',
        'إصدار Laravel': '{{ app()->version() }}',
        'البيئة': '{{ app()->environment() }}',
        'المنطقة الزمنية': '{{ config("app.timezone") }}',
        'الذاكرة المستخدمة': '{{ round(memory_get_usage(true) / 1024 / 1024, 2) }} MB',
        'وقت التشغيل': '{{ gmdate("H:i:s", time() - $_SERVER["REQUEST_TIME"]) }}'
    };

    let infoHtml = '<div class="row">';
    Object.entries(systemInfo).forEach(([key, value]) => {
        infoHtml += `
            <div class="col-md-6 mb-2">
                <strong>${key}:</strong> ${value}
            </div>
        `;
    });
    infoHtml += '</div>';

    // Show in modal (assuming you have a modal component)
    if (typeof NotificationSystem !== 'undefined') {
        NotificationSystem.info(infoHtml, 'معلومات النظام', {
            duration: 10000,
            position: 'top-center'
        });
    } else {
        alert('معلومات النظام:\n' + Object.entries(systemInfo).map(([k,v]) => `${k}: ${v}`).join('\n'));
    }
}

function clearCache() {
    if (confirm('هل أنت متأكد من مسح الذاكرة المؤقتة؟')) {
        // Show loading
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>جاري المسح...';
        btn.disabled = true;

        // Make AJAX request to clear cache
        fetch('{{ route("admin.cache.clear") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content,
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (typeof NotificationSystem !== 'undefined') {
                    NotificationSystem.success('تم مسح الذاكرة المؤقتة بنجاح');
                } else {
                    alert('تم مسح الذاكرة المؤقتة بنجاح');
                }
            } else {
                throw new Error(data.message || 'حدث خطأ');
            }
        })
        .catch(error => {
            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.error('حدث خطأ أثناء مسح الذاكرة المؤقتة');
            } else {
                alert('حدث خطأ أثناء مسح الذاكرة المؤقتة');
            }
        })
        .finally(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;
        });
    }
}

// Auto-refresh stats every 5 minutes
setInterval(() => {
    // Update stats without full page reload
    updateDashboardStats();
}, 300000);

function updateDashboardStats() {
    fetch('{{ route("admin.dashboard.stats") }}')
        .then(response => response.json())
        .then(data => {
            // Update stats cards with new data
            if (data.success && data.stats) {
                // Update stats values
                Object.entries(data.stats).forEach(([key, value]) => {
                    const element = document.querySelector(`[data-stat="${key}"]`);
                    if (element) {
                        element.textContent = value;
                    }
                });
            }
        })
        .catch(error => {
            console.error('Error updating stats:', error);
        });
}

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Animate cards on load
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Update time every minute
    setInterval(updateTime, 60000);
});

function updateTime() {
    const timeElement = document.querySelector('.current-time');
    if (timeElement) {
        const now = new Date();
        timeElement.textContent = now.toLocaleTimeString('ar-SA');
    }
}
</script>
@endpush
