/* Meta Tech Revolutionary Design System - Next Generation UI */

:root {
    /* Revolutionary Brand Colors - Enhanced Palette */
    --meta-primary: #8b5cf6;
    --meta-primary-dark: #7c3aed;
    --meta-primary-light: #a78bfa;
    --meta-primary-ultra-light: #e9d5ff;
    --meta-secondary: #3b82f6;
    --meta-secondary-dark: #2563eb;
    --meta-secondary-light: #60a5fa;
    --meta-accent: #6366f1;
    --meta-accent-dark: #4f46e5;
    --meta-accent-light: #818cf8;
    --meta-orange: #f59e0b;
    --meta-orange-dark: #d97706;
    --meta-orange-light: #fbbf24;

    /* Revolutionary Gradient System */
    --gradient-primary: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
    --gradient-accent: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
    --gradient-warm: linear-gradient(135deg, #8b5cf6 0%, #f59e0b 100%);
    --gradient-cosmic: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-aurora: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    --gradient-sunset: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --gradient-ocean: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --gradient-neon: linear-gradient(135deg, #00f5ff 0%, #fc00ff 100%);

    /* Advanced Background Gradients */
    --bg-gradient-light: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    --bg-gradient-purple: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
    --bg-gradient-blue: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    --bg-gradient-cosmic: radial-gradient(ellipse at top, #667eea 0%, #764ba2 100%);
    --bg-gradient-mesh: conic-gradient(from 180deg at 50% 50%, #8b5cf6 0deg, #3b82f6 120deg, #6366f1 240deg, #8b5cf6 360deg);

    /* Revolutionary Glass & Morphism Effects */
    --glass-bg: rgba(255, 255, 255, 0.1);
    --glass-bg-strong: rgba(255, 255, 255, 0.15);
    --glass-border: rgba(255, 255, 255, 0.2);
    --glass-border-strong: rgba(255, 255, 255, 0.3);
    --glass-backdrop: blur(20px);
    --glass-backdrop-strong: blur(40px);

    /* Neumorphism Variables */
    --neuro-light: #ffffff;
    --neuro-dark: #e0e7ff;
    --neuro-shadow-light: rgba(255, 255, 255, 0.7);
    --neuro-shadow-dark: rgba(0, 0, 0, 0.1);

    /* Advanced Shadow System */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
    --shadow-purple: 0 10px 25px rgba(139, 92, 246, 0.3);
    --shadow-blue: 0 10px 25px rgba(59, 130, 246, 0.3);
    --shadow-neon: 0 0 20px rgba(139, 92, 246, 0.5);
    --shadow-glow: 0 0 40px rgba(139, 92, 246, 0.3);

    /* Revolutionary Animation Variables */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
    --transition-bounce: 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-elastic: 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

/* Revolutionary Section Backgrounds */
.section-bg-light {
    background: var(--bg-gradient-light);
    position: relative;
    overflow: hidden;
}

.section-bg-light::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(59, 130, 246, 0.1) 0%, transparent 50%);
    pointer-events: none;
}

.section-bg-purple {
    background: var(--bg-gradient-purple);
    position: relative;
    overflow: hidden;
}

.section-bg-purple::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-gradient-mesh);
    opacity: 0.1;
    pointer-events: none;
}

.section-bg-blue {
    background: var(--bg-gradient-blue);
    position: relative;
}

.section-bg-gradient {
    background: var(--gradient-primary);
    position: relative;
    overflow: hidden;
}

.section-bg-gradient::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

.section-bg-dark {
    background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #1e1b4b 100%);
    position: relative;
    overflow: hidden;
}

.section-bg-dark::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 30% 70%, rgba(139, 92, 246, 0.2) 0%, transparent 50%),
                radial-gradient(circle at 70% 30%, rgba(59, 130, 246, 0.2) 0%, transparent 50%);
    pointer-events: none;
}

.section-bg-cosmic {
    background: var(--bg-gradient-cosmic);
    position: relative;
    overflow: hidden;
}

.section-bg-mesh {
    background: var(--bg-gradient-mesh);
    position: relative;
    overflow: hidden;
}

/* Revolutionary Card System */
.meta-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: 20px;
    box-shadow: var(--shadow-lg);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.meta-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--gradient-primary);
    transform: scaleX(0);
    transition: transform var(--transition-normal);
}

.meta-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
    border-color: var(--meta-primary);
}

.meta-card:hover::before {
    transform: scaleX(1);
}

.meta-card-glass {
    background: var(--glass-bg-strong);
    backdrop-filter: var(--glass-backdrop-strong);
    border: 1px solid var(--glass-border-strong);
    border-radius: 24px;
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.meta-card-glass::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transform: rotate(45deg);
    transition: all var(--transition-slow);
    opacity: 0;
}

.meta-card-glass:hover::after {
    animation: shimmer 1.5s ease-in-out;
}

.meta-card-neuro {
    background: var(--neuro-light);
    border-radius: 20px;
    box-shadow:
        8px 8px 16px var(--neuro-shadow-dark),
        -8px -8px 16px var(--neuro-shadow-light);
    transition: all var(--transition-elastic);
    border: none;
}

.meta-card-neuro:hover {
    box-shadow:
        inset 8px 8px 16px var(--neuro-shadow-dark),
        inset -8px -8px 16px var(--neuro-shadow-light);
    transform: scale(0.98);
}

.meta-card-neon {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid var(--meta-primary);
    border-radius: 16px;
    box-shadow:
        0 0 20px var(--meta-primary),
        inset 0 0 20px rgba(139, 92, 246, 0.1);
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.meta-card-neon:hover {
    box-shadow:
        0 0 40px var(--meta-primary),
        0 0 60px var(--meta-primary),
        inset 0 0 40px rgba(139, 92, 246, 0.2);
    transform: translateY(-5px);
}

.meta-card-gradient {
    background: var(--gradient-primary);
    border-radius: 20px;
    padding: 2px;
    transition: all var(--transition-normal);
    position: relative;
}

.meta-card-gradient .card-content {
    background: white;
    border-radius: 18px;
    padding: 24px;
    height: 100%;
    transition: all var(--transition-normal);
}

.meta-card-gradient:hover {
    transform: translateY(-5px) rotate(1deg);
    box-shadow: var(--shadow-2xl);
}

.meta-card-gradient:hover .card-content {
    background: rgba(255, 255, 255, 0.95);
}

/* Revolutionary Button System */
.btn-meta-primary {
    background: var(--gradient-primary);
    color: white;
    padding: 14px 32px;
    border-radius: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: var(--shadow-lg);
}

.btn-meta-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left var(--transition-slow);
}

.btn-meta-primary:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-2xl), var(--shadow-glow);
}

.btn-meta-primary:hover::before {
    left: 100%;
}

.btn-meta-primary:active {
    transform: translateY(-1px) scale(1.02);
}

.btn-meta-secondary {
    background: transparent;
    color: var(--meta-primary);
    padding: 14px 32px;
    border-radius: 16px;
    font-weight: 600;
    border: 2px solid var(--meta-primary);
    cursor: pointer;
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.btn-meta-secondary::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: var(--gradient-primary);
    transition: width var(--transition-normal);
    z-index: -1;
}

.btn-meta-secondary:hover {
    color: white;
    transform: translateY(-3px) scale(1.05);
    box-shadow: var(--shadow-xl);
}

.btn-meta-secondary:hover::before {
    width: 100%;
}

.btn-meta-glass {
    background: var(--glass-bg-strong);
    backdrop-filter: var(--glass-backdrop);
    color: var(--meta-primary);
    padding: 14px 32px;
    border-radius: 20px;
    font-weight: 600;
    border: 1px solid var(--glass-border-strong);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: var(--shadow-lg);
}

.btn-meta-glass:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
    border-color: var(--meta-primary);
}

.btn-meta-neuro {
    background: var(--neuro-light);
    color: var(--meta-primary);
    padding: 14px 32px;
    border-radius: 20px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all var(--transition-elastic);
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow:
        8px 8px 16px var(--neuro-shadow-dark),
        -8px -8px 16px var(--neuro-shadow-light);
}

.btn-meta-neuro:hover {
    box-shadow:
        inset 4px 4px 8px var(--neuro-shadow-dark),
        inset -4px -4px 8px var(--neuro-shadow-light);
    transform: scale(0.98);
}

.btn-meta-neuro:active {
    box-shadow:
        inset 8px 8px 16px var(--neuro-shadow-dark),
        inset -8px -8px 16px var(--neuro-shadow-light);
}

.btn-meta-neon {
    background: transparent;
    color: var(--meta-primary);
    padding: 14px 32px;
    border-radius: 16px;
    font-weight: 600;
    border: 2px solid var(--meta-primary);
    cursor: pointer;
    transition: all var(--transition-normal);
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow:
        0 0 10px var(--meta-primary),
        inset 0 0 10px rgba(139, 92, 246, 0.1);
    position: relative;
    overflow: hidden;
}

.btn-meta-neon:hover {
    color: white;
    background: var(--meta-primary);
    box-shadow:
        0 0 20px var(--meta-primary),
        0 0 40px var(--meta-primary),
        inset 0 0 20px rgba(255, 255, 255, 0.1);
    transform: translateY(-3px);
}

.btn-hostinger-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    filter: brightness(1.1);
}

.btn-hostinger-secondary {
    background: var(--gradient-secondary);
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-blue);
}

.btn-hostinger-secondary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    filter: brightness(1.1);
}

/* Unified Icon Styles */
.hostinger-icon {
    width: 48px;
    height: 48px;
    background: var(--gradient-primary);
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    box-shadow: var(--shadow-purple);
    transition: all 0.3s ease;
}

.hostinger-icon:hover {
    transform: scale(1.1);
    box-shadow: var(--shadow-xl);
}

.hostinger-icon-large {
    width: 64px;
    height: 64px;
    font-size: 24px;
}

.hostinger-icon-small {
    width: 32px;
    height: 32px;
    font-size: 16px;
}

/* Unified Text Gradients */
.text-gradient-primary {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-secondary {
    background: var(--gradient-secondary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-warm {
    background: var(--gradient-warm);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Unified Animations */
@keyframes hostinger-float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes hostinger-pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.7; }
}

@keyframes hostinger-glow {
    0%, 100% { box-shadow: var(--shadow-purple); }
    50% { box-shadow: var(--shadow-xl); }
}

.animate-hostinger-float {
    animation: hostinger-float 3s ease-in-out infinite;
}

.animate-hostinger-pulse {
    animation: hostinger-pulse 2s ease-in-out infinite;
}

.animate-hostinger-glow {
    animation: hostinger-glow 2s ease-in-out infinite;
}

/* Unified Spacing */
.section-padding {
    padding: 80px 0;
}

.section-padding-sm {
    padding: 40px 0;
}

.section-padding-md {
    padding: 60px 0;
}

.section-padding-lg {
    padding: 80px 0;
}

.section-padding-xl {
    padding: 100px 0;
}

/* Button Styles */
.btn-orange {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    border: none;
    cursor: pointer;
}

.btn-orange:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(245, 158, 11, 0.3);
}

.btn-secondary {
    background: transparent;
    color: white;
    padding: 12px 24px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    border: 2px solid rgba(255, 255, 255, 0.3);
    cursor: pointer;
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
}

/* Card Styles */
.card-hover {
    border-radius: 16px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 1px solid rgba(139, 92, 246, 0.1);
}

.card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(139, 92, 246, 0.15);
    border-color: rgba(139, 92, 246, 0.3);
}

/* Feature Icon */
.feature-icon {
    width: 64px;
    height: 64px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 24px;
    color: white;
    font-size: 24px;
    transition: all 0.3s ease;
}

.feature-icon:hover {
    transform: scale(1.1);
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
    color: transparent;
}

/* Responsive Design */
@media (max-width: 768px) {
    .section-padding-sm { padding: 30px 0; }
    .section-padding-md { padding: 40px 0; }
    .section-padding-lg { padding: 50px 0; }
    .section-padding-xl { padding: 60px 0; }
    
    .hostinger-icon-large {
        width: 48px;
        height: 48px;
        font-size: 20px;
    }
}

/* Animation Delays */
.animation-delay-200 {
    animation-delay: 0.2s;
}

.animation-delay-400 {
    animation-delay: 0.4s;
}

.animation-delay-1000 {
    animation-delay: 1s;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-3000 {
    animation-delay: 3s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

/* Blob Animation */
@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

.animate-blob {
    animation: blob 7s infinite;
}

/* Float Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

/* Bounce Animation for Typing Indicator */
@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

.animate-bounce {
    animation: bounce 1.4s infinite ease-in-out both;
}

/* Ping Animation */
@keyframes ping {
    75%, 100% {
        transform: scale(2);
        opacity: 0;
    }
}

.animate-ping {
    animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}

/* Pulse Animation */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: .5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Navigation Enhancements */
.navbar {
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nav-item {
    position: relative;
    overflow: hidden;
    white-space: nowrap;
    font-weight: 500;
}

.nav-item::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-item:hover::before {
    width: 80%;
}

/* Navigation Spacing */
.navbar .space-x-4 > * + * {
    margin-right: 1rem;
}

.navbar .space-x-6 > * + * {
    margin-right: 1.5rem;
}

/* Dropdown Enhancements */
.group:hover .group-hover\:opacity-100 {
    opacity: 1;
}

.group:hover .group-hover\:visible {
    visibility: visible;
}

/* Dropdown Animation */
.opacity-0.invisible {
    transform: translateY(-10px);
}

.group-hover\:opacity-100.group-hover\:visible {
    transform: translateY(0);
}

/* Dropdown Items */
.dropdown-item {
    transition: all 0.2s ease;
}

.dropdown-item:hover {
    background-color: rgba(139, 92, 246, 0.05);
    color: #8b5cf6;
    transform: translateX(-2px);
}

/* Navigation Active State */
.nav-active {
    color: #8b5cf6 !important;
    font-weight: 600;
}

.nav-active::before {
    width: 80% !important;
}

/* Mobile Menu Improvements */
@media (max-width: 1024px) {
    .navbar {
        padding: 0.5rem 1rem;
    }

    .navbar .h-16 {
        height: 4rem;
    }
}

/* Dropdown Shadow Enhancement */
.shadow-lg {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Contact Button Clean Design */
.contact-btn-clean {
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    border: none;
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
    transition: all 0.3s ease;
}

.contact-btn-clean:hover {
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
    transform: translateY(-1px) scale(1.02);
}

/* Navigation Button Spacing */
.nav-contact-btn {
    margin-right: 1rem;
}

/* Login Button Style */
.login-btn {
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    transition: all 0.3s ease;
}

.login-btn:hover {
    border-color: #8b5cf6;
    color: #8b5cf6;
    background: #f3f4f6;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(139, 92, 246, 0.1);
}

/* Action Buttons Container */
.action-buttons {
    gap: 0.75rem;
}

/* Clean Button Style */
.btn-clean {
    border: none;
    outline: none;
    cursor: pointer;
    font-family: inherit;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    white-space: nowrap;
}

/* Button Hover Effects */
.btn-hover-lift:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Quick Contact Buttons */
.quick-contact-btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.quick-contact-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.quick-contact-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s, height 0.3s;
}

.quick-contact-btn:hover::after {
    width: 100px;
    height: 100px;
}

/* Smooth Transitions */
* {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .hostinger-card {
        background: rgba(30, 27, 75, 0.95);
        border-color: rgba(139, 92, 246, 0.3);
    }

    .section-bg-light {
        background: linear-gradient(135deg, #1e1b4b 0%, #312e81 100%);
    }

    .navbar {
        background: rgba(17, 24, 39, 0.95);
        border-color: rgba(75, 85, 99, 0.3);
    }
}

/* Revolutionary Animation System */
@keyframes shimmer {
    0% {
        transform: translateX(-100%) rotate(45deg);
    }
    100% {
        transform: translateX(200%) rotate(45deg);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 20px var(--meta-primary);
    }
    50% {
        box-shadow: 0 0 40px var(--meta-primary), 0 0 60px var(--meta-primary);
    }
}

@keyframes rotate-gradient {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

@keyframes wave {
    0%, 100% {
        transform: translateX(0) translateY(0) rotate(0deg);
    }
    25% {
        transform: translateX(5px) translateY(-5px) rotate(1deg);
    }
    50% {
        transform: translateX(-5px) translateY(5px) rotate(-1deg);
    }
    75% {
        transform: translateX(-5px) translateY(-5px) rotate(1deg);
    }
}

@keyframes morphing {
    0%, 100% {
        border-radius: 20px;
    }
    25% {
        border-radius: 30px 20px 30px 20px;
    }
    50% {
        border-radius: 40px;
    }
    75% {
        border-radius: 20px 40px 20px 40px;
    }
}

@keyframes gradient-shift {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

@keyframes bounce-in {
    0% {
        transform: scale(0.3) translateY(100px);
        opacity: 0;
    }
    50% {
        transform: scale(1.05) translateY(-10px);
        opacity: 0.8;
    }
    70% {
        transform: scale(0.9) translateY(5px);
        opacity: 0.9;
    }
    100% {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

@keyframes slide-in-right {
    0% {
        transform: translateX(100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slide-in-left {
    0% {
        transform: translateX(-100px);
        opacity: 0;
    }
    100% {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fade-in-up {
    0% {
        transform: translateY(50px);
        opacity: 0;
    }
    100% {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes scale-in {
    0% {
        transform: scale(0);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Revolutionary Animation Classes */
.animate-shimmer {
    animation: shimmer 2s infinite;
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
}

.animate-rotate-gradient {
    animation: rotate-gradient 8s linear infinite;
}

.animate-wave {
    animation: wave 4s ease-in-out infinite;
}

.animate-morphing {
    animation: morphing 6s ease-in-out infinite;
}

.animate-gradient-shift {
    background-size: 400% 400%;
    animation: gradient-shift 4s ease infinite;
}

.animate-bounce-in {
    animation: bounce-in 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.animate-slide-in-right {
    animation: slide-in-right 0.6s ease-out;
}

.animate-slide-in-left {
    animation: slide-in-left 0.6s ease-out;
}

.animate-fade-in-up {
    animation: fade-in-up 0.8s ease-out;
}

.animate-scale-in {
    animation: scale-in 0.5s ease-out;
}

/* Hover Animation Classes */
.hover-lift {
    transition: all var(--transition-normal);
}

.hover-lift:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-2xl);
}

.hover-glow {
    transition: all var(--transition-normal);
}

.hover-glow:hover {
    box-shadow: var(--shadow-glow);
}

.hover-scale {
    transition: all var(--transition-bounce);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-rotate {
    transition: all var(--transition-normal);
}

.hover-rotate:hover {
    transform: rotate(5deg);
}

.hover-tilt {
    transition: all var(--transition-normal);
}

.hover-tilt:hover {
    transform: perspective(1000px) rotateX(10deg) rotateY(10deg);
}

/* Scroll Animation Classes */
.scroll-reveal {
    opacity: 0;
    transform: translateY(50px);
    transition: all 0.8s ease-out;
}

.scroll-reveal.revealed {
    opacity: 1;
    transform: translateY(0);
}

.scroll-reveal-left {
    opacity: 0;
    transform: translateX(-50px);
    transition: all 0.8s ease-out;
}

.scroll-reveal-left.revealed {
    opacity: 1;
    transform: translateX(0);
}

.scroll-reveal-right {
    opacity: 0;
    transform: translateX(50px);
    transition: all 0.8s ease-out;
}

.scroll-reveal-right.revealed {
    opacity: 1;
    transform: translateX(0);
}

.scroll-reveal-scale {
    opacity: 0;
    transform: scale(0.8);
    transition: all 0.8s ease-out;
}

.scroll-reveal-scale.revealed {
    opacity: 1;
    transform: scale(1);
}

/* Revolutionary Utility Classes */
.text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-cosmic {
    background: var(--gradient-cosmic);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient-neon {
    background: var(--gradient-neon);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.bg-mesh {
    background: var(--bg-gradient-mesh);
}

.bg-cosmic {
    background: var(--bg-gradient-cosmic);
}

.backdrop-blur-strong {
    backdrop-filter: var(--glass-backdrop-strong);
}

.backdrop-blur-normal {
    backdrop-filter: var(--glass-backdrop);
}

/* Revolutionary Interactive Elements */
.interactive-card {
    cursor: pointer;
    transition: all var(--transition-bounce);
    position: relative;
    overflow: hidden;
}

.interactive-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform var(--transition-slow);
}

.interactive-card:hover::before {
    transform: translateX(100%);
}

.interactive-card:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: var(--shadow-2xl);
}

/* Revolutionary Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

.loading-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

/* Revolutionary Responsive Design */
@media (max-width: 768px) {
    .meta-card {
        border-radius: 16px;
        padding: 16px;
    }

    .btn-meta-primary,
    .btn-meta-secondary,
    .btn-meta-glass,
    .btn-meta-neuro,
    .btn-meta-neon {
        padding: 12px 24px;
        font-size: 14px;
    }

    .hover-lift:hover,
    .hover-scale:hover,
    .hover-rotate:hover,
    .hover-tilt:hover {
        transform: none;
    }

    .animate-float,
    .animate-wave,
    .animate-morphing {
        animation: none;
    }
}

@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .scroll-reveal,
    .scroll-reveal-left,
    .scroll-reveal-right,
    .scroll-reveal-scale {
        opacity: 1;
        transform: none;
    }
}

/* Grid Animation - Enhanced */
@keyframes gridMove {
    0% {
        transform: translate(0, 0);
    }
    25% {
        transform: translate(20px, 10px);
    }
    50% {
        transform: translate(40px, 40px);
    }
    75% {
        transform: translate(60px, 20px);
    }
    100% {
        transform: translate(80px, 80px);
    }
}

/* Additional Animation Delays */
.animation-delay-500 {
    animation-delay: 0.5s;
}

.animation-delay-1000 {
    animation-delay: 1s;
}

.animation-delay-1500 {
    animation-delay: 1.5s;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-3000 {
    animation-delay: 3s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

/* Floating Shapes Animation */
@keyframes floatShape {
    0%, 100% {
        transform: translateY(0px) rotate(0deg);
    }
    33% {
        transform: translateY(-15px) rotate(5deg);
    }
    66% {
        transform: translateY(10px) rotate(-3deg);
    }
}

.animate-float-shape {
    animation: floatShape 8s ease-in-out infinite;
}

/* Particle Animation */
@keyframes particle {
    0% {
        transform: translateY(0px) scale(1);
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        transform: translateY(-100px) scale(0);
        opacity: 0;
    }
}

.animate-particle {
    animation: particle 4s ease-out infinite;
}

/* Enhanced Card Hover Effects */
.meta-feature-card {
    position: relative;
    overflow: hidden;
}

.meta-feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.meta-feature-card:hover::before {
    left: 100%;
}

/* Floating Animation for Cards */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

/* Pulse Glow Effect */
@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
    }
}

.animate-pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
}

/* Additional Performance Optimizations */
.lazy {
    opacity: 0;
    transition: opacity 0.3s;
}

.lazy.loaded {
    opacity: 1;
}

/* Button Loading State */
.btn-primary.loading,
.btn-secondary.loading,
.btn-meta-primary.loading,
.btn-meta-secondary.loading {
    pointer-events: none;
    opacity: 0.7;
}

/* Navbar Scroll Effect */
.navbar.scrolled {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

/* Enhanced Focus States for Accessibility */
.btn-meta-primary:focus,
.btn-meta-secondary:focus,
.btn-meta-glass:focus,
.btn-meta-neuro:focus,
.btn-meta-neon:focus {
    outline: 2px solid var(--meta-primary);
    outline-offset: 2px;
}

/* Print Styles */
@media print {
    .animate-float,
    .animate-pulse-glow,
    .animate-wave,
    .animate-morphing {
        animation: none !important;
    }

    .meta-card-glass,
    .meta-card-neon {
        background: white !important;
        border: 1px solid #ccc !important;
    }
}
