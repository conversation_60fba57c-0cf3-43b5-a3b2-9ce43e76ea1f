{"__meta": {"id": "X53cfea7c6b1d72151404f7833a750317", "datetime": "2025-07-26 02:26:49", "utime": **********.331357, "method": "GET", "uri": "/_ignition/health-check", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[02:26:49] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp2\\htdocs\\‏‏Meta\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.095421, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.786412, "end": **********.331373, "duration": 0.5449609756469727, "duration_str": "545ms", "measures": [{"label": "Booting", "start": **********.786412, "relative_start": 0, "end": **********.07397, "relative_end": **********.07397, "duration": 0.28755807876586914, "duration_str": "288ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.073983, "relative_start": 0.2875709533691406, "end": **********.331375, "relative_end": 1.9073486328125e-06, "duration": 0.25739192962646484, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 26937496, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET _ignition/health-check", "middleware": "Facade\\Ignition\\Http\\Middleware\\IgnitionEnabled", "uses": "Facade\\Ignition\\Http\\Controllers\\HealthCheckController@__invoke", "controller": "Facade\\Ignition\\Http\\Controllers\\HealthCheckController", "as": "ignition.healthCheck", "namespace": null, "prefix": "_ignition", "where": []}, "queries": {"nb_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "statements": []}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": [], "request": {"path_info": "/_ignition/health-check", "status_code": "<pre class=sf-dump id=sf-dump-900273736 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-900273736\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-485620384 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-485620384\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-966513949 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-966513949\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-104692199 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1285 characters\">_ga=GA1.1.8662866.1751666474; _ga_69MPZE94D5=GS2.1.s1751666473$o1$g0$t1751666495$j38$l0$h0; _ga_968CT9KM7N=GS2.1.s1752007871$o1$g1$t1752007920$j11$l0$h0; mgmoaa_abrahym_alahmdy_alymny_session=eyJpdiI6IjBiTldDNXZtMS9JWklzbFdFeGk5RHc9PSIsInZhbHVlIjoiaWtjcjRlZ0ZRNTRlSm9OVlhyeTRwOHR3RW5KV0xSZVlHNGdQdDFtM2NRMEROa3MrR1RUaDBCYW04Z3k2L3JaZ3FXS29TNlIwY0orbkZzM0k4TWFjZFFVOWJTcXpzWmxkQVZuY2M5OE55QTIxV2xJMDRNNmZLK3RYSkZjQS85emYiLCJtYWMiOiIwMmI1MTUxNzA5N2ZiMzRkMDFmODU3M2U0ODliYmM1ZmE2ZDdlZWMzNjVmNTZlMmNkMjZiY2FmYWNkOTZiNzNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlpGZkx0Yy9hSk9kUHV3Vno2eFRDc1E9PSIsInZhbHVlIjoiNVJvNVVSNzA2NGl0b1FHVzdqUXZ5QUFXQUNET0pxS1ZuOVNDNE56VDdXTTZad28xLy9tTlFDNHhuL0RhcVQwVE1WdWhuS1VHcmV2V21kTjhmWmhvOE5iSVRqQ1dYMjcwNVlkNFdhaVRlMzNpLzBTeTdEWk9ZdHZnTXkxUjlHM2IiLCJtYWMiOiI2YTQyY2U4OThjZTQxNzI1NmUwODZlZTA2ZmE4ZDNkYjJlZDdkODIwYzRmMWE2MWRmYjY2MjY2NGUyZGIzMzNhIiwidGFnIjoiIn0%3D; mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session=eyJpdiI6IlNuR1BNVjFhT0JidWdtSEloaXZ5VHc9PSIsInZhbHVlIjoiU1Q3WklVWURUZjBiaDRSSGc0WXpIb1h2UlBHdDFsRGV1SVhIOXhudTVzdkFrZ25YSWRVM2pFbDlPSW9rUmhMT0JQUkxNKzlLSzlRbHI1TmIrcW8wU3huNWpGY0xSN1R5ZXBDdGdwMExGK0RLYVVhODN1bEJxdjJ5cEEvQlNXdTMiLCJtYWMiOiI3ZDI0ZDE5M2QwOGYzZGY0ZmYyMmE2N2ViNTE0NTRlOTNiZGI5N2IzYmVlNDNmNmIyNTgxMDI0ZjBhMDdhZWM4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-104692199\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1847823389 data-indent-pad=\"  \"><span class=sf-dump-note>array:29</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"30 characters\">C:\\xampp2\\htdocs\\&#8207;&#8207;Meta\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">59812</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/_ignition/health-check</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C:\\xampp2\\htdocs\\&#8207;&#8207;Meta\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PATH_INFO</span>\" => \"<span class=sf-dump-str title=\"23 characters\">/_ignition/health-check</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"33 characters\">/index.php/_ignition/health-check</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"22 characters\">http://127.0.0.1:8000/</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1285 characters\">_ga=GA1.1.8662866.1751666474; _ga_69MPZE94D5=GS2.1.s1751666473$o1$g0$t1751666495$j38$l0$h0; _ga_968CT9KM7N=GS2.1.s1752007871$o1$g1$t1752007920$j11$l0$h0; mgmoaa_abrahym_alahmdy_alymny_session=eyJpdiI6IjBiTldDNXZtMS9JWklzbFdFeGk5RHc9PSIsInZhbHVlIjoiaWtjcjRlZ0ZRNTRlSm9OVlhyeTRwOHR3RW5KV0xSZVlHNGdQdDFtM2NRMEROa3MrR1RUaDBCYW04Z3k2L3JaZ3FXS29TNlIwY0orbkZzM0k4TWFjZFFVOWJTcXpzWmxkQVZuY2M5OE55QTIxV2xJMDRNNmZLK3RYSkZjQS85emYiLCJtYWMiOiIwMmI1MTUxNzA5N2ZiMzRkMDFmODU3M2U0ODliYmM1ZmE2ZDdlZWMzNjVmNTZlMmNkMjZiY2FmYWNkOTZiNzNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IlpGZkx0Yy9hSk9kUHV3Vno2eFRDc1E9PSIsInZhbHVlIjoiNVJvNVVSNzA2NGl0b1FHVzdqUXZ5QUFXQUNET0pxS1ZuOVNDNE56VDdXTTZad28xLy9tTlFDNHhuL0RhcVQwVE1WdWhuS1VHcmV2V21kTjhmWmhvOE5iSVRqQ1dYMjcwNVlkNFdhaVRlMzNpLzBTeTdEWk9ZdHZnTXkxUjlHM2IiLCJtYWMiOiI2YTQyY2U4OThjZTQxNzI1NmUwODZlZTA2ZmE4ZDNkYjJlZDdkODIwYzRmMWE2MWRmYjY2MjY2NGUyZGIzMzNhIiwidGFnIjoiIn0%3D; mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session=eyJpdiI6IlNuR1BNVjFhT0JidWdtSEloaXZ5VHc9PSIsInZhbHVlIjoiU1Q3WklVWURUZjBiaDRSSGc0WXpIb1h2UlBHdDFsRGV1SVhIOXhudTVzdkFrZ25YSWRVM2pFbDlPSW9rUmhMT0JQUkxNKzlLSzlRbHI1TmIrcW8wU3huNWpGY0xSN1R5ZXBDdGdwMExGK0RLYVVhODN1bEJxdjJ5cEEvQlNXdTMiLCJtYWMiOiI3ZDI0ZDE5M2QwOGYzZGY0ZmYyMmE2N2ViNTE0NTRlOTNiZGI5N2IzYmVlNDNmNmIyNTgxMDI0ZjBhMDdhZWM4IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.7864</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1847823389\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1242665167 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => \"<span class=sf-dump-str title=\"24 characters\">GA1.1.8662866.1751666474</span>\"\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => \"<span class=sf-dump-str title=\"45 characters\">GS2.1.s1751666473$o1$g0$t1751666495$j38$l0$h0</span>\"\n  \"<span class=sf-dump-key>_ga_968CT9KM7N</span>\" => \"<span class=sf-dump-str title=\"45 characters\">GS2.1.s1752007871$o1$g1$t1752007920$j11$l0$h0</span>\"\n  \"<span class=sf-dump-key>mgmoaa_abrahym_alahmdy_alymny_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjBiTldDNXZtMS9JWklzbFdFeGk5RHc9PSIsInZhbHVlIjoiaWtjcjRlZ0ZRNTRlSm9OVlhyeTRwOHR3RW5KV0xSZVlHNGdQdDFtM2NRMEROa3MrR1RUaDBCYW04Z3k2L3JaZ3FXS29TNlIwY0orbkZzM0k4TWFjZFFVOWJTcXpzWmxkQVZuY2M5OE55QTIxV2xJMDRNNmZLK3RYSkZjQS85emYiLCJtYWMiOiIwMmI1MTUxNzA5N2ZiMzRkMDFmODU3M2U0ODliYmM1ZmE2ZDdlZWMzNjVmNTZlMmNkMjZiY2FmYWNkOTZiNzNiIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlpGZkx0Yy9hSk9kUHV3Vno2eFRDc1E9PSIsInZhbHVlIjoiNVJvNVVSNzA2NGl0b1FHVzdqUXZ5QUFXQUNET0pxS1ZuOVNDNE56VDdXTTZad28xLy9tTlFDNHhuL0RhcVQwVE1WdWhuS1VHcmV2V21kTjhmWmhvOE5iSVRqQ1dYMjcwNVlkNFdhaVRlMzNpLzBTeTdEWk9ZdHZnTXkxUjlHM2IiLCJtYWMiOiI2YTQyY2U4OThjZTQxNzI1NmUwODZlZTA2ZmE4ZDNkYjJlZDdkODIwYzRmMWE2MWRmYjY2MjY2NGUyZGIzMzNhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlNuR1BNVjFhT0JidWdtSEloaXZ5VHc9PSIsInZhbHVlIjoiU1Q3WklVWURUZjBiaDRSSGc0WXpIb1h2UlBHdDFsRGV1SVhIOXhudTVzdkFrZ25YSWRVM2pFbDlPSW9rUmhMT0JQUkxNKzlLSzlRbHI1TmIrcW8wU3huNWpGY0xSN1R5ZXBDdGdwMExGK0RLYVVhODN1bEJxdjJ5cEEvQlNXdTMiLCJtYWMiOiI3ZDI0ZDE5M2QwOGYzZGY0ZmYyMmE2N2ViNTE0NTRlOTNiZGI5N2IzYmVlNDNmNmIyNTgxMDI0ZjBhMDdhZWM4IiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1242665167\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1972843622 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 02:26:49 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1972843622\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1260481749 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1260481749\", {\"maxDepth\":0})</script>\n"}}