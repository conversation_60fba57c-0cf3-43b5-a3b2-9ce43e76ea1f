<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class FastBlogSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('🚀 بدء إنشاء مليون مقال...');
        
        // Disable foreign key checks and other constraints for speed
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        DB::statement('SET UNIQUE_CHECKS=0;');
        DB::statement('SET AUTOCOMMIT=0;');
        
        // Clear existing posts
        DB::table('blog_posts')->truncate();
        
        $batchSize = 10000; // Large batch size for maximum speed
        $totalPosts = 1000000;
        $batches = ceil($totalPosts / $batchSize);
        
        $categories = ['البرمجة', 'التصميم', 'التقنية', 'الأمان', 'تحليلات', 'التسويق', 'قواعد البيانات', 'تصميم الجوال', 'الحوسبة السحابية', 'تطوير API', 'التجارة الإلكترونية', 'DevOps', 'الذكاء الاصطناعي', 'البلوك تشين'];
        
        $authors = ['أحمد محمد', 'سارة أحمد', 'محمد عبدالله', 'فاطمة سالم', 'خالد عمر', 'نورا حسن', 'عبدالله يوسف', 'مريم علي', 'John Smith', 'Sarah Johnson', 'Michael Brown', 'Emily Davis', 'Ahmed Hassan', 'Fatima Al-Zahra', 'Marco Rossi', 'Giulia Ferrari'];
        
        $techs = ['React', 'Vue.js', 'Angular', 'Laravel', 'Django', 'Node.js', 'Python', 'JavaScript', 'TypeScript', 'PHP', 'Java', 'C#', 'Go', 'Rust', 'Docker', 'Kubernetes'];
        
        $images = [
            'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
            'https://images.unsplash.com/photo-1558655146-9f40138edfeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80'
        ];
        
        for ($i = 0; $i < $batches; $i++) {
            $this->command->info("📝 إنشاء الدفعة " . ($i + 1) . " من {$batches}");
            
            $posts = [];
            $currentBatchSize = min($batchSize, $totalPosts - ($i * $batchSize));
            
            for ($j = 0; $j < $currentBatchSize; $j++) {
                $postNumber = ($i * $batchSize) + $j + 1;
                $category = $categories[array_rand($categories)];
                $author = $authors[array_rand($authors)];
                $tech = $techs[array_rand($techs)];
                
                $title = "دليل شامل لتعلم {$tech} - المقال رقم {$postNumber}";
                $slug = "guide-{$tech}-post-{$postNumber}";
                $excerpt = "تعلم أساسيات {$tech} وكيفية استخدامه في مشاريع {$category} الحديثة.";
                $content = "<h2>مقدمة</h2><p>في هذا المقال، سنتعلم أساسيات {$category} وكيفية استخدام {$tech}.</p><h2>الأساسيات</h2><p>لفهم {$category} بشكل صحيح، نحتاج لفهم {$tech}.</p><h2>الخلاصة</h2><p>تعلمنا في هذا المقال أساسيات {$tech} في {$category}.</p>";
                
                $posts[] = [
                    'title' => $title,
                    'slug' => $slug,
                    'excerpt' => $excerpt,
                    'content' => $content,
                    'image' => $images[array_rand($images)],
                    'category' => $category,
                    'author' => $author,
                    'read_time' => rand(3, 20) . ' دقيقة',
                    'is_featured' => rand(1, 100) <= 2,
                    'is_published' => rand(1, 100) <= 98,
                    'views' => rand(0, 10000),
                    'tags' => json_encode(['تقنية', 'برمجة', $category, $tech]),
                    'meta_data' => json_encode(['difficulty' => ['مبتدئ', 'متوسط', 'متقدم'][rand(0, 2)]]),
                    'created_at' => now()->subDays(rand(0, 365))->format('Y-m-d H:i:s'),
                    'updated_at' => now()->format('Y-m-d H:i:s')
                ];
            }
            
            // Insert batch using raw SQL for maximum speed
            DB::table('blog_posts')->insert($posts);
            
            // Show progress
            $completed = min(($i + 1) * $batchSize, $totalPosts);
            $percentage = round(($completed / $totalPosts) * 100, 1);
            $this->command->info("✅ تم إنشاء {$completed} مقال من أصل {$totalPosts} ({$percentage}%)");
            
            // Commit every batch
            DB::statement('COMMIT;');
            
            // Clear memory
            unset($posts);
        }
        
        // Re-enable constraints
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
        DB::statement('SET UNIQUE_CHECKS=1;');
        DB::statement('SET AUTOCOMMIT=1;');
        
        $this->command->info('🎉 تم إنشاء مليون مقال بنجاح!');
        $this->command->info("📊 إجمالي المقالات: " . number_format($totalPosts));
    }
}
