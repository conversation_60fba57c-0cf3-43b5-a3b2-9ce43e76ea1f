/* Domain Purchase Modal Styles */
.domain-purchase-modal {
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
}

.modal-container {
    max-height: 90vh;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #cbd5e0 #f7fafc;
}

.modal-container::-webkit-scrollbar {
    width: 6px;
}

.modal-container::-webkit-scrollbar-track {
    background: #f7fafc;
    border-radius: 3px;
}

.modal-container::-webkit-scrollbar-thumb {
    background: #cbd5e0;
    border-radius: 3px;
}

.modal-container::-webkit-scrollbar-thumb:hover {
    background: #a0aec0;
}

/* Period Selection Cards */
.period-card {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.period-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(124, 58, 237, 0.1), transparent);
    transition: left 0.5s ease;
}

.period-card:hover::before {
    left: 100%;
}

.period-card:hover {
    border-color: #7c3aed;
    box-shadow: 0 8px 25px rgba(124, 58, 237, 0.15);
    transform: translateY(-2px);
}

.period-card.selected {
    border-color: #7c3aed;
    background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
    box-shadow: 0 4px 15px rgba(124, 58, 237, 0.2);
}

/* Radio Button Styling */
.custom-radio {
    appearance: none;
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    position: relative;
    cursor: pointer;
    transition: all 0.2s ease;
}

.custom-radio:checked {
    border-color: #7c3aed;
    background-color: #7c3aed;
}

.custom-radio:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background-color: white;
    border-radius: 50%;
}

/* Payment Method Card */
.payment-method-card {
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
}

.payment-method-card:hover {
    border-color: #7c3aed;
    box-shadow: 0 4px 12px rgba(124, 58, 237, 0.1);
    transform: translateY(-1px);
}

/* Pricing Summary */
.pricing-summary {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
}

.pricing-row {
    padding: 8px 0;
    border-bottom: 1px solid #e2e8f0;
}

.pricing-row:last-child {
    border-bottom: none;
    padding-top: 12px;
    font-weight: 600;
}

/* Action Buttons */
.btn-cancel {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 2px solid #e2e8f0;
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
    border-color: #cbd5e0;
    transform: translateY(-1px);
}

.btn-purchase {
    background: linear-gradient(135deg, #7c3aed 0%, #5b21b6 100%);
    box-shadow: 0 4px 15px rgba(124, 58, 237, 0.3);
    transition: all 0.3s ease;
}

.btn-purchase:hover {
    background: linear-gradient(135deg, #6d28d9 0%, #4c1d95 100%);
    box-shadow: 0 6px 20px rgba(124, 58, 237, 0.4);
    transform: translateY(-2px);
}

.btn-purchase:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(124, 58, 237, 0.3);
}

/* Loading Animation */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Privacy Protection Badge */
.privacy-badge {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    padding: 12px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

/* Modal Animation */
@keyframes modalFadeIn {
    from {
        opacity: 0;
        backdrop-filter: blur(0px);
    }
    to {
        opacity: 1;
        backdrop-filter: blur(8px);
    }
}

@keyframes modalSlideUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-enter {
    animation: modalFadeIn 0.3s ease-out;
}

.modal-content-enter {
    animation: modalSlideUp 0.3s ease-out;
}

/* Responsive Design */
@media (max-width: 640px) {
    .modal-container {
        margin: 1rem;
        max-height: 95vh;
    }
    
    .period-card {
        padding: 1rem;
    }
    
    .btn-purchase,
    .btn-cancel {
        padding: 12px 16px;
        font-size: 14px;
    }
}

/* Arabic RTL Support */
[dir="rtl"] .modal-container {
    text-align: right;
}

[dir="rtl"] .custom-radio {
    margin-left: 12px;
    margin-right: 0;
}

/* Accessibility */
.period-card:focus-within {
    outline: 2px solid #7c3aed;
    outline-offset: 2px;
}

.btn-purchase:focus,
.btn-cancel:focus {
    outline: 2px solid #7c3aed;
    outline-offset: 2px;
}

/* Modal specific styling to match the new design */
.modal-container {
    max-width: 480px;
    font-family: 'Cairo', sans-serif;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    background: white;
}

.modal-container h3 {
    font-size: 18px;
    font-weight: 700;
    line-height: 1.3;
    color: #333;
}

.modal-container .text-sm {
    font-size: 14px;
    line-height: 1.4;
    color: #333;
}

/* تصميم الخطط المحسن */
.plan {
    border: 2px solid #d1d5db;
    border-radius: 12px;
    padding: 12px;
    margin-bottom: 12px;
    position: relative;
    cursor: pointer;
    background: white;
    transition: all 0.3s ease;
    font-size: 14px;
}

.plan:hover {
    border-color: #7c3aed;
}

.plan.selected {
    border-color: #7c3aed;
    background: #f5f3ff;
}

.plan input[type="radio"] {
    margin-left: 10px;
    width: 16px;
    height: 16px;
    accent-color: #7c3aed;
}

/* شارة الأكثر شيوعاً */
.plan .absolute {
    position: absolute;
    right: 16px;
    top: 8px;
    background: #f87171;
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
}

/* تصميم حماية الخصوصية */
.bg-teal-50 {
    background: #e6fffa !important;
    color: #065f46 !important;
    font-size: 14px;
}

/* تصميم طريقة الدفع */
.bg-gray-100 {
    background: #f3f4f6 !important;
    font-size: 14px;
}

/* تصميم ملخص الأسعار */
.text-gray-600 {
    color: #4b5563;
}

.text-gray-800 {
    color: #111827;
}

/* تصميم زر الدفع */
.btn-pay {
    background: #7c3aed;
    color: white;
    width: 100%;
    border: none;
    padding: 12px;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.btn-pay:hover {
    background: #6d28d9;
}

/* Radio button styling */
.period-card input[type="radio"] {
    width: 18px;
    height: 18px;
    accent-color: #8b5cf6;
}

/* Payment method dropdown styling */
select[name="payment_method"] {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    padding: 12px 16px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
}

select[name="payment_method"]:focus {
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

/* Pricing summary styling */
.bg-gray-50 {
    background: #f9fafb;
    border: 1px solid #e5e7eb;
}

/* Button styling improvements */
#purchaseBtn {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    font-weight: 600;
    font-size: 15px;
    padding: 12px 24px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

#purchaseBtn:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #6d28d9 100%);
    transform: translateY(-1px);
    box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
}

/* Cancel button styling */
button[onclick="closeDomainPurchaseModal()"] {
    font-weight: 500;
    font-size: 15px;
    padding: 12px 24px;
    border: 2px solid #d1d5db;
    border-radius: 8px;
    transition: all 0.3s ease;
}

button[onclick="closeDomainPurchaseModal()"]:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

/* Privacy protection styling */
.bg-green-50 {
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
    border: 1px solid #a7f3d0;
}

.text-green-800 {
    color: #065f46;
    font-weight: 500;
    font-size: 14px;
}

.period-card .font-bold {
    font-size: 14px;
    font-weight: 700;
}

/* Country Select Styling */
select {
    background-image: none;
    padding-right: 40px;
}

/* Override radio button styling for modal */
.period-card input[type="radio"] {
    width: 16px;
    height: 16px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
}

.period-card input[type="radio"]:checked {
    border-color: #7c3aed;
    background-color: #7c3aed;
}

.period-card input[type="radio"]:checked::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    background-color: white;
    border-radius: 50%;
}

/* Pricing Summary Override */
.bg-gray-50 {
    background-color: #f9fafb !important;
    border: 1px solid #e5e7eb !important;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .modal-container {
        max-width: 95%;
        margin: 1rem;
    }

    .modal-container h3 {
        font-size: 18px;
    }

    .period-card {
        padding: 12px;
    }

    .pricing-summary {
        padding: 1rem;
    }

    .btn-purchase,
    .btn-cancel {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

/* Dark mode support (if needed) */
@media (prefers-color-scheme: dark) {
    .modal-container {
        background-color: #1f2937;
        color: #f9fafb;
    }

    .period-card {
        background-color: #374151;
        border-color: #4b5563;
    }

    .pricing-summary {
        background: linear-gradient(135deg, #374151 0%, #4b5563 100%);
    }
}
