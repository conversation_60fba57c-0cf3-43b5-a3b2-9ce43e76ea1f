/**
 * Image Optimizer for Meta Tech Website
 * Handles lazy loading, WebP support, and responsive images
 */

class ImageOptimizer {
    constructor() {
        this.supportsWebP = false;
        this.supportsAvif = false;
        this.init();
    }

    async init() {
        // Check format support
        await this.checkFormatSupport();
        
        // Initialize lazy loading
        this.initLazyLoading();
        
        // Setup responsive images
        this.setupResponsiveImages();
        
        // Preload critical images
        this.preloadCriticalImages();
    }

    async checkFormatSupport() {
        // Check WebP support
        this.supportsWebP = await this.checkWebPSupport();
        
        // Check AVIF support
        this.supportsAvif = await this.checkAvifSupport();
        
        // Add classes to document
        document.documentElement.classList.add(
            this.supportsWebP ? 'webp' : 'no-webp',
            this.supportsAvif ? 'avif' : 'no-avif'
        );
    }

    checkWebPSupport() {
        return new Promise(resolve => {
            const webP = new Image();
            webP.onload = webP.onerror = () => resolve(webP.height === 2);
            webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
        });
    }

    checkAvifSupport() {
        return new Promise(resolve => {
            const avif = new Image();
            avif.onload = avif.onerror = () => resolve(avif.height === 2);
            avif.src = 'data:image/avif;base64,AAAAIGZ0eXBhdmlmAAAAAGF2aWZtaWYxbWlhZk1BMUIAAADybWV0YQAAAAAAAAAoaGRscgAAAAAAAAAAcGljdAAAAAAAAAAAAAAAAGxpYmF2aWYAAAAADnBpdG0AAAAAAAEAAAAeaWxvYwAAAABEAAABAAEAAAABAAABGgAAAB0AAAAoaWluZgAAAAAAAQAAABppbmZlAgAAAAABAABhdjAxQ29sb3IAAAAAamlwcnAAAABLaXBjbwAAABRpc3BlAAAAAAAAAAIAAAACAAAAEHBpeGkAAAAAAwgICAAAAAxhdjFDgQ0MAAAAABNjb2xybmNseAACAAIAAYAAAAAXaXBtYQAAAAAAAAABAAEEAQKDBAAAACVtZGF0EgAKCBgABogQEAwgMg8f8D///8WfhwB8+ErK42A=';
        });
    }

    initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        this.loadImage(entry.target);
                        observer.unobserve(entry.target);
                    }
                });
            }, {
                rootMargin: '50px 0px',
                threshold: 0.01
            });

            // Observe all lazy images
            document.querySelectorAll('img[data-src], img[loading="lazy"]').forEach(img => {
                imageObserver.observe(img);
            });
        } else {
            // Fallback for older browsers
            this.loadAllImages();
        }
    }

    loadImage(img) {
        // Create a new image element for loading
        const imageLoader = new Image();
        
        imageLoader.onload = () => {
            // Image loaded successfully
            this.applyImageSrc(img, imageLoader.src);
            img.classList.remove('lazy');
            img.classList.add('loaded');
            
            // Trigger custom event
            img.dispatchEvent(new CustomEvent('imageLoaded', {
                detail: { src: imageLoader.src }
            }));
        };

        imageLoader.onerror = () => {
            // Handle error
            img.classList.add('error');
            console.warn('Failed to load image:', img.dataset.src || img.src);
        };

        // Determine best format and load
        const src = this.getBestImageSrc(img);
        imageLoader.src = src;
    }

    getBestImageSrc(img) {
        const baseSrc = img.dataset.src || img.src;
        
        // If we have format-specific sources
        if (this.supportsAvif && img.dataset.avif) {
            return img.dataset.avif;
        }
        
        if (this.supportsWebP && img.dataset.webp) {
            return img.dataset.webp;
        }
        
        return baseSrc;
    }

    applyImageSrc(img, src) {
        img.src = src;
        
        // Handle srcset if available
        if (img.dataset.srcset) {
            img.srcset = img.dataset.srcset;
            img.removeAttribute('data-srcset');
        }
        
        // Remove data attributes
        img.removeAttribute('data-src');
        img.removeAttribute('data-webp');
        img.removeAttribute('data-avif');
    }

    setupResponsiveImages() {
        // Handle responsive images based on device pixel ratio
        const pixelRatio = window.devicePixelRatio || 1;
        
        document.querySelectorAll('img[data-sizes]').forEach(img => {
            const sizes = JSON.parse(img.dataset.sizes);
            const appropriateSize = this.getAppropriateSize(sizes, pixelRatio);
            
            if (appropriateSize) {
                img.dataset.src = appropriateSize;
            }
        });
    }

    getAppropriateSize(sizes, pixelRatio) {
        // Sort sizes by width
        const sortedSizes = Object.keys(sizes).sort((a, b) => parseInt(a) - parseInt(b));
        
        // Get viewport width
        const viewportWidth = window.innerWidth * pixelRatio;
        
        // Find appropriate size
        for (let i = sortedSizes.length - 1; i >= 0; i--) {
            const width = parseInt(sortedSizes[i]);
            if (viewportWidth >= width) {
                return sizes[sortedSizes[i]];
            }
        }
        
        // Return smallest size as fallback
        return sizes[sortedSizes[0]];
    }

    preloadCriticalImages() {
        // Preload images that are critical for LCP
        const criticalImages = document.querySelectorAll('img[data-critical]');
        
        criticalImages.forEach(img => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.as = 'image';
            link.href = this.getBestImageSrc(img);
            
            // Add to head
            document.head.appendChild(link);
            
            // Load immediately
            this.loadImage(img);
        });
    }

    loadAllImages() {
        // Fallback method for browsers without IntersectionObserver
        document.querySelectorAll('img[data-src]').forEach(img => {
            this.loadImage(img);
        });
    }

    // Utility method to compress images on the client side
    compressImage(file, quality = 0.8, maxWidth = 1920) {
        return new Promise((resolve) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            
            img.onload = () => {
                // Calculate new dimensions
                const ratio = Math.min(maxWidth / img.width, maxWidth / img.height);
                canvas.width = img.width * ratio;
                canvas.height = img.height * ratio;
                
                // Draw and compress
                ctx.drawImage(img, 0, 0, canvas.width, canvas.height);
                
                canvas.toBlob(resolve, 'image/jpeg', quality);
            };
            
            img.src = URL.createObjectURL(file);
        });
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new ImageOptimizer();
    });
} else {
    new ImageOptimizer();
}

// Export for use in other scripts
window.ImageOptimizer = ImageOptimizer;
