{"__meta": {"id": "X376056fbc47350e57d8dee073156bab0", "datetime": "2025-07-26 02:39:55", "utime": **********.291261, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[02:39:55] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp2\\htdocs\\‏‏Meta\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.229034, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753497594.965762, "end": **********.291281, "duration": 0.3255190849304199, "duration_str": "326ms", "measures": [{"label": "Booting", "start": 1753497594.965762, "relative_start": 0, "end": **********.211866, "relative_end": **********.211866, "duration": 0.24610400199890137, "duration_str": "246ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.211879, "relative_start": 0.*****************, "end": **********.291283, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "79.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#507\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#505 …}\n  file: \"C:\\xampp2\\htdocs\\‏‏Meta\\routes\\web.php\"\n  line: \"21 to 23\"\n}", "namespace": null, "prefix": "", "where": [], "as": "home", "file": "<a href=\"phpstorm://open?file=C:\\xampp2\\htdocs\\‏‏Meta\\routes\\web.php&line=21\">\\routes\\web.php:21-23</a>"}, "queries": {"nb_statements": 3, "nb_failed_statements": 0, "accumulated_duration": 0.010929999999999999, "accumulated_duration_str": "10.93ms", "statements": [{"sql": "select * from \"sessions\" where \"id\" = 'W5pujRwbhJefb8rarjo8hVEg0JKPiBgbFM1ME2UW' limit 1", "type": "query", "params": [], "bindings": ["W5pujRwbhJefb8rarjo8hVEg0JKPiBgbFM1ME2UW"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 100}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 87}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "duration": 0.00064, "duration_str": "640μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php:100", "connection": "C:\\xampp2\\htdocs\\‏‏Meta\\database\\database.sqlite", "start_percent": 0, "width_percent": 5.855}, {"sql": "update \"sessions\" set \"payload\" = 'YTozOntzOjY6Il90b2tlbiI7czo0MDoia3AyNDdsMlJMVlNvUXRyc1pvb05OUVVXQnhVN01YT3oyN2FrYk5TMyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', \"last_activity\" = **********, \"user_id\" = '', \"ip_address\" = '127.0.0.1', \"user_agent\" = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********' where \"id\" = 'W5pujRwbhJefb8rarjo8hVEg0JKPiBgbFM1ME2UW'", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoia3AyNDdsMlJMVlNvUXRyc1pvb05OUVVXQnhVN01YT3oyN2FrYk5TMyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=", "**********", "", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********", "W5pujRwbhJefb8rarjo8hVEg0JKPiBgbFM1ME2UW"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 177}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 144}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 128}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 236}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "duration": 0.0101, "duration_str": "10.1ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php:177", "connection": "C:\\xampp2\\htdocs\\‏‏Meta\\database\\database.sqlite", "start_percent": 5.855, "width_percent": 92.406}, {"sql": "select * from \"sessions\" where \"id\" = 'W5pujRwbhJefb8rarjo8hVEg0JKPiBgbFM1ME2UW' limit 1", "type": "query", "params": [], "bindings": ["W5pujRwbhJefb8rarjo8hVEg0JKPiBgbFM1ME2UW"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 100}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 87}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 71}, {"index": 20, "namespace": null, "name": "\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DebugBar.php", "line": 446}], "duration": 0.00019, "duration_str": "190μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php:100", "connection": "C:\\xampp2\\htdocs\\‏‏Meta\\database\\database.sqlite", "start_percent": 98.262, "width_percent": 1.738}]}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "kp247l2RLVSoQtrsZooNNQUWBxU7MXOz27akbNS3", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-1974729973 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1974729973\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1154207194 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1154207194\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-92036518 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-92036518\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-158297898 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1285 characters\">_ga=GA1.1.8662866.1751666474; _ga_69MPZE94D5=GS2.1.s1751666473$o1$g0$t1751666495$j38$l0$h0; _ga_968CT9KM7N=GS2.1.s1752007871$o1$g1$t1752007920$j11$l0$h0; mgmoaa_abrahym_alahmdy_alymny_session=eyJpdiI6IjBiTldDNXZtMS9JWklzbFdFeGk5RHc9PSIsInZhbHVlIjoiaWtjcjRlZ0ZRNTRlSm9OVlhyeTRwOHR3RW5KV0xSZVlHNGdQdDFtM2NRMEROa3MrR1RUaDBCYW04Z3k2L3JaZ3FXS29TNlIwY0orbkZzM0k4TWFjZFFVOWJTcXpzWmxkQVZuY2M5OE55QTIxV2xJMDRNNmZLK3RYSkZjQS85emYiLCJtYWMiOiIwMmI1MTUxNzA5N2ZiMzRkMDFmODU3M2U0ODliYmM1ZmE2ZDdlZWMzNjVmNTZlMmNkMjZiY2FmYWNkOTZiNzNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ind5T0YxRmRWSWtTNElPR1l0c3lVS3c9PSIsInZhbHVlIjoiMDhoL0dwM3NCamdJSlVkMXhFUVJ5WXdYZDl5MDF4cFVHUWVONUZwY2VxWnhYYzZpYU9qNC8rMUI3RUpRQk05eE9ud0tiZ1V2cU9Fc2hxUUFSVE9MSWxjQW9GaXB0clprVnJXOWJ0SldIdXpuQ3d3NVN0dW9GZXFiQ08rZjl5b08iLCJtYWMiOiI2ZTNiOGMzNWY1N2VlYmE2Zjk0N2IyNmRlNDdiYTFmZjJlOWNhZGQ5MmMzOTEwODI1OGUxNDk1ZThkMDEyMzc2IiwidGFnIjoiIn0%3D; mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session=eyJpdiI6IlhOUzVkUkhJWE5ocllTRG5BYVJyaGc9PSIsInZhbHVlIjoibi9qU05uNHJIenRkQmJtQUdqTU1xUjJDTlNUdERCNGNkVVRuaXVkaUxQSjBzeTN1MStwc1gxUE9HK0hMK3VpN1EyRCswbnk3bEloVzZaWUhpY0Z3ZzlPeWJ5ZmZDcndKVmt4cUhjT3JuejhvTFo3WEdNZU5wZnFFTFdpakNuRzAiLCJtYWMiOiJmNTc0ZjQyNDM0YzNjZmJmMjkwZmQyYjZlM2M5YmRhNmQ4Nzg0MmQ3ZGZlMzU0NDEwOWMxMzM1NTM4ZWRlMDI1IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-158297898\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1432445655 data-indent-pad=\"  \"><span class=sf-dump-note>array:30</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"30 characters\">C:\\xampp2\\htdocs\\&#8207;&#8207;Meta\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60650</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C:\\xampp2\\htdocs\\&#8207;&#8207;Meta\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA</span>\" => \"<span class=sf-dump-str title=\"65 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Microsoft Edge&quot;;v=&quot;138&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_MOBILE</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_CH_UA_PLATFORM</span>\" => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_SITE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_MODE</span>\" => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_USER</span>\" => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  \"<span class=sf-dump-key>HTTP_SEC_FETCH_DEST</span>\" => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"1285 characters\">_ga=GA1.1.8662866.1751666474; _ga_69MPZE94D5=GS2.1.s1751666473$o1$g0$t1751666495$j38$l0$h0; _ga_968CT9KM7N=GS2.1.s1752007871$o1$g1$t1752007920$j11$l0$h0; mgmoaa_abrahym_alahmdy_alymny_session=eyJpdiI6IjBiTldDNXZtMS9JWklzbFdFeGk5RHc9PSIsInZhbHVlIjoiaWtjcjRlZ0ZRNTRlSm9OVlhyeTRwOHR3RW5KV0xSZVlHNGdQdDFtM2NRMEROa3MrR1RUaDBCYW04Z3k2L3JaZ3FXS29TNlIwY0orbkZzM0k4TWFjZFFVOWJTcXpzWmxkQVZuY2M5OE55QTIxV2xJMDRNNmZLK3RYSkZjQS85emYiLCJtYWMiOiIwMmI1MTUxNzA5N2ZiMzRkMDFmODU3M2U0ODliYmM1ZmE2ZDdlZWMzNjVmNTZlMmNkMjZiY2FmYWNkOTZiNzNiIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ind5T0YxRmRWSWtTNElPR1l0c3lVS3c9PSIsInZhbHVlIjoiMDhoL0dwM3NCamdJSlVkMXhFUVJ5WXdYZDl5MDF4cFVHUWVONUZwY2VxWnhYYzZpYU9qNC8rMUI3RUpRQk05eE9ud0tiZ1V2cU9Fc2hxUUFSVE9MSWxjQW9GaXB0clprVnJXOWJ0SldIdXpuQ3d3NVN0dW9GZXFiQ08rZjl5b08iLCJtYWMiOiI2ZTNiOGMzNWY1N2VlYmE2Zjk0N2IyNmRlNDdiYTFmZjJlOWNhZGQ5MmMzOTEwODI1OGUxNDk1ZThkMDEyMzc2IiwidGFnIjoiIn0%3D; mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session=eyJpdiI6IlhOUzVkUkhJWE5ocllTRG5BYVJyaGc9PSIsInZhbHVlIjoibi9qU05uNHJIenRkQmJtQUdqTU1xUjJDTlNUdERCNGNkVVRuaXVkaUxQSjBzeTN1MStwc1gxUE9HK0hMK3VpN1EyRCswbnk3bEloVzZaWUhpY0Z3ZzlPeWJ5ZmZDcndKVmt4cUhjT3JuejhvTFo3WEdNZU5wZnFFTFdpakNuRzAiLCJtYWMiOiJmNTc0ZjQyNDM0YzNjZmJmMjkwZmQyYjZlM2M5YmRhNmQ4Nzg0MmQ3ZGZlMzU0NDEwOWMxMzM1NTM4ZWRlMDI1IiwidGFnIjoiIn0%3D</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753497594.9658</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753497594</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1432445655\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-266057075 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_ga</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_69MPZE94D5</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>_ga_968CT9KM7N</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>mgmoaa_abrahym_alahmdy_alymny_session</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kp247l2RLVSoQtrsZooNNQUWBxU7MXOz27akbNS3</span>\"\n  \"<span class=sf-dump-key>mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">W5pujRwbhJefb8rarjo8hVEg0JKPiBgbFM1ME2UW</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-266057075\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-216049982 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 02:39:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjkzdTVXcTRaZ2Jya2h5MGI1eGdOeVE9PSIsInZhbHVlIjoiaDR5YklyVE5CdlV0SFloTUtGV0hOZWEwQTRENHBLK2U2WmdtQmFHZ2c5U3hldCtUZHVLaWRmekdmWTlmRmI0WnphWndKZFYwNk1obE0yR0xNVmt4SWhiY0pKN3UwMnZVMUowVHJsMjRnZlBBNW1KQ3VhMHJEK3VXR09RMU8zWWYiLCJtYWMiOiI3ZGVkZWEzZDUxMmRlM2JhMjlkOTkwNmQ2YTZkYmYwNTRkYmMzYzU1MDhmNjM3Njg2Y2UyYTc0YTQ4ZDk0YTBiIiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:39:55 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"479 characters\">mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session=eyJpdiI6InA1VkFZMHJvZFdVMWtIOXMxYm1vWVE9PSIsInZhbHVlIjoiUWxkZGRiRUhTOHpVWndWV25taDBITXlFczNiUXNUODlxTzVVdmZtRU5TU2ttV3oxZHV1V3F2dGJkQ0xnSkpuYy9JNy82bElEVEgxdWo1WUhSQkFuZE1TZ3R6UkZkK2JMNnhpMTk2Rm5leDVMb0lYSmo1cjRVYUh6dlZXc1BDai8iLCJtYWMiOiJmODM0YTZmYWE3YTdlNTI5ZmI5YzBjNWIwMmY1ZmRhYjA3YWFhNGRmMGNmYmZhNzY4OGVhMGRhOTA5ODNkNmU3IiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:39:55 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjkzdTVXcTRaZ2Jya2h5MGI1eGdOeVE9PSIsInZhbHVlIjoiaDR5YklyVE5CdlV0SFloTUtGV0hOZWEwQTRENHBLK2U2WmdtQmFHZ2c5U3hldCtUZHVLaWRmekdmWTlmRmI0WnphWndKZFYwNk1obE0yR0xNVmt4SWhiY0pKN3UwMnZVMUowVHJsMjRnZlBBNW1KQ3VhMHJEK3VXR09RMU8zWWYiLCJtYWMiOiI3ZGVkZWEzZDUxMmRlM2JhMjlkOTkwNmQ2YTZkYmYwNTRkYmMzYzU1MDhmNjM3Njg2Y2UyYTc0YTQ4ZDk0YTBiIiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:39:55 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session=eyJpdiI6InA1VkFZMHJvZFdVMWtIOXMxYm1vWVE9PSIsInZhbHVlIjoiUWxkZGRiRUhTOHpVWndWV25taDBITXlFczNiUXNUODlxTzVVdmZtRU5TU2ttV3oxZHV1V3F2dGJkQ0xnSkpuYy9JNy82bElEVEgxdWo1WUhSQkFuZE1TZ3R6UkZkK2JMNnhpMTk2Rm5leDVMb0lYSmo1cjRVYUh6dlZXc1BDai8iLCJtYWMiOiJmODM0YTZmYWE3YTdlNTI5ZmI5YzBjNWIwMmY1ZmRhYjA3YWFhNGRmMGNmYmZhNzY4OGVhMGRhOTA5ODNkNmU3IiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:39:55 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-216049982\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-355674787 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">kp247l2RLVSoQtrsZooNNQUWBxU7MXOz27akbNS3</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-355674787\", {\"maxDepth\":0})</script>\n"}}