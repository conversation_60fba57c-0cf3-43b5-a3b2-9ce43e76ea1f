<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\Admin;

class RoleController extends Controller
{
    public function __construct()
    {
        $this->middleware('admin.auth');
    }

    /**
     * Display a listing of roles.
     */
    public function index()
    {
        $roles = Role::where('guard_name', 'admin')
                    ->withCount(['permissions', 'users'])
                    ->paginate(15);

        $stats = [
            'total_roles' => Role::where('guard_name', 'admin')->count(),
            'total_permissions' => Permission::where('guard_name', 'admin')->count(),
            'total_admins' => Admin::count(),
        ];

        return view('admin.roles.index', compact('roles', 'stats'));
    }

    /**
     * Show the form for creating a new role.
     */
    public function create()
    {
        $permissions = Permission::where('guard_name', 'admin')
                                ->get()
                                ->groupBy(function($permission) {
                                    return explode('-', $permission->name)[1] ?? 'other';
                                });

        return view('admin.roles.create', compact('permissions'));
    }

    /**
     * Store a newly created role in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        $role = Role::create([
            'name' => $request->name,
            'guard_name' => 'admin',
            'display_name' => $request->display_name,
            'description' => $request->description,
        ]);

        if ($request->has('permissions')) {
            $role->syncPermissions($request->permissions);
        }

        return redirect()->route('admin.roles.index')
            ->with('success', 'تم إنشاء الدور بنجاح');
    }

    /**
     * Display the specified role.
     */
    public function show(Role $role)
    {
        $role->load(['permissions', 'users']);
        
        $roleStats = [
            'total_permissions' => $role->permissions->count(),
            'total_users' => $role->users->count(),
            'created_at' => $role->created_at,
            'updated_at' => $role->updated_at,
        ];

        return view('admin.roles.show', compact('role', 'roleStats'));
    }

    /**
     * Show the form for editing the specified role.
     */
    public function edit(Role $role)
    {
        $permissions = Permission::where('guard_name', 'admin')
                                ->get()
                                ->groupBy(function($permission) {
                                    return explode('-', $permission->name)[1] ?? 'other';
                                });

        $rolePermissions = $role->permissions->pluck('name')->toArray();

        return view('admin.roles.edit', compact('role', 'permissions', 'rolePermissions'));
    }

    /**
     * Update the specified role in storage.
     */
    public function update(Request $request, Role $role)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        $role->update([
            'name' => $request->name,
            'display_name' => $request->display_name,
            'description' => $request->description,
        ]);

        $role->syncPermissions($request->permissions ?? []);

        return redirect()->route('admin.roles.show', $role)
            ->with('success', 'تم تحديث الدور بنجاح');
    }

    /**
     * Remove the specified role from storage.
     */
    public function destroy(Role $role)
    {
        // Prevent deletion of Super Admin role
        if ($role->name === 'Super Admin') {
            return back()->with('error', 'لا يمكن حذف دور المدير العام');
        }

        // Check if role has users
        if ($role->users()->count() > 0) {
            return back()->with('error', 'لا يمكن حذف دور مرتبط بمستخدمين');
        }

        $role->delete();

        return redirect()->route('admin.roles.index')
            ->with('success', 'تم حذف الدور بنجاح');
    }

    /**
     * Display permissions management.
     */
    public function permissions()
    {
        $permissions = Permission::where('guard_name', 'admin')
                                ->get()
                                ->groupBy(function($permission) {
                                    return explode('-', $permission->name)[1] ?? 'other';
                                });

        $stats = [
            'total_permissions' => Permission::where('guard_name', 'admin')->count(),
            'total_roles' => Role::where('guard_name', 'admin')->count(),
        ];

        return view('admin.roles.permissions', compact('permissions', 'stats'));
    }

    /**
     * Create new permission.
     */
    public function createPermission(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:permissions,name',
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
            'group' => 'required|string|max:100',
        ]);

        Permission::create([
            'name' => $request->name,
            'guard_name' => 'admin',
            'display_name' => $request->display_name,
            'description' => $request->description,
            'group' => $request->group,
        ]);

        return back()->with('success', 'تم إنشاء الصلاحية بنجاح');
    }

    /**
     * Update permission.
     */
    public function updatePermission(Request $request, Permission $permission)
    {
        $request->validate([
            'display_name' => 'required|string|max:255',
            'description' => 'nullable|string|max:500',
        ]);

        $permission->update([
            'display_name' => $request->display_name,
            'description' => $request->description,
        ]);

        return back()->with('success', 'تم تحديث الصلاحية بنجاح');
    }

    /**
     * Delete permission.
     */
    public function deletePermission(Permission $permission)
    {
        // Check if permission is assigned to any role
        if ($permission->roles()->count() > 0) {
            return back()->with('error', 'لا يمكن حذف صلاحية مرتبطة بأدوار');
        }

        $permission->delete();

        return back()->with('success', 'تم حذف الصلاحية بنجاح');
    }

    /**
     * Assign role to admin.
     */
    public function assignRole(Request $request)
    {
        $request->validate([
            'admin_id' => 'required|exists:admins,id',
            'role_id' => 'required|exists:roles,id',
        ]);

        $admin = Admin::findOrFail($request->admin_id);
        $role = Role::findOrFail($request->role_id);

        $admin->assignRole($role);

        return back()->with('success', 'تم تعيين الدور بنجاح');
    }

    /**
     * Remove role from admin.
     */
    public function removeRole(Request $request)
    {
        $request->validate([
            'admin_id' => 'required|exists:admins,id',
            'role_id' => 'required|exists:roles,id',
        ]);

        $admin = Admin::findOrFail($request->admin_id);
        $role = Role::findOrFail($request->role_id);

        // Prevent removing Super Admin role from the last Super Admin
        if ($role->name === 'Super Admin' && $role->users()->count() <= 1) {
            return back()->with('error', 'لا يمكن إزالة دور المدير العام من آخر مدير عام');
        }

        $admin->removeRole($role);

        return back()->with('success', 'تم إزالة الدور بنجاح');
    }

    /**
     * Get role data for AJAX.
     */
    public function getRoleData(Role $role)
    {
        $role->load(['permissions', 'users']);
        
        return response()->json([
            'role' => $role,
            'permissions' => $role->permissions->pluck('name'),
            'users_count' => $role->users->count(),
        ]);
    }

    /**
     * Bulk assign permissions to role.
     */
    public function bulkAssignPermissions(Request $request, Role $role)
    {
        $request->validate([
            'permissions' => 'array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        $role->syncPermissions($request->permissions ?? []);

        return response()->json([
            'success' => true,
            'message' => 'تم تحديث الصلاحيات بنجاح'
        ]);
    }
}
