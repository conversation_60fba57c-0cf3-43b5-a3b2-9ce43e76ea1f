@extends('layouts.meta-dashboard')

@section('title', 'لوحة التحكم - ميتاء تك')

@section('styles')
<style>
/* بطاقات المؤشرات الرئيسية */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 24px;
    margin-bottom: 32px;
}

.stat-card {
    background: #ffffff;
    border-radius: 20px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    border: none;
    transition: all var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.stat-card.orange {
    background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    color: white;
}

.stat-card.blue {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.stat-card.purple {
    background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%);
    color: white;
}

.stat-card.red {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.stat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    color: white;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.stat-value {
    font-size: 32px;
    font-weight: 700;
    color: inherit;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 14px;
    color: inherit;
    opacity: 0.9;
    margin-bottom: 12px;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 6px;
    font-size: 14px;
    font-weight: 500;
    color: inherit;
    opacity: 0.9;
    background: rgba(255, 255, 255, 0.2);
    padding: 4px 8px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
}

/* شبكة المحتوى */
.content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 32px;
}

.chart-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 24px;
    box-shadow: var(--shadow-lg);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.chart-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--gray-800);
}

.chart-container {
    height: 300px;
    position: relative;
}

/* بطاقات الحسابات */
.accounts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

.balance-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    padding: 24px;
    color: white;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
}

.balance-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    pointer-events: none;
}

.balance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.balance-type {
    font-size: 14px;
    opacity: 0.8;
}

.balance-amount {
    font-size: 28px;
    font-weight: 700;
    margin-bottom: 16px;
}

.card-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.2);
}

.card-holder {
    font-size: 14px;
    opacity: 0.9;
}

.card-expiry {
    font-size: 12px;
    opacity: 0.7;
}

/* التصميم المتجاوب */
@media (max-width: 1200px) {
    .content-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .accounts-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card,
    .chart-card,
    .balance-card {
        padding: 20px;
    }
    
    .stat-value {
        font-size: 28px;
    }
}
</style>
@endsection

@section('content')
<!-- بطاقات المؤشرات الرئيسية -->
<div class="stats-grid">
    <!-- الرصيد العام (BTC) -->
    <div class="stat-card" data-stat="btc_balance" style="background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%); color: white;">
        <div class="stat-header">
            <div class="stat-icon">
                <i class="fab fa-bitcoin"></i>
            </div>
            <div class="stat-change">
                <i class="fas fa-arrow-{{ str_contains($stats['btc_balance']['change'], '+') ? 'up' : 'down' }}"></i>
                <span>{{ $stats['btc_balance']['change'] }}% {{ $stats['btc_balance']['period'] }}</span>
            </div>
        </div>
        <div class="stat-value">{{ $stats['btc_balance']['value'] }}</div>
        <div class="stat-label">الرصيد العام (BTC)</div>
    </div>

    <!-- الأرباح الحالية -->
    <div class="stat-card" data-stat="current_earnings" style="background: linear-gradient(135deg, #a855f7 0%, #7c3aed 100%); color: white;">
        <div class="stat-header">
            <div class="stat-icon">
                <i class="fas fa-chart-line"></i>
            </div>
            <div class="stat-change">
                <i class="fas fa-arrow-{{ str_contains($stats['current_earnings']['change'], '+') ? 'up' : 'down' }}"></i>
                <span>{{ $stats['current_earnings']['change'] }}% {{ $stats['current_earnings']['period'] }}</span>
            </div>
        </div>
        <div class="stat-value">{{ $stats['current_earnings']['value'] }}</div>
        <div class="stat-label">الأرباح الحالية</div>
    </div>

    <!-- إجمالي الأصول الرقمية -->
    <div class="stat-card" data-stat="digital_assets" style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white;">
        <div class="stat-header">
            <div class="stat-icon">
                <i class="fas fa-coins"></i>
            </div>
            <div class="stat-change">
                <i class="fas fa-arrow-{{ str_contains($stats['digital_assets']['change'], '+') ? 'up' : 'down' }}"></i>
                <span>{{ $stats['digital_assets']['change'] }}%</span>
            </div>
        </div>
        <div class="stat-value">{{ $stats['digital_assets']['value'] }}</div>
        <div class="stat-label">إجمالي الأصول الرقمية</div>
    </div>

    <!-- النفقات هذا الأسبوع -->
    <div class="stat-card" data-stat="weekly_expenses" style="background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%); color: white;">
        <div class="stat-header">
            <div class="stat-icon">
                <i class="fas fa-credit-card"></i>
            </div>
            <div class="stat-change">
                <i class="fas fa-arrow-{{ str_contains($stats['weekly_expenses']['change'], '+') ? 'up' : 'down' }}"></i>
                <span>{{ $stats['weekly_expenses']['change'] }}% {{ $stats['weekly_expenses']['period'] }}</span>
            </div>
        </div>
        <div class="stat-value">{{ $stats['weekly_expenses']['value'] }}</div>
        <div class="stat-label">النفقات هذا الأسبوع</div>
    </div>
</div>

<!-- الرسوم البيانية -->
<div class="content-grid">
    <!-- الإحصائيات الحالية -->
    <div class="chart-card">
        <div class="chart-header">
            <h3 class="chart-title">الإحصائيات الحالية</h3>
        </div>
        <div class="chart-container">
            <canvas id="currentStatsChart"></canvas>
        </div>
    </div>

    <!-- نظرة على السوق -->
    <div class="chart-card">
        <div class="chart-header">
            <h3 class="chart-title">نظرة على السوق</h3>
            <div class="market-filters">
                <button class="market-filter active" data-crypto="ETH">ETH</button>
                <button class="market-filter" data-crypto="XMR">XMR</button>
                <button class="market-filter" data-crypto="LTC">LTC</button>
                <button class="market-filter" data-crypto="XRP">XRP</button>
            </div>
        </div>
        <div class="chart-container">
            <canvas id="marketOverviewChart"></canvas>
        </div>
    </div>
</div>

<!-- بطاقات الحسابات -->
<div class="accounts-grid">
    @php
        $cardColors = [
            'linear-gradient(135deg, #ff6b35 0%, #f7931e 100%)', // برتقالي
            'linear-gradient(135deg, #a855f7 0%, #7c3aed 100%)', // بنفسجي
            'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)', // أزرق
            'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)'  // بنفسجي فاتح
        ];
    @endphp

    @foreach($accounts as $index => $account)
    <div class="balance-card" style="background: {{ $cardColors[$index % 4] }};">
        <div class="balance-header">
            <div class="balance-type">{{ $account['type'] }}</div>
            <i class="fas fa-credit-card"></i>
        </div>
        <div class="balance-amount">{{ $account['amount'] }}</div>
        <div class="card-details">
            <div>
                <div class="card-holder">{{ $account['holder'] }}</div>
                <div class="card-expiry">{{ $account['expiry'] }}</div>
            </div>
            <div>
                <div class="card-holder">CARD HOLDER</div>
                <div class="card-expiry">VALID THRU</div>
            </div>
        </div>
    </div>
    @endforeach
</div>
@endsection

@section('scripts')
<style>
.market-filters {
    display: flex;
    gap: 8px;
}

.market-filter {
    padding: 6px 12px;
    border: 1px solid var(--gray-300);
    border-radius: 8px;
    background: white;
    color: var(--gray-600);
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.market-filter.active,
.market-filter:hover {
    background: var(--primary-blue);
    color: white;
    border-color: var(--primary-blue);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // إعداد الرسم البياني الدائري للإحصائيات الحالية
    const currentStatsCtx = document.getElementById('currentStatsChart').getContext('2d');
    const chartData = @json($chartData);

    const currentStatsChart = new Chart(currentStatsCtx, {
        type: 'doughnut',
        data: {
            labels: ['الدخل (60%)', 'المصاريف (20%)', 'الأقساط (15%)', 'الاستثمارات (5%)'],
            datasets: [{
                data: [
                    chartData.current_stats.income,
                    chartData.current_stats.expenses,
                    chartData.current_stats.installments,
                    chartData.current_stats.investments
                ],
                backgroundColor: [
                    '#10b981',
                    '#ef4444',
                    '#f59e0b',
                    '#8b5cf6'
                ],
                borderWidth: 0,
                cutout: '70%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        usePointStyle: true,
                        padding: 20,
                        font: {
                            family: 'Cairo',
                            size: 12
                        }
                    }
                }
            }
        }
    });

    // إعداد الرسم البياني الخطي لنظرة السوق
    const marketCtx = document.getElementById('marketOverviewChart').getContext('2d');
    const marketChart = new Chart(marketCtx, {
        type: 'line',
        data: {
            labels: ['الأسبوع 01', 'الأسبوع 02', 'الأسبوع 03', 'الأسبوع 04', 'الأسبوع 05', 'الأسبوع 06', 'الأسبوع 07', 'الأسبوع 08', 'الأسبوع 09', 'الأسبوع 10'],
            datasets: [{
                label: 'ETH',
                data: chartData.market_data.ETH,
                borderColor: '#3b82f6',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#3b82f6',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6
            }, {
                label: 'XMR',
                data: chartData.market_data.XMR,
                borderColor: '#f59e0b',
                backgroundColor: 'rgba(245, 158, 11, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#f59e0b',
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: '#ffffff',
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false,
                    callbacks: {
                        title: function(context) {
                            return context[0].label;
                        },
                        label: function(context) {
                            return '$' + context.parsed.y + 'K';
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        font: {
                            family: 'Cairo',
                            size: 11
                        },
                        color: '#64748b'
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(0, 0, 0, 0.05)'
                    },
                    ticks: {
                        font: {
                            family: 'Cairo',
                            size: 11
                        },
                        color: '#64748b',
                        callback: function(value) {
                            return '$' + value + 'K';
                        }
                    }
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });

    // معالجة أزرار تصفية السوق
    document.querySelectorAll('.market-filter').forEach(button => {
        button.addEventListener('click', function() {
            // إزالة الفئة النشطة من جميع الأزرار
            document.querySelectorAll('.market-filter').forEach(btn => {
                btn.classList.remove('active');
            });

            // إضافة الفئة النشطة للزر المنقور
            this.classList.add('active');

            // تحديث البيانات حسب العملة المختارة
            const crypto = this.dataset.crypto;
            updateMarketChart(crypto);
        });
    });

    function updateMarketChart(crypto) {
        let newData = chartData.market_data[crypto] || chartData.market_data.ETH;

        marketChart.data.datasets[0].data = newData;
        marketChart.data.datasets[0].label = crypto;
        marketChart.update('active');
    }

    // تأثيرات الرسوم المتحركة للبطاقات
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // مراقبة جميع البطاقات
    document.querySelectorAll('.stat-card, .chart-card, .balance-card').forEach(card => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = 'all 0.6s ease-out';
        observer.observe(card);
    });

    // تأثير النقر على البطاقات
    document.querySelectorAll('.balance-card').forEach(card => {
        card.addEventListener('click', function() {
            this.style.transform = 'scale(0.98)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });

    // تحديث البيانات في الوقت الفعلي
    function startRealTimeUpdates() {
        setInterval(async () => {
            try {
                const response = await fetch('{{ route("admin.meta-dashboard.realtime") }}');
                const data = await response.json();

                if (data.success) {
                    updateStatsCards(data.data);
                }
            } catch (error) {
                console.log('خطأ في تحديث البيانات:', error);
            }
        }, 30000); // كل 30 ثانية
    }

    function updateStatsCards(stats) {
        // تحديث قيم البطاقات
        Object.keys(stats).forEach(key => {
            const card = document.querySelector(`[data-stat="${key}"]`);
            if (card) {
                const valueElement = card.querySelector('.stat-value');
                const changeElement = card.querySelector('.stat-change span');

                if (valueElement) valueElement.textContent = stats[key].value;
                if (changeElement) changeElement.textContent = stats[key].change + '% ' + stats[key].period;
            }
        });
    }

    // بدء التحديثات التلقائية
    startRealTimeUpdates();
});
</script>
@endsection
