<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_domains', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('domain_name');
            $table->string('domain_url')->nullable();
            $table->enum('status', ['نشط', 'غير نشط', 'منتهي الصلاحية', 'معلق'])->default('نشط');
            $table->date('expiry_date')->nullable();
            $table->date('registration_date')->nullable();
            $table->boolean('auto_renewal')->default(false);
            $table->json('nameservers')->nullable();
            $table->json('dns_records')->nullable();
            $table->enum('ssl_status', ['نشط', 'غير نشط', 'منتهي الصلاحية'])->default('غير نشط');
            $table->text('notes')->nullable();
            $table->string('provider')->nullable();
            $table->string('provider_domain_id')->nullable();
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index('expiry_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_domains');
    }
};
