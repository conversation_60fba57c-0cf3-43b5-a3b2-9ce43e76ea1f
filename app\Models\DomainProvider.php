<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class DomainProvider extends Model
{
    protected $fillable = [
        'name',
        'type',
        'api_endpoint',
        'api_key',
        'api_secret',
        'api_config',
        'whois_server',
        'commission_rate',
        'supports_registration',
        'supports_transfer',
        'supports_renewal',
        'is_active',
        'supported_tlds',
        'notes',
    ];

    protected $casts = [
        'api_config' => 'array',
        'supported_tlds' => 'array',
        'commission_rate' => 'decimal:2',
        'supports_registration' => 'boolean',
        'supports_transfer' => 'boolean',
        'supports_renewal' => 'boolean',
        'is_active' => 'boolean',
    ];

    public function domains(): HasMany
    {
        return $this->hasMany(Domain::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }
}
