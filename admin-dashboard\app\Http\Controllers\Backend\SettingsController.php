<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class SettingsController extends Controller
{
    /**
     * Display the settings page.
     */
    public function index()
    {
        return view('backend.settings.index');
    }

    /**
     * Update system settings.
     */
    public function update(Request $request)
    {
        try {
            $settings = $request->all();
            
            // Validate settings
            $this->validateSettings($settings);
            
            // Save settings to database or config
            foreach ($settings as $key => $value) {
                $this->saveSetting($key, $value);
            }
            
            // Clear cache after updating settings
            Cache::flush();
            
            return response()->json([
                'success' => true,
                'message' => 'تم حفظ الإعدادات بنجاح'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Settings update failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حفظ الإعدادات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Test email configuration.
     */
    public function testEmail(Request $request)
    {
        try {
            $emailSettings = $request->all();
            
            // Temporarily update mail configuration
            config([
                'mail.mailers.smtp.host' => $emailSettings['mail_host'],
                'mail.mailers.smtp.port' => $emailSettings['mail_port'],
                'mail.mailers.smtp.encryption' => $emailSettings['mail_encryption'],
                'mail.mailers.smtp.username' => $emailSettings['mail_username'],
                'mail.mailers.smtp.password' => $emailSettings['mail_password'],
                'mail.from.address' => $emailSettings['mail_from_address'],
                'mail.from.name' => $emailSettings['mail_from_name'],
            ]);
            
            // Send test email
            Mail::raw('هذه رسالة تجريبية لاختبار إعدادات البريد الإلكتروني.', function ($message) use ($emailSettings) {
                $message->to($emailSettings['mail_from_address'])
                        ->subject('اختبار إعدادات البريد الإلكتروني');
            });
            
            return response()->json([
                'success' => true,
                'message' => 'تم إرسال الرسالة التجريبية بنجاح'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Email test failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'فشل في إرسال الرسالة التجريبية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear all cache.
     */
    public function clearAllCache()
    {
        try {
            // Clear application cache
            Cache::flush();
            
            // Clear config cache
            Artisan::call('config:clear');
            
            // Clear route cache
            Artisan::call('route:clear');
            
            // Clear view cache
            Artisan::call('view:clear');
            
            // Clear compiled views
            Artisan::call('clear-compiled');
            
            return response()->json([
                'success' => true,
                'message' => 'تم مسح جميع الذاكرة المؤقتة بنجاح'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Cache clear failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء مسح الذاكرة المؤقتة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear system logs.
     */
    public function clearLogs()
    {
        try {
            $logPath = storage_path('logs');
            $files = glob($logPath . '/*.log');
            
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            
            return response()->json([
                'success' => true,
                'message' => 'تم مسح السجلات بنجاح'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Log clear failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء مسح السجلات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Optimize database.
     */
    public function optimizeDatabase()
    {
        try {
            // Get all tables
            $tables = DB::select('SHOW TABLES');
            $databaseName = DB::getDatabaseName();
            $tableColumn = 'Tables_in_' . $databaseName;
            
            foreach ($tables as $table) {
                $tableName = $table->$tableColumn;
                DB::statement("OPTIMIZE TABLE `{$tableName}`");
            }
            
            return response()->json([
                'success' => true,
                'message' => 'تم تحسين قاعدة البيانات بنجاح'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Database optimization failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحسين قاعدة البيانات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Enable maintenance mode.
     */
    public function enableMaintenance(Request $request)
    {
        try {
            $message = $request->input('message', 'الموقع تحت الصيانة');
            
            Artisan::call('down', [
                '--message' => $message,
                '--retry' => 60
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'تم تفعيل وضع الصيانة'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تفعيل وضع الصيانة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Disable maintenance mode.
     */
    public function disableMaintenance()
    {
        try {
            Artisan::call('up');
            
            return response()->json([
                'success' => true,
                'message' => 'تم إلغاء وضع الصيانة'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إلغاء وضع الصيانة: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get maintenance status.
     */
    public function maintenanceStatus()
    {
        $isDown = app()->isDownForMaintenance();
        
        return response()->json([
            'maintenance_mode' => $isDown,
            'status' => $isDown ? 'enabled' : 'disabled'
        ]);
    }

    /**
     * Run artisan command.
     */
    public function runArtisanCommand(Request $request)
    {
        try {
            $command = $request->input('command');
            
            // Security check - only allow safe commands
            $allowedCommands = [
                'cache:clear',
                'config:clear',
                'route:clear',
                'view:clear',
                'queue:restart',
                'storage:link',
                'migrate:status'
            ];
            
            if (!in_array($command, $allowedCommands)) {
                return response()->json([
                    'success' => false,
                    'message' => 'الأمر غير مسموح'
                ], 403);
            }
            
            Artisan::call($command);
            $output = Artisan::output();
            
            return response()->json([
                'success' => true,
                'message' => 'تم تنفيذ الأمر بنجاح',
                'output' => $output
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تنفيذ الأمر: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get PHP info.
     */
    public function phpInfo()
    {
        ob_start();
        phpinfo();
        $phpinfo = ob_get_contents();
        ob_end_clean();
        
        return response($phpinfo)->header('Content-Type', 'text/html');
    }

    /**
     * Get server information.
     */
    public function serverInfo()
    {
        return response()->json([
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_software' => $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown',
            'memory_limit' => ini_get('memory_limit'),
            'max_execution_time' => ini_get('max_execution_time'),
            'upload_max_filesize' => ini_get('upload_max_filesize'),
            'post_max_size' => ini_get('post_max_size'),
            'timezone' => date_default_timezone_get(),
            'disk_free_space' => $this->formatBytes(disk_free_space('/')),
            'disk_total_space' => $this->formatBytes(disk_total_space('/'))
        ]);
    }

    /**
     * Block IP address.
     */
    public function blockIP(Request $request)
    {
        $ip = $request->input('ip');
        
        // Add IP to blocked list (implement your blocking logic)
        // This could be stored in database, cache, or firewall rules
        
        return response()->json([
            'success' => true,
            'message' => "تم حظر عنوان IP: {$ip}"
        ]);
    }

    /**
     * Unblock IP address.
     */
    public function unblockIP(Request $request)
    {
        $ip = $request->input('ip');
        
        // Remove IP from blocked list
        
        return response()->json([
            'success' => true,
            'message' => "تم إلغاء حظر عنوان IP: {$ip}"
        ]);
    }

    /**
     * Get blocked IPs.
     */
    public function getBlockedIPs()
    {
        // Return list of blocked IPs
        return response()->json([
            'blocked_ips' => []
        ]);
    }

    /**
     * Validate settings.
     */
    private function validateSettings($settings)
    {
        // Add validation logic here
        if (isset($settings['mail_port']) && !is_numeric($settings['mail_port'])) {
            throw new \Exception('منفذ البريد الإلكتروني يجب أن يكون رقماً');
        }
        
        if (isset($settings['session_lifetime']) && !is_numeric($settings['session_lifetime'])) {
            throw new \Exception('مدة الجلسة يجب أن تكون رقماً');
        }
    }

    /**
     * Save individual setting.
     */
    private function saveSetting($key, $value)
    {
        // Implement your settings storage logic
        // This could be database, config files, or cache
        Cache::forever("setting.{$key}", $value);
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes($bytes, $precision = 2)
    {
        $units = array('B', 'KB', 'MB', 'GB', 'TB');
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, $precision) . ' ' . $units[$i];
    }
}
