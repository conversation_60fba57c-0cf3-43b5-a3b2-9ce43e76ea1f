<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('email_plans', function (Blueprint $table) {
            // Drop old columns
            $table->dropColumn([
                'mailbox_count',
                'storage_per_mailbox',
                'spam_filter',
                'mobile_access',
                'outlook_support',
                'gmail_integration'
            ]);

            // Add new columns
            $table->string('provider')->after('description_en');
            $table->integer('max_users')->default(0)->after('provider'); // 0 = unlimited
            $table->string('storage_per_user')->after('max_users');
            $table->decimal('commission_rate', 5, 2)->default(0)->after('features');
            $table->json('provider_config')->nullable()->after('sort_order');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('email_plans', function (Blueprint $table) {
            // Add back old columns
            $table->integer('mailbox_count')->after('description_en');
            $table->string('storage_per_mailbox')->after('mailbox_count');
            $table->boolean('spam_filter')->default(true)->after('storage_per_mailbox');
            $table->boolean('mobile_access')->default(true)->after('spam_filter');
            $table->boolean('outlook_support')->default(true)->after('mobile_access');
            $table->boolean('gmail_integration')->default(true)->after('outlook_support');

            // Drop new columns
            $table->dropColumn([
                'provider',
                'max_users',
                'storage_per_user',
                'commission_rate',
                'provider_config'
            ]);
        });
    }
};
