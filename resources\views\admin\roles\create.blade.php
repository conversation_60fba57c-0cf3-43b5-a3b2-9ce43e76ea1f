@extends('layouts.admin')

@section('title', 'إضافة دور جديد')

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">إضافة دور جديد</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.roles.index') }}">الأدوار والصلاحيات</a></li>
                        <li class="breadcrumb-item active">إضافة دور جديد</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <div class="row">
                <div class="col-md-12">
                    <div class="card card-primary">
                        <div class="card-header">
                            <h3 class="card-title">معلومات الدور</h3>
                        </div>
                        
                        <form method="POST" action="{{ route('admin.roles.store') }}">
                            @csrf
                            <div class="card-body">
                                
                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">اسم الدور (بالإنجليزية) <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name') }}" required
                                                   placeholder="مثال: editor">
                                            @error('name')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                            <small class="form-text text-muted">
                                                يجب أن يكون بالأحرف الإنجليزية فقط، بدون مسافات
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="display_name">الاسم المعروض <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('display_name') is-invalid @enderror" 
                                                   id="display_name" name="display_name" value="{{ old('display_name') }}" required
                                                   placeholder="مثال: محرر المحتوى">
                                            @error('display_name')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="description">الوصف</label>
                                    <textarea class="form-control @error('description') is-invalid @enderror" 
                                              id="description" name="description" rows="3"
                                              placeholder="وصف مختصر عن الدور وصلاحياته">{{ old('description') }}</textarea>
                                    @error('description')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Permissions -->
                                <div class="form-group">
                                    <label>الصلاحيات</label>
                                    <div class="row">
                                        @foreach($permissions as $module => $modulePermissions)
                                        <div class="col-md-6 mb-3">
                                            <div class="card">
                                                <div class="card-header">
                                                    <h6 class="card-title mb-0">
                                                        <input type="checkbox" class="module-checkbox" data-module="{{ $module }}">
                                                        {{ ucfirst($module) }}
                                                    </h6>
                                                </div>
                                                <div class="card-body">
                                                    @foreach($modulePermissions as $permission)
                                                    <div class="form-check">
                                                        <input type="checkbox" 
                                                               class="form-check-input permission-checkbox" 
                                                               id="permission_{{ $permission->id }}" 
                                                               name="permissions[]" 
                                                               value="{{ $permission->name }}"
                                                               data-module="{{ $module }}"
                                                               {{ in_array($permission->name, old('permissions', [])) ? 'checked' : '' }}>
                                                        <label class="form-check-label" for="permission_{{ $permission->id }}">
                                                            {{ $permission->display_name ?? $permission->name }}
                                                        </label>
                                                        @if($permission->description)
                                                        <small class="form-text text-muted">{{ $permission->description }}</small>
                                                        @endif
                                                    </div>
                                                    @endforeach
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                    @error('permissions')
                                        <span class="text-danger">{{ $message }}</span>
                                    @enderror
                                </div>

                            </div>

                            <div class="card-footer">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> إنشاء الدور
                                </button>
                                <a href="{{ route('admin.roles.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Module checkbox functionality
    $('.module-checkbox').change(function() {
        const module = $(this).data('module');
        const isChecked = $(this).is(':checked');
        
        $(`.permission-checkbox[data-module="${module}"]`).prop('checked', isChecked);
    });

    // Update module checkbox when individual permissions change
    $('.permission-checkbox').change(function() {
        const module = $(this).data('module');
        const totalPermissions = $(`.permission-checkbox[data-module="${module}"]`).length;
        const checkedPermissions = $(`.permission-checkbox[data-module="${module}"]:checked`).length;
        
        const moduleCheckbox = $(`.module-checkbox[data-module="${module}"]`);
        
        if (checkedPermissions === 0) {
            moduleCheckbox.prop('checked', false).prop('indeterminate', false);
        } else if (checkedPermissions === totalPermissions) {
            moduleCheckbox.prop('checked', true).prop('indeterminate', false);
        } else {
            moduleCheckbox.prop('checked', false).prop('indeterminate', true);
        }
    });

    // Initialize module checkboxes state
    $('.module-checkbox').each(function() {
        const module = $(this).data('module');
        const totalPermissions = $(`.permission-checkbox[data-module="${module}"]`).length;
        const checkedPermissions = $(`.permission-checkbox[data-module="${module}"]:checked`).length;
        
        if (checkedPermissions === 0) {
            $(this).prop('checked', false).prop('indeterminate', false);
        } else if (checkedPermissions === totalPermissions) {
            $(this).prop('checked', true).prop('indeterminate', false);
        } else {
            $(this).prop('checked', false).prop('indeterminate', true);
        }
    });

    // Form validation
    $('form').on('submit', function(e) {
        const name = $('#name').val().trim();
        const displayName = $('#display_name').val().trim();
        
        if (!name || !displayName) {
            e.preventDefault();
            alert('يرجى ملء جميع الحقول المطلوبة');
            return false;
        }
        
        // Check if name contains only English letters, numbers, and underscores
        if (!/^[a-zA-Z0-9_]+$/.test(name)) {
            e.preventDefault();
            alert('اسم الدور يجب أن يحتوي على أحرف إنجليزية وأرقام وشرطة سفلية فقط');
            return false;
        }
    });

    // Auto-generate name from display name
    $('#display_name').on('input', function() {
        const displayName = $(this).val();
        const name = displayName
            .toLowerCase()
            .replace(/[^a-zA-Z0-9\s]/g, '')
            .replace(/\s+/g, '_')
            .replace(/^_+|_+$/g, '');
        
        if (!$('#name').val()) {
            $('#name').val(name);
        }
    });
});
</script>
@endpush
