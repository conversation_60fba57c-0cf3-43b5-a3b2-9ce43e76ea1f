<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bank_transfers', function (Blueprint $table) {
            $table->id();
            $table->string('order_id'); // رقم الطلب
            $table->string('full_name'); // الاسم الكامل
            $table->enum('transfer_type', ['تحويل', 'ايداع']); // نوع العملية
            $table->decimal('amount', 10, 2); // المبلغ
            $table->string('service_type')->default('domain'); // نوع الخدمة
            $table->enum('status', ['pending', 'confirmed', 'rejected'])->default('pending'); // حالة التحويل
            $table->string('reference_number')->unique(); // رقم المرجع
            $table->text('admin_notes')->nullable(); // ملاحظات الإدارة
            $table->timestamp('confirmed_at')->nullable(); // تاريخ التأكيد
            $table->unsignedBigInteger('confirmed_by')->nullable(); // المؤكد بواسطة
            $table->timestamps();

            $table->index(['order_id', 'status']);
            $table->index('reference_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bank_transfers');
    }
};
