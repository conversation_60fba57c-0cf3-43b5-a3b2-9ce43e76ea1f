@extends('layouts.app')

@section('title', 'تم إنشاء طلب البريد الإلكتروني بنجاح')

@section('content')
<!-- Success Hero Section -->
<section class="relative bg-gradient-to-br from-green-900 via-blue-900 to-gray-800 py-20">
    <div class="absolute inset-0 bg-black/30"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center" data-aos="fade-up">
            <div class="w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                تم إنشاء طلبك <span class="text-green-400">بنجاح!</span>
            </h1>
            <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                شكراً لك على اختيار خدماتنا. سيتم معالجة طلبك وإرسال تفاصيل الحساب قريباً
            </p>
        </div>
    </div>
</section>

<!-- Order Details -->
<section class="py-20 bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Order Information -->
            <div class="bg-gray-800 rounded-2xl p-8 border border-gray-700">
                <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-blue-400 ml-3 rtl:mr-3 rtl:ml-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                    </svg>
                    تفاصيل الطلب
                </h2>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">رقم الطلب</span>
                        <span class="text-white font-semibold">{{ $order->order_number }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">تاريخ الطلب</span>
                        <span class="text-white">{{ $order->created_at->format('Y-m-d H:i') }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">الخطة</span>
                        <span class="text-white font-semibold">{{ $order->emailPlan->name_ar }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">الدومين</span>
                        <span class="text-white">{{ $order->domain_name }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">عدد المستخدمين</span>
                        <span class="text-white">{{ $order->email_users_count }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">دورة الفوترة</span>
                        <span class="text-white">
                            {{ $order->order_details['billing_cycle'] === 'yearly' ? 'سنوي' : 'شهري' }}
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">حالة الطلب</span>
                        <span class="px-3 py-1 rounded-full text-sm font-semibold
                            @if($order->status === 'active') bg-green-500/20 text-green-400
                            @elseif($order->status === 'pending') bg-yellow-500/20 text-yellow-400
                            @else bg-gray-500/20 text-gray-400
                            @endif">
                            @if($order->status === 'active') نشط
                            @elseif($order->status === 'pending') قيد المعالجة
                            @else {{ $order->status }}
                            @endif
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3">
                        <span class="text-gray-300">حالة الدفع</span>
                        <span class="px-3 py-1 rounded-full text-sm font-semibold
                            @if($order->payment_status === 'paid') bg-green-500/20 text-green-400
                            @elseif($order->payment_status === 'pending') bg-yellow-500/20 text-yellow-400
                            @else bg-red-500/20 text-red-400
                            @endif">
                            @if($order->payment_status === 'paid') مدفوع
                            @elseif($order->payment_status === 'pending') قيد الانتظار
                            @else غير مدفوع
                            @endif
                        </span>
                    </div>
                </div>
            </div>

            <!-- Pricing Breakdown -->
            <div class="bg-gray-800 rounded-2xl p-8 border border-gray-700">
                <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-green-400 ml-3 rtl:mr-3 rtl:ml-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"/>
                    </svg>
                    تفاصيل الفاتورة
                </h2>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">المجموع الفرعي</span>
                        <span class="text-white">{{ number_format($order->subtotal) }} ريال</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">ضريبة القيمة المضافة (15%)</span>
                        <span class="text-white">{{ number_format($order->tax_amount) }} ريال</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 text-lg font-semibold">
                        <span class="text-white">المجموع الكلي</span>
                        <span class="text-green-400">{{ number_format($order->total_amount) }} ريال</span>
                    </div>
                </div>

                @if($order->payment_method === 'bank_transfer' && $order->payment_status === 'pending')
                    <div class="mt-6 p-4 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
                        <h3 class="text-yellow-400 font-semibold mb-2">تعليمات التحويل البنكي</h3>
                        <div class="text-gray-300 text-sm space-y-1">
                            <p><strong>اسم البنك:</strong> البنك الأهلي السعودي</p>
                            <p><strong>رقم الحساب:</strong> SA1234567890123456789</p>
                            <p><strong>اسم المستفيد:</strong> شركة كود كرافت للحلول التقنية</p>
                            <p><strong>المبلغ:</strong> {{ number_format($order->total_amount) }} ريال</p>
                            <p><strong>رقم المرجع:</strong> {{ $order->order_number }}</p>
                        </div>
                        <p class="text-yellow-400 text-sm mt-3">
                            يرجى إرسال إيصال التحويل عبر الواتساب أو البريد الإلكتروني
                        </p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Email Users List -->
        @if($order->email_users && count($order->email_users) > 0)
            <div class="mt-8 bg-gray-800 rounded-2xl p-8 border border-gray-700">
                <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-purple-400 ml-3 rtl:mr-3 rtl:ml-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                    </svg>
                    حسابات البريد الإلكتروني
                </h2>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    @foreach($order->email_users as $index => $user)
                        <div class="bg-gray-700 rounded-lg p-4">
                            <div class="flex items-center mb-2">
                                <div class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                                    {{ $index + 1 }}
                                </div>
                                <div class="mr-3 rtl:ml-3 rtl:mr-0">
                                    <h3 class="text-white font-semibold">{{ $user['name'] }}</h3>
                                    <p class="text-gray-300 text-sm">{{ $user['email'] }}@{{ $order->domain_name }}</p>
                                </div>
                            </div>
                            <div class="text-gray-400 text-xs">
                                @if($order->status === 'active')
                                    ✅ تم إنشاء الحساب
                                @else
                                    ⏳ قيد الإنشاء
                                @endif
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endif

        <!-- Next Steps -->
        <div class="mt-8 bg-gradient-to-r from-blue-500/10 to-purple-500/10 border border-blue-500/30 rounded-2xl p-8">
            <h2 class="text-2xl font-bold text-white mb-6 flex items-center">
                <svg class="w-6 h-6 text-blue-400 ml-3 rtl:mr-3 rtl:ml-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                </svg>
                الخطوات التالية
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3 rtl:ml-3 rtl:mr-0 mt-1">
                            1
                        </div>
                        <div>
                            <h3 class="text-white font-semibold mb-1">معالجة الطلب</h3>
                            <p class="text-gray-300 text-sm">
                                سيتم معالجة طلبك خلال 24 ساعة من تأكيد الدفع
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3 rtl:ml-3 rtl:mr-0 mt-1">
                            2
                        </div>
                        <div>
                            <h3 class="text-white font-semibold mb-1">إرسال تفاصيل الحساب</h3>
                            <p class="text-gray-300 text-sm">
                                ستصلك رسالة بريد إلكتروني تحتوي على تفاصيل الدخول
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3 rtl:ml-3 rtl:mr-0 mt-1">
                            3
                        </div>
                        <div>
                            <h3 class="text-white font-semibold mb-1">إعداد البريد الإلكتروني</h3>
                            <p class="text-gray-300 text-sm">
                                دليل مفصل لإعداد البريد على جميع الأجهزة
                            </p>
                        </div>
                    </div>
                    
                    <div class="flex items-start">
                        <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold text-sm mr-3 rtl:ml-3 rtl:mr-0 mt-1">
                            4
                        </div>
                        <div>
                            <h3 class="text-white font-semibold mb-1">الدعم الفني</h3>
                            <p class="text-gray-300 text-sm">
                                فريق الدعم متاح 24/7 لمساعدتك في أي استفسار
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Contact Information -->
        <div class="mt-8 text-center">
            <div class="bg-gray-800 rounded-2xl p-8 border border-gray-700">
                <h3 class="text-xl font-bold text-white mb-4">هل تحتاج مساعدة؟</h3>
                <p class="text-gray-300 mb-6">
                    فريق الدعم الفني متاح على مدار الساعة لمساعدتك
                </p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="https://wa.me/966501234567" 
                       class="bg-green-500 hover:bg-green-600 text-white px-6 py-3 rounded-lg font-semibold transition-all flex items-center justify-center">
                        <svg class="w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488"/>
                        </svg>
                        واتساب
                    </a>
                    
                    <a href="mailto:<EMAIL>" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-all flex items-center justify-center">
                        <svg class="w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"/>
                        </svg>
                        بريد إلكتروني
                    </a>
                    
                    <a href="tel:+966501234567" 
                       class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold transition-all flex items-center justify-center">
                        <svg class="w-5 h-5 ml-2 rtl:mr-2 rtl:ml-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                        </svg>
                        هاتف
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
