@props(['items' => []])

@if(count($items) > 0)
<nav class="bg-gray-50 py-4 border-b border-gray-200" aria-label="Breadcrumb">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <ol class="flex items-center space-x-2 space-x-reverse text-sm">
            <!-- Home Link -->
            <li>
                <a href="{{ route('home') }}" class="text-gray-500 hover:text-purple-600 transition-colors duration-200">
                    <i class="fas fa-home ml-1"></i>
                    الرئيسية
                </a>
            </li>
            
            @foreach($items as $index => $item)
                <li class="flex items-center">
                    <i class="fas fa-chevron-left text-gray-400 mx-2"></i>
                    @if($loop->last)
                        <span class="text-gray-900 font-medium">{{ $item['title'] }}</span>
                    @else
                        <a href="{{ $item['url'] }}" class="text-gray-500 hover:text-purple-600 transition-colors duration-200">
                            {{ $item['title'] }}
                        </a>
                    @endif
                </li>
            @endforeach
        </ol>
    </div>
</nav>

<!-- Schema.org Breadcrumb -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
        {
            "@type": "ListItem",
            "position": 1,
            "name": "الرئيسية",
            "item": "{{ route('home') }}"
        }
        @foreach($items as $index => $item)
        ,{
            "@type": "ListItem",
            "position": {{ $index + 2 }},
            "name": "{{ $item['title'] }}"
            @if(!$loop->last)
            ,"item": "{{ $item['url'] }}"
            @endif
        }
        @endforeach
    ]
}
</script>
@endif
