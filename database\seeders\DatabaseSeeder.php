<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Create admin user directly
        $this->createAdminUser();

        // Seed other data
        $this->call([
            UserSeeder::class,
        ]);
    }



    private function createAdminUser(): void
    {
        try {
            // إنشاء جدول المديرين إذا لم يكن موجوداً
            if (!Schema::hasTable('admins')) {
                Schema::create('admins', function (Blueprint $table) {
                    $table->id();
                    $table->string('name');
                    $table->string('email')->unique();
                    $table->timestamp('email_verified_at')->nullable();
                    $table->string('password');
                    $table->string('avatar')->nullable();
                    $table->string('phone')->nullable();
                    $table->boolean('is_active')->default(true);
                    $table->timestamp('last_login_at')->nullable();
                    $table->rememberToken();
                    $table->timestamps();
                });
                $this->command->info('✅ تم إنشاء جدول المديرين');
            }

            // إنشاء جدول cache إذا لم يكن موجوداً
            if (!Schema::hasTable('cache')) {
                Schema::create('cache', function (Blueprint $table) {
                    $table->string('key')->primary();
                    $table->mediumText('value');
                    $table->integer('expiration');
                });
                $this->command->info('✅ تم إنشاء جدول cache');
            }

            // إنشاء جدول cache_locks إذا لم يكن موجوداً
            if (!Schema::hasTable('cache_locks')) {
                Schema::create('cache_locks', function (Blueprint $table) {
                    $table->string('key')->primary();
                    $table->string('owner');
                    $table->integer('expiration');
                });
                $this->command->info('✅ تم إنشاء جدول cache_locks');
            }

            // إنشاء المدير الافتراضي
            $admin = Admin::firstOrCreate([
                'email' => '<EMAIL>'
            ], [
                'name' => 'مدير ميتاء تك',
                'password' => Hash::make('123456'),
                'is_active' => true,
            ]);

            $this->command->info('✅ تم إنشاء المدير الافتراضي بنجاح!');
            $this->command->info('📧 البريد الإلكتروني: <EMAIL>');
            $this->command->info('🔑 كلمة المرور: 123456');

            // إنشاء بعض المستخدمين التجريبيين
            $this->createSampleData();

        } catch (\Exception $e) {
            $this->command->error('❌ خطأ في إنشاء المدير: ' . $e->getMessage());
        }
    }

    private function createSampleData(): void
    {
        try {
            // إنشاء مستخدمين تجريبيين
            if (User::count() < 5) {
                for ($i = 1; $i <= 10; $i++) {
                    User::firstOrCreate([
                        'email' => "user{$i}@example.com"
                    ], [
                        'name' => "مستخدم تجريبي {$i}",
                        'password' => Hash::make('password'),
                        'email_verified_at' => now(),
                    ]);
                }
                $this->command->info('✅ تم إنشاء المستخدمين التجريبيين');
            }

            // إنشاء رسائل اتصال تجريبية
            if (DB::table('contacts')->count() < 3) {
                for ($i = 1; $i <= 5; $i++) {
                    DB::table('contacts')->insert([
                        'name' => "عميل {$i}",
                        'email' => "client{$i}@example.com",
                        'phone' => "77777777{$i}",
                        'subject' => "استفسار عن الخدمات",
                        'message' => "مرحباً، أريد الاستفسار عن خدماتكم المتاحة.",
                        'is_read' => $i % 2 == 0,
                        'created_at' => now()->subDays(rand(1, 30)),
                        'updated_at' => now()->subDays(rand(1, 30)),
                    ]);
                }
                $this->command->info('✅ تم إنشاء رسائل الاتصال التجريبية');
            }

        } catch (\Exception $e) {
            $this->command->error('❌ خطأ في إنشاء البيانات التجريبية: ' . $e->getMessage());
        }
    }
}
