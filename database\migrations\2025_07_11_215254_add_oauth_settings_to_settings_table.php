<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use App\Models\Setting;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add OAuth default settings
        $oauthSettings = [
            [
                'key' => 'google_client_id',
                'value' => env('GOOGLE_CLIENT_ID', ''),
                'type' => 'string',
                'description' => 'Google OAuth Client ID',
                'is_public' => false,
            ],
            [
                'key' => 'google_client_secret',
                'value' => env('GOOGLE_CLIENT_SECRET', ''),
                'type' => 'string',
                'description' => 'Google OAuth Client Secret',
                'is_public' => false,
            ],
            [
                'key' => 'google_redirect_uri',
                'value' => env('GOOGLE_REDIRECT_URI', url('/auth/google/socialite/callback')),
                'type' => 'string',
                'description' => 'Google OAuth Redirect URI',
                'is_public' => false,
            ],
            [
                'key' => 'oauth_enabled',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Enable Google OAuth authentication',
                'is_public' => false,
            ],
            [
                'key' => 'auto_register_users',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Automatically register new users via OAuth',
                'is_public' => false,
            ],
            [
                'key' => 'default_user_role',
                'value' => 'user',
                'type' => 'string',
                'description' => 'Default role for new OAuth users',
                'is_public' => false,
            ],
        ];

        foreach ($oauthSettings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove OAuth settings
        $oauthKeys = [
            'google_client_id',
            'google_client_secret',
            'google_redirect_uri',
            'oauth_enabled',
            'auto_register_users',
            'default_user_role',
        ];

        Setting::whereIn('key', $oauthKeys)->delete();
    }
};
