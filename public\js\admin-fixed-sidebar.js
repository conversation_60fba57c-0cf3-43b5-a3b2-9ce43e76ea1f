// Fixed Sidebar JavaScript - Ultimate Override

(function() {
    'use strict';

    // Function to enforce sidebar positioning with maximum force
    function enforceSidebarPosition() {
        const sidebar = document.querySelector('.main-sidebar');
        const contentWrapper = document.querySelector('.content-wrapper');
        const mainHeader = document.querySelector('.main-header');
        const body = document.body;

        if (sidebar) {
            // Remove any conflicting classes
            body.classList.remove('sidebar-collapse', 'sidebar-mini', 'sidebar-mini-md', 'sidebar-mini-xs');

            // Force sidebar positioning with maximum priority
            sidebar.style.setProperty('position', 'fixed', 'important');
            sidebar.style.setProperty('top', '0', 'important');
            sidebar.style.setProperty('right', '0', 'important');
            sidebar.style.setProperty('left', 'auto', 'important');
            sidebar.style.setProperty('width', '250px', 'important');
            sidebar.style.setProperty('height', '100vh', 'important');
            sidebar.style.setProperty('z-index', '1050', 'important');
            sidebar.style.setProperty('transform', 'translateX(0)', 'important');
            sidebar.style.setProperty('transition', 'none', 'important');
            sidebar.style.setProperty('visibility', 'visible', 'important');
            sidebar.style.setProperty('opacity', '1', 'important');
            sidebar.style.setProperty('display', 'block', 'important');
            sidebar.style.setProperty('margin', '0', 'important');
            sidebar.style.setProperty('overflow-y', 'auto', 'important');
            sidebar.style.setProperty('overflow-x', 'hidden', 'important');

            // Ensure sidebar is always visible
            sidebar.setAttribute('data-fixed', 'true');
        }

        if (contentWrapper) {
            // Force content positioning with maximum priority
            contentWrapper.style.setProperty('margin-right', '250px', 'important');
            contentWrapper.style.setProperty('margin-left', '0', 'important');
            contentWrapper.style.setProperty('min-height', '100vh', 'important');
            contentWrapper.style.setProperty('position', 'relative', 'important');
            contentWrapper.style.setProperty('padding-top', '60px', 'important');
            contentWrapper.style.setProperty('transition', 'none', 'important');
        }

        if (mainHeader) {
            // Force header positioning with maximum priority
            mainHeader.style.setProperty('margin-right', '250px', 'important');
            mainHeader.style.setProperty('margin-left', '0', 'important');
            mainHeader.style.setProperty('position', 'fixed', 'important');
            mainHeader.style.setProperty('top', '0', 'important');
            mainHeader.style.setProperty('right', '250px', 'important');
            mainHeader.style.setProperty('left', '0', 'important');
            mainHeader.style.setProperty('width', 'calc(100% - 250px)', 'important');
            mainHeader.style.setProperty('z-index', '1001', 'important');
            mainHeader.style.setProperty('transition', 'none', 'important');
        }
    }

    // Function to completely disable AdminLTE sidebar behaviors
    function disableAllSidebarBehaviors() {
        // Remove any sidebar classes that might interfere
        const body = document.body;
        const wrapper = document.querySelector('.wrapper');

        if (body) {
            body.classList.remove('sidebar-collapse', 'sidebar-mini', 'sidebar-mini-md', 'sidebar-mini-xs');
            body.classList.add('sidebar-fixed');
        }

        if (wrapper) {
            wrapper.classList.remove('sidebar-collapse', 'sidebar-mini', 'sidebar-mini-md', 'sidebar-mini-xs');
        }
    }

    // Function to completely disable AdminLTE layout functions
    function disableAdminLTELayout() {
        // Disable jQuery-based AdminLTE functions
        if (typeof $ !== 'undefined' && typeof $.AdminLTE !== 'undefined') {
            if ($.AdminLTE.Layout) {
                $.AdminLTE.Layout.prototype.fix = function() { enforceSidebarPosition(); };
                $.AdminLTE.Layout.prototype.fixSidebar = function() { enforceSidebarPosition(); };
                $.AdminLTE.Layout.prototype.activate = function() { enforceSidebarPosition(); };
            }

            // Override layout object completely
            $.AdminLTE.layout = {
                activate: function() { enforceSidebarPosition(); },
                fix: function() { enforceSidebarPosition(); },
                fixSidebar: function() { enforceSidebarPosition(); }
            };

            // Disable PushMenu widget
            if ($.AdminLTE.PushMenu) {
                $.AdminLTE.PushMenu.prototype.toggle = function() { return false; };
                $.AdminLTE.PushMenu.prototype.expand = function() { return false; };
                $.AdminLTE.PushMenu.prototype.collapse = function() { return false; };
            }
        }

        // Disable vanilla JS AdminLTE if it exists
        if (typeof AdminLTE !== 'undefined') {
            AdminLTE.Layout = {
                fix: function() { enforceSidebarPosition(); },
                fixSidebar: function() { enforceSidebarPosition(); }
            };
        }
    }

    // Function to override ALL menu behaviors
    function overridePushMenu() {
        // Override click events with highest priority
        document.addEventListener('click', function(e) {
            const target = e.target;
            if (target.matches('[data-widget="pushmenu"]') ||
                target.closest('[data-widget="pushmenu"]') ||
                target.matches('.sidebar-toggle') ||
                target.closest('.sidebar-toggle')) {
                e.preventDefault();
                e.stopPropagation();
                e.stopImmediatePropagation();
                enforceSidebarPosition();
                return false;
            }
        }, true);

        // Override any existing event listeners
        const pushMenuElements = document.querySelectorAll('[data-widget="pushmenu"], .sidebar-toggle');
        pushMenuElements.forEach(element => {
            element.onclick = function(e) {
                e.preventDefault();
                e.stopPropagation();
                enforceSidebarPosition();
                return false;
            };
        });
    }

    // Monitor for DOM changes and fix them
    function setupMutationObserver() {
        const observer = new MutationObserver(function(mutations) {
            let needsUpdate = false;
            
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && 
                    (mutation.attributeName === 'class' || mutation.attributeName === 'style')) {
                    if (mutation.target.classList.contains('main-sidebar') ||
                        mutation.target.classList.contains('wrapper') ||
                        mutation.target === document.body) {
                        needsUpdate = true;
                    }
                }
            });
            
            if (needsUpdate) {
                setTimeout(enforceSidebarPosition, 50);
            }
        });

        observer.observe(document.body, {
            attributes: true,
            attributeFilter: ['class', 'style'],
            subtree: true
        });
    }

    // Initialize everything when DOM is ready
    function initialize() {
        console.log('Initializing Fixed Admin Sidebar...');

        // Run all enforcement functions
        disableAllSidebarBehaviors();
        enforceSidebarPosition();
        disableAdminLTELayout();
        overridePushMenu();
        setupMutationObserver();

        // Set up aggressive periodic checks
        setInterval(function() {
            const sidebar = document.querySelector('.main-sidebar');
            if (sidebar) {
                const computedStyle = window.getComputedStyle(sidebar);
                if (computedStyle.position !== 'fixed' ||
                    computedStyle.right !== '0px' ||
                    computedStyle.width !== '250px' ||
                    computedStyle.transform !== 'none' ||
                    computedStyle.visibility !== 'visible' ||
                    computedStyle.opacity !== '1') {
                    console.log('Sidebar position drift detected, fixing...');
                    enforceSidebarPosition();
                }
            }

            // Also check for unwanted classes
            const body = document.body;
            if (body.classList.contains('sidebar-collapse') ||
                body.classList.contains('sidebar-mini')) {
                console.log('Unwanted sidebar classes detected, removing...');
                disableAllSidebarBehaviors();
                enforceSidebarPosition();
            }
        }, 500); // Check every 500ms for maximum responsiveness

        // Handle window events with immediate response
        window.addEventListener('resize', function() {
            disableAllSidebarBehaviors();
            setTimeout(enforceSidebarPosition, 50);
        });

        window.addEventListener('scroll', function() {
            enforceSidebarPosition();
        });

        // Handle focus events (when switching tabs/windows)
        window.addEventListener('focus', function() {
            setTimeout(function() {
                disableAllSidebarBehaviors();
                enforceSidebarPosition();
            }, 100);
        });

        console.log('Fixed Admin Sidebar initialized successfully');
    }

    // Start immediately and on all possible events
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }

    // Multiple backup initialization attempts
    window.addEventListener('load', function() {
        setTimeout(initialize, 50);
    });

    // Initialize after a short delay to ensure all scripts are loaded
    setTimeout(initialize, 100);

    // Final backup initialization
    setTimeout(function() {
        console.log('Final backup initialization...');
        disableAllSidebarBehaviors();
        enforceSidebarPosition();
    }, 1000);

    // Export functions for manual calling if needed
    window.AdminFixedSidebar = {
        enforce: enforceSidebarPosition,
        disable: disableAllSidebarBehaviors,
        init: initialize
    };

})();
