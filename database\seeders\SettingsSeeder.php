<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'site_name',
                'value' => 'ميتاء تك للبرمجة وتصميم المواقع والتطبيقات',
                'type' => 'string',
                'description' => 'اسم الموقع',
                'is_public' => true,
            ],
            [
                'key' => 'site_description',
                'value' => 'شركة متخصصة في تطوير المواقع والتطبيقات',
                'type' => 'string',
                'description' => 'وصف الموقع',
                'is_public' => true,
            ],
            [
                'key' => 'contact_email',
                'value' => '<EMAIL>',
                'type' => 'string',
                'description' => 'البريد الإلكتروني للتواصل',
                'is_public' => true,
            ],
            [
                'key' => 'contact_phone',
                'value' => '782871439',
                'type' => 'string',
                'description' => 'رقم الهاتف للتواصل',
                'is_public' => true,
            ],
            [
                'key' => 'contact_address',
                'value' => 'اليمن، حضرموت، المكلا',
                'type' => 'string',
                'description' => 'عنوان الشركة',
                'is_public' => true,
            ],
            [
                'key' => 'whatsapp_number',
                'value' => '967737662752',
                'type' => 'string',
                'description' => 'رقم الواتساب',
                'is_public' => true,
            ],
            [
                'key' => 'facebook_url',
                'value' => '',
                'type' => 'string',
                'description' => 'رابط صفحة الفيسبوك',
                'is_public' => true,
            ],
            [
                'key' => 'twitter_url',
                'value' => '',
                'type' => 'string',
                'description' => 'رابط صفحة تويتر',
                'is_public' => true,
            ],
            [
                'key' => 'instagram_url',
                'value' => '',
                'type' => 'string',
                'description' => 'رابط صفحة الانستغرام',
                'is_public' => true,
            ],
            [
                'key' => 'linkedin_url',
                'value' => '',
                'type' => 'string',
                'description' => 'رابط صفحة لينكد إن',
                'is_public' => true,
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'وضع الصيانة',
                'is_public' => false,
            ],
            [
                'key' => 'allow_registration',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'السماح بالتسجيل',
                'is_public' => false,
            ],
            [
                'key' => 'items_per_page',
                'value' => '10',
                'type' => 'integer',
                'description' => 'عدد العناصر في الصفحة',
                'is_public' => false,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}
