<?php

namespace Database\Seeders;

use App\Models\HostingProvider;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class HostingProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $providers = [
            [
                'name' => 'Hostinger Reseller',
                'type' => 'hostinger_reseller',
                'api_endpoint' => 'https://api.hostinger.com/v1',
                'api_key' => 'your_hostinger_api_key_here',
                'api_secret' => null,
                'api_config' => [
                    'reseller_id' => 'your_reseller_id',
                    'white_label' => true,
                ],
                'commission_rate' => 30.00,
                'is_active' => true,
                'supported_features' => [
                    'auto_ssl',
                    'daily_backups',
                    'email_accounts',
                    'mysql_databases',
                    'ftp_accounts',
                ],
                'notes' => 'Primary hosting provider for shared hosting plans',
            ],
            [
                'name' => 'DigitalOcean',
                'type' => 'digitalocean',
                'api_endpoint' => 'https://api.digitalocean.com/v2',
                'api_key' => 'your_digitalocean_api_key_here',
                'api_secret' => null,
                'api_config' => [
                    'default_region' => 'fra1',
                    'default_image' => 'ubuntu-22-04-x64',
                    'enable_backups' => true,
                    'enable_monitoring' => true,
                ],
                'commission_rate' => 0.00,
                'is_active' => true,
                'supported_features' => [
                    'ssd_storage',
                    'automated_backups',
                    'load_balancers',
                    'firewalls',
                    'monitoring',
                ],
                'notes' => 'VPS and cloud hosting provider',
            ],
            [
                'name' => 'cPanel/WHM Server',
                'type' => 'cpanel_whm',
                'api_endpoint' => 'https://your-server.com:2087',
                'api_key' => 'your_whm_api_key_here',
                'api_secret' => null,
                'api_config' => [
                    'server_ip' => '*************',
                    'nameservers' => [
                        'ns1.your-server.com',
                        'ns2.your-server.com',
                    ],
                ],
                'commission_rate' => 0.00,
                'is_active' => false,
                'supported_features' => [
                    'cpanel_access',
                    'email_accounts',
                    'mysql_databases',
                    'ssl_certificates',
                    'file_manager',
                ],
                'notes' => 'Self-managed cPanel server (currently inactive)',
            ],
            [
                'name' => 'Contabo VPS',
                'type' => 'contabo',
                'api_endpoint' => 'https://api.contabo.com/v1',
                'api_key' => 'your_contabo_api_key_here',
                'api_secret' => 'your_contabo_api_secret_here',
                'api_config' => [
                    'customer_id' => 'your_customer_id',
                    'default_location' => 'EU',
                ],
                'commission_rate' => 0.00,
                'is_active' => false,
                'supported_features' => [
                    'high_performance',
                    'ddos_protection',
                    'snapshots',
                    'rescue_system',
                ],
                'notes' => 'High-performance VPS provider (backup option)',
            ],
        ];

        foreach ($providers as $provider) {
            HostingProvider::create($provider);
        }
    }
}
