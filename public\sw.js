// Service Worker for Meta Tech PWA - Optimized for Performance
const CACHE_VERSION = '2.0.0';
const STATIC_CACHE = `metatech-static-v${CACHE_VERSION}`;
const DYNAMIC_CACHE = `metatech-dynamic-v${CACHE_VERSION}`;
const IMAGE_CACHE = `metatech-images-v${CACHE_VERSION}`;
const API_CACHE = `metatech-api-v${CACHE_VERSION}`;

// Cache strategies
const CACHE_STRATEGIES = {
    CACHE_FIRST: 'cache-first',
    NETWORK_FIRST: 'network-first',
    STALE_WHILE_REVALIDATE: 'stale-while-revalidate'
};

// Files to cache
const STATIC_FILES = [
    '/',
    '/services',
    '/hosting',
    '/contact',
    '/about',
    '/portfolio',
    '/manifest.json',
    '/images/zdeuj.png',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Tajawal:wght@300;400;500;700;800;900&display=swap',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    'https://unpkg.com/aos@2.3.1/dist/aos.css',
    'https://cdn.tailwindcss.com'
];

// Install event
self.addEventListener('install', event => {
    console.log('Service Worker: Installing...');
    event.waitUntil(
        caches.open(STATIC_CACHE)
            .then(cache => {
                console.log('Service Worker: Caching static files');
                return cache.addAll(STATIC_FILES);
            })
            .then(() => {
                console.log('Service Worker: Static files cached');
                return self.skipWaiting();
            })
            .catch(error => {
                console.error('Service Worker: Error caching static files', error);
            })
    );
});

// Activate event
self.addEventListener('activate', event => {
    console.log('Service Worker: Activating...');
    event.waitUntil(
        caches.keys().then(cacheNames => {
            return Promise.all(
                cacheNames.map(cacheName => {
                    if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
                        console.log('Service Worker: Deleting old cache', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        }).then(() => {
            console.log('Service Worker: Activated');
            return self.clients.claim();
        })
    );
});

// Fetch event - Network First with Cache Fallback
self.addEventListener('fetch', event => {
    const { request } = event;
    
    // Skip non-GET requests
    if (request.method !== 'GET') {
        return;
    }

    // Skip Chrome extension requests
    if (request.url.startsWith('chrome-extension://')) {
        return;
    }

    event.respondWith(
        // Try network first
        fetch(request)
            .then(response => {
                // Check if response is valid
                if (!response || response.status !== 200 || response.type !== 'basic') {
                    return response;
                }

                // Clone response for caching
                const responseToCache = response.clone();

                // Cache dynamic content
                caches.open(DYNAMIC_CACHE)
                    .then(cache => {
                        cache.put(request, responseToCache);
                    });

                return response;
            })
            .catch(() => {
                // Network failed, try cache
                return caches.match(request)
                    .then(response => {
                        if (response) {
                            return response;
                        }
                        
                        // If no cache, return offline page for navigation requests
                        if (request.destination === 'document') {
                            return caches.match('/');
                        }
                        
                        // For other requests, return a basic response
                        return new Response('Offline', {
                            status: 503,
                            statusText: 'Service Unavailable'
                        });
                    });
            })
    );
});

// Background sync for form submissions
self.addEventListener('sync', event => {
    if (event.tag === 'contact-form') {
        event.waitUntil(syncContactForm());
    }
});

// Push notifications
self.addEventListener('push', event => {
    const options = {
        body: event.data ? event.data.text() : 'رسالة جديدة من ميتاء تك',
        icon: '/images/zdeuj.png',
        badge: '/images/zdeuj.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'عرض التفاصيل',
                icon: '/images/zdeuj.png'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/images/zdeuj.png'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('ميتاء تك', options)
    );
});

// Notification click handler
self.addEventListener('notificationclick', event => {
    event.notification.close();

    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});

// Helper function for syncing contact form
async function syncContactForm() {
    try {
        // Get pending form data from IndexedDB or localStorage
        const pendingForms = await getPendingForms();
        
        for (const form of pendingForms) {
            try {
                const response = await fetch('/contact', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': form.token
                    },
                    body: JSON.stringify(form.data)
                });

                if (response.ok) {
                    await removePendingForm(form.id);
                }
            } catch (error) {
                console.error('Failed to sync form:', error);
            }
        }
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

// Helper functions for form data management
async function getPendingForms() {
    // Implementation would depend on your storage choice
    return [];
}

async function removePendingForm(id) {
    // Implementation would depend on your storage choice
    return true;
}

// Cache management
self.addEventListener('message', event => {
    if (event.data && event.data.type === 'SKIP_WAITING') {
        self.skipWaiting();
    }
    
    if (event.data && event.data.type === 'CACHE_URLS') {
        event.waitUntil(
            caches.open(DYNAMIC_CACHE)
                .then(cache => cache.addAll(event.data.payload))
        );
    }
});

// Periodic background sync (if supported)
self.addEventListener('periodicsync', event => {
    if (event.tag === 'content-sync') {
        event.waitUntil(syncContent());
    }
});

async function syncContent() {
    try {
        // Sync latest content, prices, etc.
        const response = await fetch('/api/sync-content');
        if (response.ok) {
            const data = await response.json();
            // Update cached content
            await updateCachedContent(data);
        }
    } catch (error) {
        console.error('Content sync failed:', error);
    }
}

async function updateCachedContent(data) {
    // Update cached pages with new content
    const cache = await caches.open(DYNAMIC_CACHE);
    // Implementation depends on your content structure
}
