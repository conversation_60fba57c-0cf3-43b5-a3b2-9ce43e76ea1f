<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class UserDomain extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'domain_name',
        'domain_url',
        'status',
        'expiry_date',
        'registration_date',
        'auto_renewal',
        'nameservers',
        'dns_records',
        'ssl_status',
        'notes',
        'provider',
        'provider_domain_id',
    ];

    protected $casts = [
        'expiry_date' => 'date',
        'registration_date' => 'date',
        'auto_renewal' => 'boolean',
        'nameservers' => 'array',
        'dns_records' => 'array',
    ];

    /**
     * Get the user that owns the domain.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'نشط' => 'success',
            'غير نشط' => 'danger',
            'منتهي الصلاحية' => 'warning',
            'معلق' => 'secondary',
            default => 'secondary'
        };
    }

    /**
     * Get the status text in Arabic
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'نشط' => 'نشط',
            'غير نشط' => 'غير نشط',
            'منتهي الصلاحية' => 'منتهي الصلاحية',
            'معلق' => 'معلق',
            default => 'غير محدد'
        };
    }

    /**
     * Check if domain is expiring soon (within 30 days)
     */
    public function getIsExpiringSoonAttribute(): bool
    {
        if (!$this->expiry_date) {
            return false;
        }
        
        return $this->expiry_date->diffInDays(now()) <= 30 && $this->expiry_date->isFuture();
    }

    /**
     * Check if domain is expired
     */
    public function getIsExpiredAttribute(): bool
    {
        if (!$this->expiry_date) {
            return false;
        }
        
        return $this->expiry_date->isPast();
    }

    /**
     * Get days until expiry
     */
    public function getDaysUntilExpiryAttribute(): int
    {
        if (!$this->expiry_date) {
            return 0;
        }
        
        return max(0, $this->expiry_date->diffInDays(now()));
    }
}
