<?php

use App\Http\Controllers\ProfileController;
use App\Http\Controllers\HostingController;
use App\Http\Controllers\DomainController;
use App\Http\Controllers\EmailController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\PortfolioController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\LocaleController;
use Illuminate\Support\Facades\Route;

/*
 * Global Routes
 */

// Switch between the included languages
Route::get('lang/{lang}', [LocaleController::class, 'change'])->name('locale.change');

// Main website routes
Route::get('/', function () {
    return view('home');
})->name('home');

Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/services', function () {
    return view('services');
})->name('services');

Route::get('/portfolio', [PortfolioController::class, 'index'])->name('portfolio');

Route::get('/contact', [ContactController::class, 'index'])->name('contact');
Route::post('/contact', [ContactController::class, 'store'])->name('contact.store');

Route::get('/privacy-policy', function () {
    return view('privacy-policy');
})->name('privacy-policy');

Route::get('/terms-of-service', function () {
    return view('terms-of-service');
})->name('terms-of-service');

// Hosting routes
Route::prefix('hosting')->name('hosting.')->group(function () {
    Route::get('/', [HostingController::class, 'index'])->name('index');
    Route::get('/order/{plan?}', [HostingController::class, 'order'])->name('order');
    Route::post('/order', [HostingController::class, 'processOrder'])->name('process');
    Route::get('/success', [HostingController::class, 'success'])->name('success');
});

// Domain routes
Route::prefix('domains')->name('domains.')->group(function () {
    Route::get('/', [DomainController::class, 'index'])->name('index');
    Route::get('/order', [DomainController::class, 'order'])->name('order');
    Route::post('/order', [DomainController::class, 'processOrder'])->name('process');
});

// Email routes
Route::prefix('email')->name('email.')->group(function () {
    Route::get('/', [EmailController::class, 'index'])->name('index');
    Route::get('/order', [EmailController::class, 'order'])->name('order');
    Route::post('/order', [EmailController::class, 'processOrder'])->name('process');
    Route::get('/success', [EmailController::class, 'success'])->name('success');
});

// Blog routes
Route::prefix('blog')->name('blog.')->group(function () {
    Route::get('/', [BlogController::class, 'index'])->name('index');
    Route::get('/{slug}', [BlogController::class, 'show'])->name('show');
});

// Auth routes aliases for compatibility - removed duplicate
// Route::get('/login', function () {
//     return view('auth.login');
// })->name('login');

Route::get('/register', function () {
    return view('auth.register');
})->name('register');

Route::post('/logout', function () {
    auth()->logout();
    return redirect()->route('home');
})->name('logout');

// Dashboard and user routes (temporary placeholders)
Route::get('/dashboard', function () {
    return view('dashboard');
})->name('dashboard')->middleware('auth');

Route::get('/profile', function () {
    return view('profile.edit');
})->name('profile.edit')->middleware('auth');

Route::get('/orders', function () {
    return view('orders.index');
})->name('user.orders.index')->middleware('auth');

/*
 * Frontend Routes
 */
Route::group(['as' => 'frontend.'], function () {
    includeRouteFiles(__DIR__.'/frontend/');
});

// Route aliases for navigation compatibility
Route::get('/login-alias', function () {
    return redirect()->route('frontend.auth.login');
})->name('login');

/*
 * Backend Routes
 *
 * These routes can only be accessed by users with type `admin`
 */
// Temporarily disabled backend routes
// Route::group(['prefix' => 'admin', 'as' => 'admin.', 'middleware' => 'admin'], function () {
//     includeRouteFiles(__DIR__.'/backend/');
// });
