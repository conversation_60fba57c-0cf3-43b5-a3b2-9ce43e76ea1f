<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('domains', function (Blueprint $table) {
            // Add new columns for enhanced domain management
            $table->string('tld_category')->after('extension'); // popular, country, new, premium
            $table->text('description')->nullable()->after('tld_category');
            $table->boolean('is_premium')->default(false)->after('description');
            $table->boolean('requires_verification')->default(false)->after('is_premium');
            $table->json('features')->nullable()->after('requires_verification'); // Additional features
            $table->foreignId('domain_provider_id')->nullable()->constrained()->onDelete('set null');
            $table->decimal('setup_fee', 8, 2)->default(0)->after('transfer_price');
            $table->integer('min_years')->default(1)->after('setup_fee');
            $table->integer('max_years')->default(10)->after('min_years');
            $table->boolean('auto_renewal_available')->default(true)->after('max_years');
            $table->json('pricing_tiers')->nullable()->after('auto_renewal_available'); // Volume pricing
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('domains', function (Blueprint $table) {
            $table->dropForeign(['domain_provider_id']);
            $table->dropColumn([
                'tld_category',
                'description',
                'is_premium',
                'requires_verification',
                'features',
                'domain_provider_id',
                'setup_fee',
                'min_years',
                'max_years',
                'auto_renewal_available',
                'pricing_tiers'
            ]);
        });
    }
};
