@extends('layouts.admin')

@section('title', 'تفاصيل المستخدم - ' . $user->name)

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">تفاصيل المستخدم</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">إدارة المستخدمين</a></li>
                        <li class="breadcrumb-item active">{{ $user->name }}</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <!-- User Profile Card -->
            <div class="row">
                <div class="col-md-4">
                    <div class="card card-primary card-outline">
                        <div class="card-body box-profile">
                            <div class="text-center">
                                <img class="profile-user-img img-fluid img-circle"
                                     src="{{ $user->avatar_url }}"
                                     alt="User profile picture"
                                     style="width: 100px; height: 100px;">
                            </div>

                            <h3 class="profile-username text-center">{{ $user->name }}</h3>

                            <p class="text-muted text-center">
                                @if($user->email_verified_at)
                                    <span class="badge badge-success">مستخدم مفعل</span>
                                @else
                                    <span class="badge badge-warning">غير مفعل</span>
                                @endif
                            </p>

                            <ul class="list-group list-group-unbordered mb-3">
                                <li class="list-group-item">
                                    <b>إجمالي الطلبات</b> <a class="float-right">{{ $userStats['total_orders'] }}</a>
                                </li>
                                <li class="list-group-item">
                                    <b>الطلبات المكتملة</b> <a class="float-right">{{ $userStats['completed_orders'] }}</a>
                                </li>
                                <li class="list-group-item">
                                    <b>إجمالي الإنفاق</b> <a class="float-right">${{ number_format($userStats['total_spent'], 2) }}</a>
                                </li>
                                <li class="list-group-item">
                                    <b>عدد الدومينات</b> <a class="float-right">{{ $userStats['total_domains'] }}</a>
                                </li>
                                <li class="list-group-item">
                                    <b>عمر الحساب</b> <a class="float-right">{{ $userStats['account_age_days'] }} يوم</a>
                                </li>
                            </ul>

                            <div class="row">
                                <div class="col-6">
                                    <a href="{{ route('admin.users.edit', $user) }}" class="btn btn-warning btn-block">
                                        <i class="fas fa-edit"></i> تعديل
                                    </a>
                                </div>
                                <div class="col-6">
                                    <button type="button" class="btn btn-{{ $user->email_verified_at ? 'secondary' : 'success' }} btn-block"
                                            onclick="toggleVerification({{ $user->id }})">
                                        <i class="fas fa-{{ $user->email_verified_at ? 'user-times' : 'user-check' }}"></i>
                                        {{ $user->email_verified_at ? 'إلغاء التفعيل' : 'تفعيل' }}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="card card-primary">
                        <div class="card-header">
                            <h3 class="card-title">معلومات الاتصال</h3>
                        </div>
                        <div class="card-body">
                            <strong><i class="fas fa-envelope mr-1"></i> البريد الإلكتروني</strong>
                            <p class="text-muted">{{ $user->email }}</p>
                            <hr>

                            @if($user->phone)
                            <strong><i class="fas fa-phone mr-1"></i> الهاتف</strong>
                            <p class="text-muted">{{ $user->phone }}</p>
                            <hr>
                            @endif

                            @if($user->address)
                            <strong><i class="fas fa-map-marker-alt mr-1"></i> العنوان</strong>
                            <p class="text-muted">{{ $user->address }}</p>
                            <hr>
                            @endif

                            @if($user->website)
                            <strong><i class="fas fa-globe mr-1"></i> الموقع الإلكتروني</strong>
                            <p class="text-muted">
                                <a href="{{ $user->website }}" target="_blank">{{ $user->website }}</a>
                            </p>
                            <hr>
                            @endif

                            <strong><i class="fas fa-calendar mr-1"></i> تاريخ التسجيل</strong>
                            <p class="text-muted">{{ $user->created_at->format('Y-m-d H:i:s') }}</p>

                            @if($user->last_login_at)
                            <hr>
                            <strong><i class="fas fa-clock mr-1"></i> آخر دخول</strong>
                            <p class="text-muted">{{ $user->last_login_at->format('Y-m-d H:i:s') }}</p>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="col-md-8">
                    <!-- User Details -->
                    <div class="card">
                        <div class="card-header p-2">
                            <ul class="nav nav-pills">
                                <li class="nav-item"><a class="nav-link active" href="#details" data-toggle="tab">التفاصيل</a></li>
                                <li class="nav-item"><a class="nav-link" href="#orders" data-toggle="tab">الطلبات</a></li>
                                <li class="nav-item"><a class="nav-link" href="#domains" data-toggle="tab">الدومينات</a></li>
                                <li class="nav-item"><a class="nav-link" href="#settings" data-toggle="tab">الإعدادات</a></li>
                            </ul>
                        </div>
                        <div class="card-body">
                            <div class="tab-content">
                                <!-- Details Tab -->
                                <div class="active tab-pane" id="details">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>الاسم الكامل</label>
                                                <p class="form-control-static">{{ $user->name }}</p>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>البريد الإلكتروني</label>
                                                <p class="form-control-static">{{ $user->email }}</p>
                                            </div>
                                        </div>
                                        @if($user->company)
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>الشركة</label>
                                                <p class="form-control-static">{{ $user->company }}</p>
                                            </div>
                                        </div>
                                        @endif
                                        @if($user->location)
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>المدينة</label>
                                                <p class="form-control-static">{{ $user->location }}</p>
                                            </div>
                                        </div>
                                        @endif
                                        @if($user->birth_date)
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>تاريخ الميلاد</label>
                                                <p class="form-control-static">{{ $user->birth_date }}</p>
                                            </div>
                                        </div>
                                        @endif
                                        @if($user->gender)
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label>الجنس</label>
                                                <p class="form-control-static">{{ $user->gender === 'male' ? 'ذكر' : 'أنثى' }}</p>
                                            </div>
                                        </div>
                                        @endif
                                        @if($user->bio)
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label>نبذة شخصية</label>
                                                <p class="form-control-static">{{ $user->bio }}</p>
                                            </div>
                                        </div>
                                        @endif
                                    </div>
                                </div>

                                <!-- Orders Tab -->
                                <div class="tab-pane" id="orders">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>رقم الطلب</th>
                                                    <th>نوع الخدمة</th>
                                                    <th>المبلغ</th>
                                                    <th>الحالة</th>
                                                    <th>التاريخ</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($recentOrders as $order)
                                                <tr>
                                                    <td>#{{ $order->id }}</td>
                                                    <td>{{ $order->service_type }}</td>
                                                    <td>${{ number_format($order->total_amount, 2) }}</td>
                                                    <td>
                                                        <span class="badge badge-{{ $order->status === 'completed' ? 'success' : ($order->status === 'pending' ? 'warning' : 'danger') }}">
                                                            {{ $order->status_text }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $order->created_at->format('Y-m-d') }}</td>
                                                    <td>
                                                        <a href="{{ route('admin.orders.show', $order) }}" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                                @empty
                                                <tr>
                                                    <td colspan="6" class="text-center">لا توجد طلبات</td>
                                                </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                    @if($recentOrders->count() >= 5)
                                    <div class="text-center">
                                        <a href="{{ route('admin.orders.index', ['user_id' => $user->id]) }}" class="btn btn-primary">
                                            عرض جميع الطلبات
                                        </a>
                                    </div>
                                    @endif
                                </div>

                                <!-- Domains Tab -->
                                <div class="tab-pane" id="domains">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>اسم الدومين</th>
                                                    <th>الحالة</th>
                                                    <th>تاريخ التسجيل</th>
                                                    <th>تاريخ الانتهاء</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @forelse($recentDomains as $domain)
                                                <tr>
                                                    <td>{{ $domain->domain_name }}</td>
                                                    <td>
                                                        <span class="badge badge-{{ $domain->status === 'نشط' ? 'success' : 'warning' }}">
                                                            {{ $domain->status }}
                                                        </span>
                                                    </td>
                                                    <td>{{ $domain->registration_date }}</td>
                                                    <td>{{ $domain->expiry_date }}</td>
                                                    <td>
                                                        <a href="{{ route('admin.domains.show', $domain) }}" class="btn btn-sm btn-info">
                                                            <i class="fas fa-eye"></i>
                                                        </a>
                                                    </td>
                                                </tr>
                                                @empty
                                                <tr>
                                                    <td colspan="5" class="text-center">لا توجد دومينات</td>
                                                </tr>
                                                @endforelse
                                            </tbody>
                                        </table>
                                    </div>
                                </div>

                                <!-- Settings Tab -->
                                <div class="tab-pane" id="settings">
                                    <form method="POST" action="{{ route('admin.users.reset-password', $user) }}">
                                        @csrf
                                        <div class="form-group">
                                            <label>كلمة مرور جديدة</label>
                                            <input type="password" name="new_password" class="form-control" required>
                                        </div>
                                        <div class="form-group">
                                            <label>تأكيد كلمة المرور</label>
                                            <input type="password" name="new_password_confirmation" class="form-control" required>
                                        </div>
                                        <button type="submit" class="btn btn-warning">
                                            <i class="fas fa-key"></i> تغيير كلمة المرور
                                        </button>
                                    </form>

                                    <hr>

                                    <h5>إعدادات الإشعارات</h5>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" {{ $user->email_notifications ? 'checked' : '' }} disabled>
                                                <label class="form-check-label">إشعارات البريد الإلكتروني</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" {{ $user->sms_notifications ? 'checked' : '' }} disabled>
                                                <label class="form-check-label">إشعارات الرسائل النصية</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" {{ $user->marketing_emails ? 'checked' : '' }} disabled>
                                                <label class="form-check-label">رسائل التسويق</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" {{ $user->order_updates ? 'checked' : '' }} disabled>
                                                <label class="form-check-label">تحديثات الطلبات</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
function toggleVerification(userId) {
    if (confirm('هل أنت متأكد من تغيير حالة تفعيل هذا المستخدم؟')) {
        $.ajax({
            url: `{{ route('admin.users.index') }}/${userId}/toggle-verification`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                location.reload();
            },
            error: function(xhr) {
                alert('حدث خطأ أثناء تغيير حالة التفعيل');
            }
        });
    }
}
</script>
@endpush
