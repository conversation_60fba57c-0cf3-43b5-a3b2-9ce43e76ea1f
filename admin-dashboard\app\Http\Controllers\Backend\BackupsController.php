<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use ZipArchive;

class BackupsController extends Controller
{
    protected $backupDisk = 'local';
    protected $backupPath = 'backups';

    /**
     * Display the backups page.
     */
    public function index()
    {
        $backups = $this->getBackupsList();
        return view('backend.backups.index', compact('backups'));
    }

    /**
     * Create a new backup.
     */
    public function create(Request $request)
    {
        try {
            $request->validate([
                'backup_type' => 'required|in:database,files,full',
                'backup_name' => 'nullable|string|max:255',
                'description' => 'nullable|string',
                'compress' => 'boolean',
                'encrypt' => 'boolean'
            ]);

            $backupType = $request->input('backup_type');
            $backupName = $request->input('backup_name') ?: $this->generateBackupName($backupType);
            $description = $request->input('description', '');
            $compress = $request->boolean('compress', true);
            $encrypt = $request->boolean('encrypt', false);

            $backupPath = $this->createBackup($backupType, $backupName, $description, $compress, $encrypt);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء النسخة الاحتياطية بنجاح',
                'backup_path' => $backupPath,
                'backup_name' => $backupName
            ]);

        } catch (\Exception $e) {
            Log::error('Backup creation failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Download a backup.
     */
    public function download($backup)
    {
        try {
            $backupPath = $this->backupPath . '/' . $backup;
            
            if (!Storage::disk($this->backupDisk)->exists($backupPath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'النسخة الاحتياطية غير موجودة'
                ], 404);
            }

            return Storage::disk($this->backupDisk)->download($backupPath);

        } catch (\Exception $e) {
            Log::error('Backup download failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحميل النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Restore a backup.
     */
    public function restore(Request $request, $backup)
    {
        try {
            $backupPath = $this->backupPath . '/' . $backup;
            
            if (!Storage::disk($this->backupDisk)->exists($backupPath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'النسخة الاحتياطية غير موجودة'
                ], 404);
            }

            $this->restoreBackup($backupPath);

            return response()->json([
                'success' => true,
                'message' => 'تم استعادة النسخة الاحتياطية بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('Backup restore failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء استعادة النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete a backup.
     */
    public function delete($backup)
    {
        try {
            $backupPath = $this->backupPath . '/' . $backup;
            
            if (!Storage::disk($this->backupDisk)->exists($backupPath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'النسخة الاحتياطية غير موجودة'
                ], 404);
            }

            Storage::disk($this->backupDisk)->delete($backupPath);

            return response()->json([
                'success' => true,
                'message' => 'تم حذف النسخة الاحتياطية بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('Backup deletion failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Verify a backup.
     */
    public function verify($backup)
    {
        try {
            $backupPath = $this->backupPath . '/' . $backup;
            
            if (!Storage::disk($this->backupDisk)->exists($backupPath)) {
                return response()->json([
                    'success' => false,
                    'message' => 'النسخة الاحتياطية غير موجودة'
                ], 404);
            }

            $isValid = $this->verifyBackup($backupPath);

            return response()->json([
                'success' => true,
                'valid' => $isValid,
                'message' => $isValid ? 'النسخة الاحتياطية سليمة' : 'النسخة الاحتياطية تالفة'
            ]);

        } catch (\Exception $e) {
            Log::error('Backup verification failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء التحقق من النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get backup status.
     */
    public function status()
    {
        try {
            $backups = $this->getBackupsList();
            $totalSize = 0;
            
            foreach ($backups as $backup) {
                $totalSize += $backup['size'];
            }

            return response()->json([
                'success' => true,
                'status' => [
                    'total_backups' => count($backups),
                    'total_size' => $totalSize,
                    'latest_backup' => $backups[0] ?? null,
                    'disk_usage' => $this->getDiskUsage()
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب حالة النسخ الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Schedule backup.
     */
    public function schedule(Request $request)
    {
        try {
            $request->validate([
                'backup_type' => 'required|in:database,files,full',
                'schedule_type' => 'required|in:daily,weekly,monthly',
                'time' => 'required|date_format:H:i'
            ]);

            // Store schedule in database or config
            // This would typically be handled by a job scheduler

            return response()->json([
                'success' => true,
                'message' => 'تم جدولة النسخة الاحتياطية بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جدولة النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk action on backups.
     */
    public function bulkAction(Request $request)
    {
        try {
            $action = $request->input('action');
            $backups = $request->input('backups', []);

            if (empty($backups)) {
                return response()->json([
                    'success' => false,
                    'message' => 'لم يتم تحديد أي نسخ احتياطية'
                ], 400);
            }

            switch ($action) {
                case 'delete':
                    $this->bulkDeleteBackups($backups);
                    $message = 'تم حذف النسخ الاحتياطية المحددة بنجاح';
                    break;
                case 'download':
                    return $this->bulkDownloadBackups($backups);
                default:
                    throw new \Exception('إجراء غير مدعوم');
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تنفيذ الإجراء: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Quick database backup.
     */
    public function quickBackupDatabase()
    {
        try {
            $backupName = $this->generateBackupName('database');
            $backupPath = $this->createDatabaseBackup($backupName);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء نسخة احتياطية سريعة لقاعدة البيانات',
                'backup_path' => $backupPath
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء النسخة الاحتياطية: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get backup status for API.
     */
    public function getBackupStatus()
    {
        return $this->status();
    }

    /**
     * Create backup based on type.
     */
    private function createBackup($type, $name, $description, $compress, $encrypt)
    {
        switch ($type) {
            case 'database':
                return $this->createDatabaseBackup($name, $compress, $encrypt);
            case 'files':
                return $this->createFilesBackup($name, $compress, $encrypt);
            case 'full':
                return $this->createFullBackup($name, $compress, $encrypt);
            default:
                throw new \Exception('نوع النسخة الاحتياطية غير مدعوم');
        }
    }

    /**
     * Create database backup.
     */
    private function createDatabaseBackup($name, $compress = true, $encrypt = false)
    {
        $filename = $name . '.sql';
        $backupPath = $this->backupPath . '/' . $filename;

        // Create database dump
        $databaseName = config('database.connections.mysql.database');
        $username = config('database.connections.mysql.username');
        $password = config('database.connections.mysql.password');
        $host = config('database.connections.mysql.host');

        $command = "mysqldump --user={$username} --password={$password} --host={$host} {$databaseName}";
        $output = shell_exec($command);

        if ($compress) {
            $filename = $name . '.sql.gz';
            $backupPath = $this->backupPath . '/' . $filename;
            $output = gzencode($output);
        }

        Storage::disk($this->backupDisk)->put($backupPath, $output);

        return $backupPath;
    }

    /**
     * Create files backup.
     */
    private function createFilesBackup($name, $compress = true, $encrypt = false)
    {
        $filename = $name . '.zip';
        $backupPath = $this->backupPath . '/' . $filename;
        $tempPath = storage_path('app/temp/' . $filename);

        // Create zip archive
        $zip = new ZipArchive();
        if ($zip->open($tempPath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('لا يمكن إنشاء ملف الضغط');
        }

        // Add files to zip
        $this->addFilesToZip($zip, storage_path('app'), 'storage');
        $this->addFilesToZip($zip, public_path(), 'public');

        $zip->close();

        // Move to backup location
        Storage::disk($this->backupDisk)->put($backupPath, file_get_contents($tempPath));
        unlink($tempPath);

        return $backupPath;
    }

    /**
     * Create full backup.
     */
    private function createFullBackup($name, $compress = true, $encrypt = false)
    {
        // Create database backup first
        $dbBackupPath = $this->createDatabaseBackup($name . '_db', false, false);
        
        // Create files backup
        $filesBackupPath = $this->createFilesBackup($name . '_files', false, false);

        // Combine into single archive
        $filename = $name . '.tar.gz';
        $backupPath = $this->backupPath . '/' . $filename;

        // This would typically use tar command or similar
        // For simplicity, we'll just return the database backup path
        return $dbBackupPath;
    }

    /**
     * Add files to zip recursively.
     */
    private function addFilesToZip($zip, $path, $zipPath = '')
    {
        $files = new \RecursiveIteratorIterator(
            new \RecursiveDirectoryIterator($path),
            \RecursiveIteratorIterator::LEAVES_ONLY
        );

        foreach ($files as $file) {
            if (!$file->isDir()) {
                $filePath = $file->getRealPath();
                $relativePath = $zipPath . '/' . substr($filePath, strlen($path) + 1);
                $zip->addFile($filePath, $relativePath);
            }
        }
    }

    /**
     * Restore backup.
     */
    private function restoreBackup($backupPath)
    {
        // Implementation depends on backup type
        // This is a simplified version
        throw new \Exception('استعادة النسخ الاحتياطية قيد التطوير');
    }

    /**
     * Verify backup integrity.
     */
    private function verifyBackup($backupPath)
    {
        // Check if file exists and is readable
        if (!Storage::disk($this->backupDisk)->exists($backupPath)) {
            return false;
        }

        // Additional verification logic would go here
        return true;
    }

    /**
     * Get list of backups.
     */
    private function getBackupsList()
    {
        $files = Storage::disk($this->backupDisk)->files($this->backupPath);
        $backups = [];

        foreach ($files as $file) {
            $backups[] = [
                'name' => basename($file),
                'path' => $file,
                'size' => Storage::disk($this->backupDisk)->size($file),
                'created_at' => Storage::disk($this->backupDisk)->lastModified($file),
                'type' => $this->getBackupType($file)
            ];
        }

        // Sort by creation date (newest first)
        usort($backups, function ($a, $b) {
            return $b['created_at'] - $a['created_at'];
        });

        return $backups;
    }

    /**
     * Get backup type from filename.
     */
    private function getBackupType($filename)
    {
        if (strpos($filename, '_db') !== false || strpos($filename, '.sql') !== false) {
            return 'database';
        } elseif (strpos($filename, '_files') !== false) {
            return 'files';
        } else {
            return 'full';
        }
    }

    /**
     * Generate backup name.
     */
    private function generateBackupName($type)
    {
        return $type . '_backup_' . now()->format('Y_m_d_H_i_s');
    }

    /**
     * Get disk usage.
     */
    private function getDiskUsage()
    {
        $totalSpace = disk_total_space('/');
        $freeSpace = disk_free_space('/');
        $usedSpace = $totalSpace - $freeSpace;

        return [
            'total' => $totalSpace,
            'used' => $usedSpace,
            'free' => $freeSpace,
            'percentage' => round(($usedSpace / $totalSpace) * 100, 2)
        ];
    }

    /**
     * Bulk delete backups.
     */
    private function bulkDeleteBackups($backups)
    {
        foreach ($backups as $backup) {
            $backupPath = $this->backupPath . '/' . $backup;
            if (Storage::disk($this->backupDisk)->exists($backupPath)) {
                Storage::disk($this->backupDisk)->delete($backupPath);
            }
        }
    }

    /**
     * Bulk download backups.
     */
    private function bulkDownloadBackups($backups)
    {
        // Create a zip file containing all selected backups
        $zipName = 'backups_' . now()->format('Y_m_d_H_i_s') . '.zip';
        $tempPath = storage_path('app/temp/' . $zipName);

        $zip = new ZipArchive();
        if ($zip->open($tempPath, ZipArchive::CREATE) !== TRUE) {
            throw new \Exception('لا يمكن إنشاء ملف الضغط');
        }

        foreach ($backups as $backup) {
            $backupPath = $this->backupPath . '/' . $backup;
            if (Storage::disk($this->backupDisk)->exists($backupPath)) {
                $content = Storage::disk($this->backupDisk)->get($backupPath);
                $zip->addFromString($backup, $content);
            }
        }

        $zip->close();

        return response()->download($tempPath, $zipName)->deleteFileAfterSend(true);
    }
}
