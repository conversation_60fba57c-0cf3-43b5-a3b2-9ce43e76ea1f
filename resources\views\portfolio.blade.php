@extends('layouts.app')

@section('title', 'أعمالنا - CodeCraft Solutions')
@section('description', 'استكشف مجموعة من أفضل أعمالنا وإنجازاتنا في مجال التطوير والتصميم')

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white py-20">
    <div class="absolute inset-0 bg-black opacity-50"></div>
    <div class="container mx-auto px-4 relative z-10">
        <div class="text-center">
            <h1 class="text-5xl font-bold mb-6" data-aos="fade-up">أعمالنا</h1>
            <p class="text-xl mb-8 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                نفخر بتقديم حلول تقنية متطورة ومواقع إلكترونية احترافية لعملائنا. استكشف مجموعة من 
                أفضل أعمالنا وإنجازاتنا في مجال التطوير والتصميم.
            </p>
            
            <!-- Category Filter -->
            <div class="flex flex-wrap justify-center gap-4 mt-8" data-aos="fade-up" data-aos-delay="400">
                <a href="{{ route('portfolio') }}" 
                   class="px-6 py-3 rounded-full {{ !request('category') ? 'bg-green-500 text-white' : 'bg-white/20 text-white hover:bg-white/30' }} transition-all duration-300">
                    جميع الأعمال
                </a>
                @foreach($categories as $key => $name)
                    @if($categoryCounts[$key] > 0)
                        <a href="{{ route('portfolio', ['category' => $key]) }}" 
                           class="px-6 py-3 rounded-full {{ request('category') == $key ? 'bg-green-500 text-white' : 'bg-white/20 text-white hover:bg-white/30' }} transition-all duration-300">
                            {{ $name }} ({{ $categoryCounts[$key] }})
                        </a>
                    @endif
                @endforeach
            </div>
        </div>
    </div>
</section>

@if($featuredPortfolios->count() > 0)
<!-- Featured Projects Section -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up">المشاريع المميزة</h2>
            <p class="text-gray-600 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
                مجموعة مختارة من أفضل مشاريعنا التي تعكس خبرتنا وإبداعنا في التطوير
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            @foreach($featuredPortfolios as $portfolio)
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" data-aos="fade-up" data-aos-delay="{{ $loop->index * 100 }}">
                    @if($portfolio->featured_image)
                        <div class="relative overflow-hidden h-48">
                            <img src="{{ Storage::url($portfolio->featured_image) }}" 
                                 alt="{{ $portfolio->title }}" 
                                 class="w-full h-full object-cover transition-transform duration-300 hover:scale-110">
                            <div class="absolute top-4 right-4">
                                <span class="bg-yellow-500 text-white px-3 py-1 rounded-full text-sm font-semibold">
                                    مميز
                                </span>
                            </div>
                        </div>
                    @endif
                    
                    <div class="p-6">
                        <div class="flex items-center justify-between mb-3">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">
                                {{ $categories[$portfolio->category] ?? $portfolio->category }}
                            </span>
                            @if($portfolio->client_name)
                                <span class="text-gray-500 text-sm">{{ $portfolio->client_name }}</span>
                            @endif
                        </div>
                        
                        <h3 class="text-xl font-bold text-gray-800 mb-3">{{ $portfolio->title }}</h3>
                        <p class="text-gray-600 mb-4 line-clamp-3">{{ $portfolio->description }}</p>
                        
                        <div class="flex items-center justify-between">
                            <a href="{{ route('portfolio.show', $portfolio) }}" 
                               class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-2 rounded-lg hover:from-blue-600 hover:to-purple-700 transition-all duration-300">
                                عرض التفاصيل
                            </a>
                            @if($portfolio->live_url)
                                <a href="{{ $portfolio->live_url }}" 
                                   target="_blank" 
                                   class="text-green-500 hover:text-green-600 transition-colors duration-300">
                                    <i class="fas fa-external-link-alt"></i>
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif

<!-- All Projects Section -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-4xl font-bold text-gray-800 mb-4" data-aos="fade-up">
                @if(request('category'))
                    {{ $categories[request('category')] }}
                @else
                    جميع أعمالنا
                @endif
            </h2>
            <p class="text-gray-600" data-aos="fade-up" data-aos-delay="200">
                {{ $portfolios->total() }} مشروع
            </p>
        </div>

        @if($portfolios->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                @foreach($portfolios as $portfolio)
                    <div class="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300" data-aos="fade-up" data-aos-delay="{{ $loop->index * 50 }}">
                        @if($portfolio->featured_image)
                            <div class="relative overflow-hidden h-40">
                                <img src="{{ Storage::url($portfolio->featured_image) }}" 
                                     alt="{{ $portfolio->title }}" 
                                     class="w-full h-full object-cover transition-transform duration-300 hover:scale-105">
                                @if($portfolio->is_featured)
                                    <div class="absolute top-2 right-2">
                                        <span class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-semibold">
                                            مميز
                                        </span>
                                    </div>
                                @endif
                            </div>
                        @endif
                        
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                                    {{ $categories[$portfolio->category] ?? $portfolio->category }}
                                </span>
                                @if($portfolio->completion_date)
                                    <span class="text-gray-500 text-xs">
                                        {{ $portfolio->completion_date->format('Y') }}
                                    </span>
                                @endif
                            </div>
                            
                            <h3 class="font-bold text-gray-800 mb-2 line-clamp-2">{{ $portfolio->title }}</h3>
                            <p class="text-gray-600 text-sm mb-3 line-clamp-2">{{ $portfolio->description }}</p>
                            
                            <div class="flex items-center justify-between">
                                <a href="{{ route('portfolio.show', $portfolio) }}" 
                                   class="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600 transition-colors duration-300">
                                    عرض
                                </a>
                                @if($portfolio->live_url)
                                    <a href="{{ $portfolio->live_url }}" 
                                       target="_blank" 
                                       class="text-green-500 hover:text-green-600 transition-colors duration-300">
                                        <i class="fas fa-external-link-alt text-sm"></i>
                                    </a>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-12 flex justify-center">
                {{ $portfolios->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <div class="text-gray-400 mb-4">
                    <i class="fas fa-folder-open text-6xl"></i>
                </div>
                <h3 class="text-xl font-semibold text-gray-600 mb-2">لا توجد مشاريع</h3>
                <p class="text-gray-500">
                    @if(request('category'))
                        لا توجد مشاريع في هذه الفئة حالياً
                    @else
                        لا توجد مشاريع متاحة حالياً
                    @endif
                </p>
            </div>
        @endif
    </div>
</section>

<!-- CTA Section -->
<section class="py-16 bg-gradient-to-r from-blue-600 to-purple-700 text-white">
    <div class="container mx-auto px-4 text-center">
        <h2 class="text-3xl font-bold mb-4" data-aos="fade-up">هل لديك مشروع في ذهنك؟</h2>
        <p class="text-xl mb-8 max-w-2xl mx-auto" data-aos="fade-up" data-aos-delay="200">
            دعنا نساعدك في تحويل فكرتك إلى واقع رقمي مذهل
        </p>
        <a href="{{ route('contact') }}" 
           class="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300" 
           data-aos="fade-up" data-aos-delay="400">
            ابدأ مشروعك الآن
        </a>
    </div>
</section>
@endsection

@push('styles')
<style>
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}
</style>
@endpush
