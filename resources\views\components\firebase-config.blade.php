<!-- Firebase Configuration Script -->
<script>
    // Firebase configuration from Laravel environment
    window.firebaseConfig = {
        apiKey: "{{ config('firebase.api_key') }}",
        authDomain: "{{ config('firebase.auth_domain') }}",
        projectId: "{{ config('firebase.project_id') }}",
        storageBucket: "{{ config('firebase.storage_bucket') }}",
        messagingSenderId: "{{ config('firebase.messaging_sender_id') }}",
        appId: "{{ config('firebase.app_id') }}",
        measurementId: "{{ config('firebase.measurement_id') }}"
    };

    // Make Vite environment variables available (if using Vite)
    window.viteEnv = {
        VITE_FIREBASE_API_KEY: "{{ env('VITE_FIREBASE_API_KEY') }}",
        VITE_FIREBASE_AUTH_DOMAIN: "{{ env('VITE_FIREBASE_AUTH_DOMAIN') }}",
        VITE_FIREBASE_PROJECT_ID: "{{ env('VITE_FIREBASE_PROJECT_ID') }}",
        VITE_FIREBASE_STORAGE_BUCKET: "{{ env('VITE_FIREBASE_STORAGE_BUCKET') }}",
        VITE_FIREBASE_MESSAGING_SENDER_ID: "{{ env('VITE_FIREBASE_MESSAGING_SENDER_ID') }}",
        VITE_FIREBASE_APP_ID: "{{ env('VITE_FIREBASE_APP_ID') }}",
        VITE_FIREBASE_MEASUREMENT_ID: "{{ env('VITE_FIREBASE_MEASUREMENT_ID') }}"
    };
</script>

<!-- Firebase SDK -->
<script type="module">
    // Import Firebase functions
    import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
    import { getAuth, GoogleAuthProvider, signInWithPopup } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
    import { getAnalytics } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-analytics.js';
    
    // Initialize Firebase
    const app = initializeApp(window.firebaseConfig);
    const auth = getAuth(app);
    const analytics = getAnalytics(app);
    const googleProvider = new GoogleAuthProvider();
    
    // Google Sign In function
    window.signInWithGoogle = async () => {
        try {
            const result = await signInWithPopup(auth, googleProvider);
            const user = result.user;
            
            // Send user data to Laravel backend
            const response = await fetch('/auth/firebase/callback', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify({
                    uid: user.uid,
                    name: user.displayName,
                    email: user.email,
                    avatar: user.photoURL,
                    provider: 'google'
                })
            });
            
            if (response.ok) {
                const data = await response.json();
                if (data.redirect) {
                    window.location.href = data.redirect;
                }
            } else {
                throw new Error('فشل في تسجيل الدخول');
            }
            
            return user;
        } catch (error) {
            console.error('خطأ في تسجيل الدخول عبر Google:', error);
            throw error;
        }
    };
    
    // Sign out function
    window.signOutUser = async () => {
        try {
            await auth.signOut();
            console.log('تم تسجيل الخروج بنجاح');
        } catch (error) {
            console.error('خطأ في تسجيل الخروج:', error);
            throw error;
        }
    };
    
    // Make Firebase objects available globally
    window.firebaseApp = app;
    window.firebaseAuth = auth;
    window.firebaseAnalytics = analytics;
</script>
