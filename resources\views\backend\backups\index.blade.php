@extends('backend.layouts.app')

@section('title', 'النسخ الاحتياطية')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-database me-2 text-primary"></i>
                        النسخ الاحتياطية
                    </h1>
                    <p class="text-muted mb-0">إدارة وإنشاء النسخ الاحتياطية لقاعدة البيانات والملفات</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleDarkMode()" id="darkModeToggle" title="تبديل الوضع المظلم">
                        <i class="fas fa-moon" id="darkModeIcon"></i>
                    </button>
                    <button class="btn btn-success" onclick="createBackup()">
                        <i class="fas fa-plus me-2"></i>
                        إنشاء نسخة احتياطية
                    </button>
                    <button class="btn btn-outline-primary" onclick="scheduleBackup()">
                        <i class="fas fa-clock me-2"></i>
                        جدولة النسخ
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">إجمالي النسخ</h6>
                            <h3 class="mb-0">24</h3>
                        </div>
                        <div class="backup-icon">
                            <i class="fas fa-archive fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">آخر نسخة</h6>
                            <h3 class="mb-0">اليوم</h3>
                        </div>
                        <div class="backup-icon">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">الحجم الإجمالي</h6>
                            <h3 class="mb-0">2.4 GB</h3>
                        </div>
                        <div class="backup-icon">
                            <i class="fas fa-hdd fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">النسخ المجدولة</h6>
                            <h3 class="mb-0">3</h3>
                        </div>
                        <div class="backup-icon">
                            <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-tools text-primary me-2"></i>
                        إجراءات النسخ الاحتياطي
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="backup-action-card" onclick="createDatabaseBackup()">
                                <div class="action-icon bg-primary">
                                    <i class="fas fa-database"></i>
                                </div>
                                <div class="action-content">
                                    <h6>نسخة قاعدة البيانات</h6>
                                    <p class="text-muted mb-0">إنشاء نسخة احتياطية من قاعدة البيانات فقط</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="backup-action-card" onclick="createFilesBackup()">
                                <div class="action-icon bg-success">
                                    <i class="fas fa-folder"></i>
                                </div>
                                <div class="action-content">
                                    <h6>نسخة الملفات</h6>
                                    <p class="text-muted mb-0">إنشاء نسخة احتياطية من ملفات النظام</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="backup-action-card" onclick="createFullBackup()">
                                <div class="action-icon bg-info">
                                    <i class="fas fa-server"></i>
                                </div>
                                <div class="action-content">
                                    <h6>نسخة شاملة</h6>
                                    <p class="text-muted mb-0">نسخة احتياطية كاملة للنظام والملفات</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Backup List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list text-primary me-2"></i>
                            قائمة النسخ الاحتياطية
                        </h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" style="width: auto;" onchange="filterBackups(this.value)">
                                <option value="">جميع النسخ</option>
                                <option value="database">قاعدة البيانات</option>
                                <option value="files">الملفات</option>
                                <option value="full">شاملة</option>
                            </select>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshBackupList()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="form-check-input" onchange="toggleSelectAll(this)">
                                    </th>
                                    <th>اسم النسخة</th>
                                    <th>النوع</th>
                                    <th>الحجم</th>
                                    <th>تاريخ الإنشاء</th>
                                    <th>الحالة</th>
                                    <th width="150">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input backup-checkbox" value="backup_1">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-database text-primary me-2"></i>
                                            <div>
                                                <div class="fw-bold">database_backup_2024_01_15.sql</div>
                                                <small class="text-muted">نسخة تلقائية يومية</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">قاعدة البيانات</span>
                                    </td>
                                    <td>45.2 MB</td>
                                    <td>
                                        <div>15 يناير 2024</div>
                                        <small class="text-muted">02:00 ص</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">مكتملة</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" onclick="downloadBackup('backup_1')" title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="restoreBackup('backup_1')" title="استعادة">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="verifyBackup('backup_1')" title="التحقق">
                                                <i class="fas fa-check-circle"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteBackup('backup_1')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input backup-checkbox" value="backup_2">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-folder text-success me-2"></i>
                                            <div>
                                                <div class="fw-bold">files_backup_2024_01_14.zip</div>
                                                <small class="text-muted">نسخة يدوية</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">الملفات</span>
                                    </td>
                                    <td>1.2 GB</td>
                                    <td>
                                        <div>14 يناير 2024</div>
                                        <small class="text-muted">03:30 م</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">مكتملة</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" onclick="downloadBackup('backup_2')" title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="restoreBackup('backup_2')" title="استعادة">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="verifyBackup('backup_2')" title="التحقق">
                                                <i class="fas fa-check-circle"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteBackup('backup_2')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input backup-checkbox" value="backup_3">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-server text-info me-2"></i>
                                            <div>
                                                <div class="fw-bold">full_backup_2024_01_13.tar.gz</div>
                                                <small class="text-muted">نسخة أسبوعية</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">شاملة</span>
                                    </td>
                                    <td>2.8 GB</td>
                                    <td>
                                        <div>13 يناير 2024</div>
                                        <small class="text-muted">01:00 ص</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">مكتملة</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" onclick="downloadBackup('backup_3')" title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="restoreBackup('backup_3')" title="استعادة">
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="verifyBackup('backup_3')" title="التحقق">
                                                <i class="fas fa-check-circle"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteBackup('backup_3')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input backup-checkbox" value="backup_4">
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-database text-warning me-2"></i>
                                            <div>
                                                <div class="fw-bold">database_backup_2024_01_12.sql</div>
                                                <small class="text-muted">نسخة قبل التحديث</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-primary">قاعدة البيانات</span>
                                    </td>
                                    <td>42.8 MB</td>
                                    <td>
                                        <div>12 يناير 2024</div>
                                        <small class="text-muted">11:45 ص</small>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">جاري التحقق</span>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" onclick="downloadBackup('backup_4')" title="تحميل">
                                                <i class="fas fa-download"></i>
                                            </button>
                                            <button class="btn btn-outline-success" onclick="restoreBackup('backup_4')" title="استعادة" disabled>
                                                <i class="fas fa-undo"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="verifyBackup('backup_4')" title="التحقق">
                                                <i class="fas fa-spinner fa-spin"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteBackup('backup_4')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Bulk Actions Footer -->
                <div class="card-footer bg-light border-0" id="bulkActions" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted" id="selectedBackupsCount">0 نسخة محددة</span>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-info" onclick="downloadSelectedBackups()">
                                <i class="fas fa-download me-1"></i>
                                تحميل المحدد
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteSelectedBackups()">
                                <i class="fas fa-trash me-1"></i>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Backup Modal -->
<x-backend.advanced-modal 
    id="createBackupModal"
    title="إنشاء نسخة احتياطية"
    size="modal-lg"
    :centered="true">
    
    <form id="createBackupForm">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">نوع النسخة الاحتياطية</label>
                    <select class="form-select" name="backup_type" required>
                        <option value="">اختر نوع النسخة</option>
                        <option value="database">قاعدة البيانات فقط</option>
                        <option value="files">الملفات فقط</option>
                        <option value="full">نسخة شاملة</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">اسم النسخة</label>
                    <input type="text" class="form-control" name="backup_name" placeholder="اختياري - سيتم إنشاء اسم تلقائي">
                </div>
            </div>
            <div class="col-12">
                <div class="mb-3">
                    <label class="form-label">الوصف</label>
                    <textarea class="form-control" name="description" rows="3" placeholder="وصف اختياري للنسخة الاحتياطية"></textarea>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="compress" id="compress" checked>
                        <label class="form-check-label" for="compress">
                            ضغط النسخة الاحتياطية
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="encrypt" id="encrypt">
                        <label class="form-check-label" for="encrypt">
                            تشفير النسخة الاحتياطية
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </form>
    
    <x-slot name="footerLeft">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
    </x-slot>
    
    <button type="button" class="btn btn-success" onclick="executeBackupCreation()">
        <i class="fas fa-play me-2"></i>
        إنشاء النسخة
    </button>
</x-backend.advanced-modal>

<!-- Backup Progress Modal -->
<x-backend.advanced-modal 
    id="backupProgressModal"
    title="جاري إنشاء النسخة الاحتياطية"
    size="modal-md"
    :centered="true"
    :backdrop="'static'"
    :keyboard="false">
    
    <div class="backup-progress-content">
        <div class="text-center mb-4">
            <div class="backup-progress-icon">
                <i class="fas fa-database fa-3x text-primary"></i>
            </div>
            <h5 class="mt-3">جاري إنشاء النسخة الاحتياطية...</h5>
            <p class="text-muted">يرجى عدم إغلاق هذه النافذة</p>
        </div>
        
        <div class="progress mb-3" style="height: 20px;">
            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                 role="progressbar" 
                 style="width: 0%" 
                 id="backupProgressBar">
                0%
            </div>
        </div>
        
        <div class="backup-status">
            <div class="d-flex justify-content-between mb-2">
                <span>الحالة:</span>
                <span id="backupStatus">بدء العملية...</span>
            </div>
            <div class="d-flex justify-content-between mb-2">
                <span>الوقت المتبقي:</span>
                <span id="estimatedTime">حساب...</span>
            </div>
            <div class="d-flex justify-content-between">
                <span>الحجم:</span>
                <span id="backupSize">0 MB</span>
            </div>
        </div>
    </div>
    
    <div class="text-center" style="display: none;" id="backupComplete">
        <i class="fas fa-check-circle fa-3x text-success mb-3"></i>
        <h5>تم إنشاء النسخة الاحتياطية بنجاح!</h5>
        <p class="text-muted">يمكنك الآن إغلاق هذه النافذة</p>
    </div>
    
    <x-slot name="footerLeft">
        <button type="button" class="btn btn-danger" onclick="cancelBackup()" id="cancelBackupBtn">
            إلغاء العملية
        </button>
    </x-slot>
    
    <button type="button" class="btn btn-success" data-bs-dismiss="modal" style="display: none;" id="closeBackupBtn">
        إغلاق
    </button>
</x-backend.advanced-modal>
@endsection

@push('after-styles')
<style>
/* Dark Mode Styles for Backups Page */
body.dark-mode {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
}

body.dark-mode .card {
    background: #374151;
    color: #e5e7eb;
    border-color: #4b5563;
}

body.dark-mode .card-header {
    background: #4b5563;
    border-bottom-color: #6b7280;
    color: #e5e7eb;
}

body.dark-mode .table {
    color: #e5e7eb;
}

body.dark-mode .table th {
    background: #4b5563;
    color: #d1d5db;
    border-color: #6b7280;
}

body.dark-mode .table td {
    border-color: #4b5563;
}

body.dark-mode .table tbody tr {
    border-bottom-color: #4b5563;
}

body.dark-mode .table tbody tr:hover {
    background-color: rgba(75, 85, 99, 0.3);
}

body.dark-mode .form-control,
body.dark-mode .form-select {
    background: #4b5563;
    border-color: #6b7280;
    color: #e5e7eb;
}

body.dark-mode .form-control:focus,
body.dark-mode .form-select:focus {
    background: #4b5563;
    border-color: #667eea;
    color: #e5e7eb;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

body.dark-mode .btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
}

body.dark-mode .btn-outline-primary:hover {
    background: #667eea;
    border-color: #667eea;
}

body.dark-mode .btn-outline-secondary {
    border-color: #6b7280;
    color: #e5e7eb;
}

body.dark-mode .btn-outline-secondary:hover {
    background: #6b7280;
    border-color: #6b7280;
}

body.dark-mode .modal-content {
    background: #374151;
    color: #e5e7eb;
}

body.dark-mode .modal-header {
    border-bottom-color: #4b5563;
}

body.dark-mode .modal-footer {
    border-top-color: #4b5563;
}

body.dark-mode .backup-action-card {
    background: #4b5563;
    border-color: #6b7280;
    color: #e5e7eb;
}

body.dark-mode .backup-action-card:hover {
    border-color: #667eea;
}

body.dark-mode .backup-status {
    background: #4b5563;
    color: #e5e7eb;
}

body.dark-mode .alert {
    background: #4b5563;
    border-color: #6b7280;
    color: #e5e7eb;
}

body.dark-mode .text-muted {
    color: #9ca3af !important;
}</style>
.backup-icon {
    opacity: 0.7;
}

.backup-action-card {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.backup-action-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.action-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    flex-shrink: 0;
}

.action-content h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.action-content p {
    margin: 0;
    font-size: 0.875rem;
}

.backup-progress-icon {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.3s ease;
    font-weight: 600;
    font-size: 0.875rem;
}

.backup-status {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 1rem;
    font-size: 0.875rem;
}

.table th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    color: #6c757d;
}

.table td {
    border: none;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.backup-checkbox:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-group .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

#bulkActions {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Status indicators */
.badge.bg-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
}

/* Responsive */
@media (max-width: 768px) {
    .backup-action-card {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .action-icon {
        margin-bottom: 1rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.25rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }
}

/* Loading states */
.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Success animations */
.success-animation {
    animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}
</style>
@endpush

@push('after-scripts')
<script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
<script>
let selectedBackups = [];
let backupInProgress = false;

document.addEventListener('DOMContentLoaded', function() {
    initializeBackupManager();
    setupEventListeners();
    initializeDarkMode();
});

function initializeBackupManager() {
    // Setup checkbox event listeners
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('backup-checkbox')) {
            updateSelectedBackups();
        }
    });

    // Check backup status on page load
    checkBackupStatus();
}

function setupEventListeners() {
    // Auto-refresh backup list every 30 seconds
    setInterval(() => {
        if (!backupInProgress) {
            refreshBackupList();
        }
    }, 30000);
}

function createBackup() {
    const modal = new bootstrap.Modal(document.getElementById('createBackupModal'));
    modal.show();
}

function createDatabaseBackup() {
    executeBackup('database', 'نسخة احتياطية لقاعدة البيانات');
}

function createFilesBackup() {
    executeBackup('files', 'نسخة احتياطية للملفات');
}

function createFullBackup() {
    executeBackup('full', 'نسخة احتياطية شاملة');
}

function executeBackup(type, description) {
    if (backupInProgress) {
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.warning('يوجد نسخة احتياطية قيد التنفيذ حالياً');
        }
        return;
    }

    const backupData = {
        type: type,
        description: description,
        compress: true,
        encrypt: false
    };

    startBackupProcess(backupData);
}

function executeBackupCreation() {
    const form = document.getElementById('createBackupForm');
    const formData = new FormData(form);

    const backupData = {
        type: formData.get('backup_type'),
        name: formData.get('backup_name'),
        description: formData.get('description'),
        compress: formData.has('compress'),
        encrypt: formData.has('encrypt')
    };

    if (!backupData.type) {
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.error('يرجى اختيار نوع النسخة الاحتياطية');
        }
        return;
    }

    // Close create modal
    const createModal = bootstrap.Modal.getInstance(document.getElementById('createBackupModal'));
    createModal.hide();

    // Start backup process
    startBackupProcess(backupData);
}

function startBackupProcess(backupData) {
    backupInProgress = true;

    // Show progress modal
    const progressModal = new bootstrap.Modal(document.getElementById('backupProgressModal'));
    progressModal.show();

    // Reset progress
    resetBackupProgress();

    // Start backup simulation
    simulateBackupProgress(backupData);
}

function resetBackupProgress() {
    document.getElementById('backupProgressBar').style.width = '0%';
    document.getElementById('backupProgressBar').textContent = '0%';
    document.getElementById('backupStatus').textContent = 'بدء العملية...';
    document.getElementById('estimatedTime').textContent = 'حساب...';
    document.getElementById('backupSize').textContent = '0 MB';

    document.querySelector('.backup-progress-content').style.display = 'block';
    document.getElementById('backupComplete').style.display = 'none';
    document.getElementById('cancelBackupBtn').style.display = 'block';
    document.getElementById('closeBackupBtn').style.display = 'none';
}

function simulateBackupProgress(backupData) {
    let progress = 0;
    let startTime = Date.now();

    const progressInterval = setInterval(() => {
        progress += Math.random() * 10;
        if (progress > 100) progress = 100;

        // Update progress bar
        const progressBar = document.getElementById('backupProgressBar');
        progressBar.style.width = progress + '%';
        progressBar.textContent = Math.round(progress) + '%';

        // Update status
        updateBackupStatus(progress, backupData.type);

        // Update estimated time
        const elapsed = (Date.now() - startTime) / 1000;
        const estimated = (elapsed / progress) * (100 - progress);
        document.getElementById('estimatedTime').textContent = formatTime(estimated);

        // Update size
        const size = (progress / 100) * getEstimatedSize(backupData.type);
        document.getElementById('backupSize').textContent = formatSize(size);

        if (progress >= 100) {
            clearInterval(progressInterval);
            completeBackup();
        }
    }, 500);

    // Store interval for cancellation
    window.currentBackupInterval = progressInterval;
}

function updateBackupStatus(progress, type) {
    const statusElement = document.getElementById('backupStatus');

    if (progress < 20) {
        statusElement.textContent = 'تحضير البيانات...';
    } else if (progress < 40) {
        statusElement.textContent = 'قراءة الملفات...';
    } else if (progress < 60) {
        statusElement.textContent = 'ضغط البيانات...';
    } else if (progress < 80) {
        statusElement.textContent = 'كتابة النسخة الاحتياطية...';
    } else if (progress < 95) {
        statusElement.textContent = 'التحقق من سلامة البيانات...';
    } else {
        statusElement.textContent = 'إنهاء العملية...';
    }
}

function getEstimatedSize(type) {
    const sizes = {
        'database': 50, // MB
        'files': 1200, // MB
        'full': 2800 // MB
    };
    return sizes[type] || 100;
}

function formatSize(sizeInMB) {
    if (sizeInMB < 1024) {
        return Math.round(sizeInMB) + ' MB';
    } else {
        return (sizeInMB / 1024).toFixed(1) + ' GB';
    }
}

function formatTime(seconds) {
    if (seconds < 60) {
        return Math.round(seconds) + ' ثانية';
    } else if (seconds < 3600) {
        return Math.round(seconds / 60) + ' دقيقة';
    } else {
        return Math.round(seconds / 3600) + ' ساعة';
    }
}

function completeBackup() {
    backupInProgress = false;

    // Hide progress content
    document.querySelector('.backup-progress-content').style.display = 'none';

    // Show completion message
    document.getElementById('backupComplete').style.display = 'block';
    document.getElementById('cancelBackupBtn').style.display = 'none';
    document.getElementById('closeBackupBtn').style.display = 'block';

    // Refresh backup list
    setTimeout(() => {
        refreshBackupList();
    }, 1000);

    if (typeof NotificationSystem !== 'undefined') {
        NotificationSystem.success('تم إنشاء النسخة الاحتياطية بنجاح');
    }
}

function cancelBackup() {
    if (confirm('هل أنت متأكد من إلغاء عملية النسخ الاحتياطي؟')) {
        if (window.currentBackupInterval) {
            clearInterval(window.currentBackupInterval);
        }

        backupInProgress = false;

        const progressModal = bootstrap.Modal.getInstance(document.getElementById('backupProgressModal'));
        progressModal.hide();

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.warning('تم إلغاء عملية النسخ الاحتياطي');
        }
    }
}

function downloadBackup(backupId) {
    const btn = event.target.closest('button');
    const originalHTML = btn.innerHTML;

    btn.classList.add('loading');
    btn.disabled = true;

    // Simulate download
    setTimeout(() => {
        // Create download link
        const link = document.createElement('a');
        link.href = `/admin/backups/download/${backupId}`;
        link.download = `backup_${backupId}.zip`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        btn.classList.remove('loading');
        btn.disabled = false;
        btn.innerHTML = originalHTML;

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم بدء تحميل النسخة الاحتياطية');
        }
    }, 2000);
}

function restoreBackup(backupId) {
    if (confirm('هل أنت متأكد من استعادة هذه النسخة الاحتياطية؟ سيتم استبدال البيانات الحالية.')) {
        const btn = event.target.closest('button');
        const originalHTML = btn.innerHTML;

        btn.classList.add('loading');
        btn.disabled = true;

        // Simulate restore
        setTimeout(() => {
            btn.classList.remove('loading');
            btn.disabled = false;
            btn.innerHTML = originalHTML;

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم استعادة النسخة الاحتياطية بنجاح');
            }
        }, 5000);
    }
}

function verifyBackup(backupId) {
    const btn = event.target.closest('button');
    const originalHTML = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    // Simulate verification
    setTimeout(() => {
        btn.innerHTML = '<i class="fas fa-check text-success"></i>';
        btn.classList.remove('btn-outline-warning');
        btn.classList.add('btn-outline-success');

        setTimeout(() => {
            btn.innerHTML = originalHTML;
            btn.classList.remove('btn-outline-success');
            btn.classList.add('btn-outline-warning');
            btn.disabled = false;
        }, 2000);

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم التحقق من سلامة النسخة الاحتياطية');
        }
    }, 3000);
}

function deleteBackup(backupId) {
    if (confirm('هل أنت متأكد من حذف هذه النسخة الاحتياطية؟ لا يمكن التراجع عن هذا الإجراء.')) {
        const btn = event.target.closest('button');
        const row = btn.closest('tr');

        btn.classList.add('loading');
        btn.disabled = true;

        // Simulate deletion
        setTimeout(() => {
            row.style.transition = 'all 0.3s ease';
            row.style.opacity = '0';
            row.style.transform = 'translateX(100px)';

            setTimeout(() => {
                row.remove();
                updateSelectedBackups();
            }, 300);

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم حذف النسخة الاحتياطية');
            }
        }, 1500);
    }
}

function toggleSelectAll(checkbox) {
    const backupCheckboxes = document.querySelectorAll('.backup-checkbox');
    backupCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateSelectedBackups();
}

function updateSelectedBackups() {
    const checkedBoxes = document.querySelectorAll('.backup-checkbox:checked');
    selectedBackups = Array.from(checkedBoxes).map(cb => cb.value);

    const selectedCount = document.getElementById('selectedBackupsCount');
    const bulkActions = document.getElementById('bulkActions');

    if (selectedBackups.length > 0) {
        selectedCount.textContent = `${selectedBackups.length} نسخة محددة`;
        bulkActions.style.display = 'block';
    } else {
        bulkActions.style.display = 'none';
    }
}

function downloadSelectedBackups() {
    if (selectedBackups.length === 0) return;

    if (typeof NotificationSystem !== 'undefined') {
        NotificationSystem.info(`جاري تحميل ${selectedBackups.length} نسخة احتياطية...`);
    }

    // Simulate batch download
    selectedBackups.forEach((backupId, index) => {
        setTimeout(() => {
            const link = document.createElement('a');
            link.href = `/admin/backups/download/${backupId}`;
            link.download = `backup_${backupId}.zip`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }, index * 1000);
    });
}

function deleteSelectedBackups() {
    if (selectedBackups.length === 0) return;

    if (confirm(`هل أنت متأكد من حذف ${selectedBackups.length} نسخة احتياطية؟`)) {
        const rows = selectedBackups.map(id =>
            document.querySelector(`input[value="${id}"]`).closest('tr')
        );

        rows.forEach((row, index) => {
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(100px)';

                setTimeout(() => {
                    row.remove();
                }, 300);
            }, index * 100);
        });

        setTimeout(() => {
            selectedBackups = [];
            updateSelectedBackups();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم حذف النسخ الاحتياطية المحددة');
            }
        }, rows.length * 100 + 300);
    }
}

function filterBackups(type) {
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const badge = row.querySelector('.badge');
        const backupType = badge.textContent.trim();

        if (!type ||
            (type === 'database' && backupType === 'قاعدة البيانات') ||
            (type === 'files' && backupType === 'الملفات') ||
            (type === 'full' && backupType === 'شاملة')) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function refreshBackupList() {
    const btn = event?.target || document.querySelector('[onclick="refreshBackupList()"]');
    if (btn) {
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;

        setTimeout(() => {
            btn.innerHTML = originalHTML;
            btn.disabled = false;

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم تحديث قائمة النسخ الاحتياطية');
            }
        }, 1500);
    }
}

function scheduleBackup() {
    if (typeof NotificationSystem !== 'undefined') {
        NotificationSystem.info('ميزة جدولة النسخ الاحتياطية قيد التطوير');
    }
}

function checkBackupStatus() {
    // Check if there's an ongoing backup process
    fetch('/admin/backups/status')
        .then(response => response.json())
        .then(data => {
            if (data.inProgress) {
                // Resume backup progress display
                backupInProgress = true;
                // Show progress modal and continue from where it left off
            }
        })
        .catch(error => {
            console.error('Error checking backup status:', error);
        });
}

// Dark Mode Functions
function toggleDarkMode() {
    const body = document.body;
    const darkModeIcon = document.getElementById('darkModeIcon');
    const isDarkMode = body.classList.contains('dark-mode');

    if (isDarkMode) {
        body.classList.remove('dark-mode');
        darkModeIcon.className = 'fas fa-moon';
        localStorage.setItem('darkMode', 'false');
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.info('تم تفعيل الوضع العادي');
        }
    } else {
        body.classList.add('dark-mode');
        darkModeIcon.className = 'fas fa-sun';
        localStorage.setItem('darkMode', 'true');
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.info('تم تفعيل الوضع المظلم');
        }
    }
}

function initializeDarkMode() {
    const savedDarkMode = localStorage.getItem('darkMode');
    const darkModeIcon = document.getElementById('darkModeIcon');

    if (savedDarkMode === 'true') {
        document.body.classList.add('dark-mode');
        if (darkModeIcon) darkModeIcon.className = 'fas fa-sun';
    } else {
        document.body.classList.remove('dark-mode');
        if (darkModeIcon) darkModeIcon.className = 'fas fa-moon';
    }
}
</script>
@endpush
