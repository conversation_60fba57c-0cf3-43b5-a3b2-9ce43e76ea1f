{{-- قالب المودال المتقدم --}}
@props([
    'id' => 'advancedModal',
    'title' => 'نافذة منبثقة',
    'size' => 'modal-lg', // modal-sm, modal-lg, modal-xl, modal-fullscreen
    'centered' => true,
    'scrollable' => true,
    'backdrop' => 'static',
    'keyboard' => true,
    'focus' => true,
    'headerClass' => 'bg-primary text-white',
    'footerClass' => 'bg-light',
    'animation' => 'fade', // fade, slide, zoom
    'confirmButton' => true,
    'cancelButton' => true,
    'confirmText' => 'تأكيد',
    'cancelText' => 'إلغاء',
    'confirmClass' => 'btn-primary',
    'cancelClass' => 'btn-secondary',
    'onConfirm' => null,
    'onCancel' => null,
    'loading' => false,
    'steps' => null,
    'draggable' => false
])

<div class="modal {{ $animation === 'fade' ? 'fade' : '' }}" 
     id="{{ $id }}" 
     tabindex="-1" 
     aria-labelledby="{{ $id }}Label" 
     aria-hidden="true"
     data-bs-backdrop="{{ $backdrop }}"
     data-bs-keyboard="{{ $keyboard ? 'true' : 'false' }}"
     x-data="advancedModal()" 
     x-init="init()">
    
    <div class="modal-dialog {{ $size }} {{ $centered ? 'modal-dialog-centered' : '' }} {{ $scrollable ? 'modal-dialog-scrollable' : '' }}"
         :class="{ 'modal-draggable': isDraggable }"
         x-ref="modalDialog">
        
        <div class="modal-content border-0 shadow-lg" 
             :class="{ 'modal-loading': isLoading }"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 transform scale-95"
             x-transition:enter-end="opacity-100 transform scale-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 transform scale-100"
             x-transition:leave-end="opacity-0 transform scale-95">
            
            <!-- Loading Overlay -->
            <div class="modal-loading-overlay" x-show="isLoading" x-transition>
                <div class="d-flex align-items-center justify-content-center h-100">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">جاري التحميل...</span>
                    </div>
                </div>
            </div>
            
            <!-- Header -->
            <div class="modal-header {{ $headerClass }} border-0" 
                 @if($draggable) x-ref="modalHeader" style="cursor: move;" @endif>
                
                <div class="d-flex align-items-center w-100">
                    <h5 class="modal-title flex-grow-1" id="{{ $id }}Label">
                        <i class="fas fa-window-maximize me-2"></i>
                        {{ $title }}
                    </h5>
                    
                    <!-- Step Indicator -->
                    @if($steps)
                    <div class="modal-steps me-3">
                        <span class="badge bg-light text-dark">
                            الخطوة <span x-text="currentStep"></span> من <span x-text="totalSteps"></span>
                        </span>
                    </div>
                    @endif
                    
                    <!-- Header Actions -->
                    <div class="modal-header-actions">
                        @if($size !== 'modal-fullscreen')
                        <button type="button" 
                                class="btn btn-sm btn-outline-light me-2" 
                                x-on:click="toggleFullscreen()"
                                title="ملء الشاشة">
                            <i class="fas fa-expand" x-show="!isFullscreen"></i>
                            <i class="fas fa-compress" x-show="isFullscreen"></i>
                        </button>
                        @endif
                        
                        <button type="button" 
                                class="btn-close btn-close-white" 
                                data-bs-dismiss="modal" 
                                aria-label="إغلاق"
                                x-on:click="handleClose()"></button>
                    </div>
                </div>
            </div>
            
            <!-- Body -->
            <div class="modal-body position-relative">
                @if($steps)
                    <!-- Multi-Step Content -->
                    <div class="steps-container">
                        <!-- Step Progress -->
                        <div class="step-progress mb-4">
                            <div class="progress">
                                <div class="progress-bar bg-primary" 
                                     :style="`width: ${(currentStep / totalSteps) * 100}%`"></div>
                            </div>
                            <div class="step-labels mt-2">
                                @foreach($steps as $index => $step)
                                <span class="step-label" 
                                      :class="{ 
                                          'active': currentStep === {{ $index + 1 }}, 
                                          'completed': currentStep > {{ $index + 1 }} 
                                      }">
                                    {{ $step['title'] }}
                                </span>
                                @endforeach
                            </div>
                        </div>
                        
                        <!-- Step Content -->
                        @foreach($steps as $index => $step)
                        <div class="step-content" 
                             x-show="currentStep === {{ $index + 1 }}" 
                             x-transition>
                            <h6 class="step-title mb-3">{{ $step['title'] }}</h6>
                            @if(isset($step['description']))
                            <p class="text-muted mb-4">{{ $step['description'] }}</p>
                            @endif
                            
                            {{ ${'step' . ($index + 1)} ?? '' }}
                        </div>
                        @endforeach
                    </div>
                @else
                    <!-- Single Content -->
                    {{ $slot }}
                @endif
            </div>
            
            <!-- Footer -->
            <div class="modal-footer {{ $footerClass }} border-0">
                <div class="d-flex justify-content-between w-100">
                    <div class="modal-footer-left">
                        <!-- Custom footer left content -->
                        {{ $footerLeft ?? '' }}
                    </div>
                    
                    <div class="modal-footer-right">
                        @if($steps)
                            <!-- Multi-Step Navigation -->
                            <button type="button" 
                                    class="btn btn-outline-secondary me-2" 
                                    x-show="currentStep > 1"
                                    x-on:click="previousStep()">
                                <i class="fas fa-arrow-right me-1"></i>
                                السابق
                            </button>
                            
                            <button type="button" 
                                    class="btn btn-primary" 
                                    x-show="currentStep < totalSteps"
                                    x-on:click="nextStep()">
                                التالي
                                <i class="fas fa-arrow-left ms-1"></i>
                            </button>
                            
                            @if($confirmButton)
                            <button type="button" 
                                    class="btn {{ $confirmClass }}" 
                                    x-show="currentStep === totalSteps"
                                    x-on:click="handleConfirm()"
                                    :disabled="isLoading">
                                <i class="fas fa-check me-1" x-show="!isLoading"></i>
                                <i class="fas fa-spinner fa-spin me-1" x-show="isLoading"></i>
                                {{ $confirmText }}
                            </button>
                            @endif
                        @else
                            <!-- Single Step Buttons -->
                            @if($cancelButton)
                            <button type="button" 
                                    class="btn {{ $cancelClass }} me-2" 
                                    data-bs-dismiss="modal"
                                    x-on:click="handleCancel()">
                                {{ $cancelText }}
                            </button>
                            @endif
                            
                            @if($confirmButton)
                            <button type="button" 
                                    class="btn {{ $confirmClass }}" 
                                    x-on:click="handleConfirm()"
                                    :disabled="isLoading">
                                <i class="fas fa-check me-1" x-show="!isLoading"></i>
                                <i class="fas fa-spinner fa-spin me-1" x-show="isLoading"></i>
                                {{ $confirmText }}
                            </button>
                            @endif
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function advancedModal() {
    return {
        currentStep: 1,
        totalSteps: {{ $steps ? count($steps) : 1 }},
        isLoading: {{ $loading ? 'true' : 'false' }},
        isFullscreen: false,
        isDraggable: {{ $draggable ? 'true' : 'false' }},
        dragOffset: { x: 0, y: 0 },
        modalInstance: null,
        
        init() {
            this.modalInstance = new bootstrap.Modal(this.$el);
            
            @if($draggable)
            this.initDraggable();
            @endif
            
            // Listen for modal events
            this.$el.addEventListener('shown.bs.modal', () => {
                this.onModalShown();
            });
            
            this.$el.addEventListener('hidden.bs.modal', () => {
                this.onModalHidden();
            });
        },
        
        show() {
            this.modalInstance.show();
        },
        
        hide() {
            this.modalInstance.hide();
        },
        
        nextStep() {
            if (this.validateCurrentStep()) {
                if (this.currentStep < this.totalSteps) {
                    this.currentStep++;
                }
            }
        },
        
        previousStep() {
            if (this.currentStep > 1) {
                this.currentStep--;
            }
        },
        
        validateCurrentStep() {
            // Override this method for custom validation
            return true;
        },
        
        toggleFullscreen() {
            this.isFullscreen = !this.isFullscreen;
            
            if (this.isFullscreen) {
                this.$refs.modalDialog.classList.add('modal-fullscreen');
            } else {
                this.$refs.modalDialog.classList.remove('modal-fullscreen');
            }
        },
        
        handleConfirm() {
            @if($onConfirm)
            const result = {{ $onConfirm }}();
            if (result === false) return;
            @endif
            
            this.isLoading = true;
            
            // Simulate async operation
            setTimeout(() => {
                this.isLoading = false;
                this.hide();
            }, 1000);
        },
        
        handleCancel() {
            @if($onCancel)
            {{ $onCancel }}();
            @endif
        },
        
        handleClose() {
            if (this.hasUnsavedChanges()) {
                if (confirm('لديك تغييرات غير محفوظة. هل أنت متأكد من الإغلاق؟')) {
                    this.hide();
                }
            } else {
                this.hide();
            }
        },
        
        hasUnsavedChanges() {
            // Override this method to check for unsaved changes
            return false;
        },
        
        initDraggable() {
            let isDragging = false;
            const header = this.$refs.modalHeader;
            const dialog = this.$refs.modalDialog;
            
            header.addEventListener('mousedown', (e) => {
                isDragging = true;
                this.dragOffset.x = e.clientX - dialog.offsetLeft;
                this.dragOffset.y = e.clientY - dialog.offsetTop;
                
                document.addEventListener('mousemove', this.handleDrag);
                document.addEventListener('mouseup', this.stopDrag);
            });
        },
        
        handleDrag(e) {
            if (!isDragging) return;
            
            const dialog = this.$refs.modalDialog;
            const newX = e.clientX - this.dragOffset.x;
            const newY = e.clientY - this.dragOffset.y;
            
            dialog.style.transform = `translate(${newX}px, ${newY}px)`;
        },
        
        stopDrag() {
            isDragging = false;
            document.removeEventListener('mousemove', this.handleDrag);
            document.removeEventListener('mouseup', this.stopDrag);
        },
        
        onModalShown() {
            // Focus first input
            const firstInput = this.$el.querySelector('input, textarea, select');
            if (firstInput) {
                firstInput.focus();
            }
        },
        
        onModalHidden() {
            // Reset state
            this.currentStep = 1;
            this.isLoading = false;
            this.isFullscreen = false;
        },
        
        // Public API methods
        setLoading(loading) {
            this.isLoading = loading;
        },
        
        goToStep(step) {
            if (step >= 1 && step <= this.totalSteps) {
                this.currentStep = step;
            }
        },
        
        updateTitle(title) {
            this.$el.querySelector('.modal-title').textContent = title;
        }
    }
}

// Global Modal API
window.AdvancedModal = {
    show(id) {
        const modal = document.getElementById(id);
        if (modal) {
            const alpineData = Alpine.$data(modal);
            alpineData.show();
        }
    },
    
    hide(id) {
        const modal = document.getElementById(id);
        if (modal) {
            const alpineData = Alpine.$data(modal);
            alpineData.hide();
        }
    },
    
    setLoading(id, loading) {
        const modal = document.getElementById(id);
        if (modal) {
            const alpineData = Alpine.$data(modal);
            alpineData.setLoading(loading);
        }
    }
};
</script>

<style>
.modal-content {
    border-radius: 15px;
    overflow: hidden;
}

.modal-header {
    border-radius: 15px 15px 0 0;
}

.modal-loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    z-index: 1050;
    border-radius: 15px;
}

.modal-loading .modal-body {
    opacity: 0.5;
    pointer-events: none;
}

.modal-draggable {
    position: absolute;
    margin: 0;
}

.modal-header-actions {
    display: flex;
    align-items: center;
}

.step-progress .progress {
    height: 8px;
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.1);
}

.step-labels {
    display: flex;
    justify-content: space-between;
}

.step-label {
    font-size: 0.75rem;
    color: #6c757d;
    transition: color 0.3s ease;
}

.step-label.active {
    color: #0d6efd;
    font-weight: 600;
}

.step-label.completed {
    color: #198754;
}

.step-content {
    min-height: 200px;
}

/* Animation Classes */
.modal.slide .modal-dialog {
    transform: translateY(-100%);
    transition: transform 0.3s ease-out;
}

.modal.slide.show .modal-dialog {
    transform: translateY(0);
}

.modal.zoom .modal-dialog {
    transform: scale(0.1);
    transition: transform 0.3s ease-out;
}

.modal.zoom.show .modal-dialog {
    transform: scale(1);
}

/* Responsive */
@media (max-width: 576px) {
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .modal-header-actions .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
    
    .step-labels {
        flex-direction: column;
        gap: 0.25rem;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .modal-content {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .modal-loading-overlay {
        background: rgba(45, 55, 72, 0.9);
    }
    
    .step-progress .progress {
        background: rgba(255, 255, 255, 0.1);
    }
}
</style>
