<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Validator;

class FirebaseAuthController extends Controller
{
    /**
     * Handle Firebase authentication callback
     */
    public function handleFirebaseCallback(Request $request)
    {
        // Validate the incoming data
        $validator = Validator::make($request->all(), [
            'uid' => 'required|string',
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'avatar' => 'nullable|url',
            'provider' => 'required|string'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'بيانات غير صحيحة',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $userData = $request->all();
            
            // Check if user already exists by email
            $user = User::where('email', $userData['email'])->first();
            
            if ($user) {
                // Update existing user with Firebase data if not already set
                if (!$user->firebase_uid) {
                    $user->update([
                        'firebase_uid' => $userData['uid'],
                        'avatar' => $userData['avatar'] ?? $user->avatar,
                    ]);
                }
                
                Auth::login($user);
                
                return response()->json([
                    'success' => true,
                    'message' => 'تم تسجيل الدخول بنجاح',
                    'redirect' => '/user/dashboard'
                ]);
            } else {
                // Create new user
                $user = User::create([
                    'name' => $userData['name'],
                    'email' => $userData['email'],
                    'email_verified_at' => now(),
                    'password' => Hash::make(Str::random(24)), // Random password
                    'firebase_uid' => $userData['uid'],
                    'avatar' => $userData['avatar'],
                    'provider' => $userData['provider']
                ]);
                
                Auth::login($user);
                
                return response()->json([
                    'success' => true,
                    'message' => 'تم إنشاء حسابك بنجاح',
                    'redirect' => '/user/dashboard'
                ]);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تسجيل الدخول. يرجى المحاولة مرة أخرى.',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
