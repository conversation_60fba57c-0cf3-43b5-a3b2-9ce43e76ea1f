@extends('backend.layouts.app')

@section('title', 'إدارة الملفات')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-folder me-2 text-primary"></i>
                        إدارة الملفات
                    </h1>
                    <p class="text-muted mb-0">تصفح وإدارة ملفات النظام</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="uploadFiles()">
                        <i class="fas fa-upload me-2"></i>
                        رفع ملفات
                    </button>
                    <button class="btn btn-outline-primary" onclick="createFolder()">
                        <i class="fas fa-folder-plus me-2"></i>
                        مجلد جديد
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Storage Info -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">المساحة المستخدمة</h6>
                            <h3 class="mb-0">2.4 GB</h3>
                        </div>
                        <div class="storage-icon">
                            <i class="fas fa-hdd fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">المساحة المتاحة</h6>
                            <h3 class="mb-0">7.6 GB</h3>
                        </div>
                        <div class="storage-icon">
                            <i class="fas fa-database fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">إجمالي الملفات</h6>
                            <h3 class="mb-0">1,247</h3>
                        </div>
                        <div class="storage-icon">
                            <i class="fas fa-file fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">المجلدات</h6>
                            <h3 class="mb-0">89</h3>
                        </div>
                        <div class="storage-icon">
                            <i class="fas fa-folder fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- File Manager -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <!-- Breadcrumb -->
                            <nav aria-label="breadcrumb">
                                <ol class="breadcrumb mb-0" id="fileBreadcrumb">
                                    <li class="breadcrumb-item">
                                        <a href="#" onclick="navigateToPath('/')">
                                            <i class="fas fa-home"></i>
                                            الجذر
                                        </a>
                                    </li>
                                    <li class="breadcrumb-item active">المجلد الحالي</li>
                                </ol>
                            </nav>
                        </div>
                        <div class="col-md-6">
                            <div class="d-flex justify-content-end gap-2">
                                <!-- View Toggle -->
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-secondary active" onclick="setView('grid')" title="عرض شبكي">
                                        <i class="fas fa-th"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="setView('list')" title="عرض قائمة">
                                        <i class="fas fa-list"></i>
                                    </button>
                                </div>
                                
                                <!-- Sort Options -->
                                <select class="form-select form-select-sm" style="width: auto;" onchange="sortFiles(this.value)">
                                    <option value="name">ترتيب بالاسم</option>
                                    <option value="date">ترتيب بالتاريخ</option>
                                    <option value="size">ترتيب بالحجم</option>
                                    <option value="type">ترتيب بالنوع</option>
                                </select>
                                
                                <!-- Search -->
                                <div class="input-group input-group-sm" style="width: 200px;">
                                    <input type="text" class="form-control" placeholder="البحث..." id="fileSearch">
                                    <button class="btn btn-outline-secondary" type="button">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <!-- File Grid View -->
                    <div id="fileGridView" class="file-grid p-3">
                        <div class="row g-3">
                            <!-- Folder Item -->
                            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                <div class="file-item folder-item" onclick="openFolder('documents')">
                                    <div class="file-icon">
                                        <i class="fas fa-folder text-warning"></i>
                                    </div>
                                    <div class="file-name">المستندات</div>
                                    <div class="file-info">15 ملف</div>
                                </div>
                            </div>
                            
                            <!-- Image File -->
                            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                <div class="file-item image-item" onclick="previewFile('image1.jpg')">
                                    <div class="file-icon">
                                        <img src="/images/sample.jpg" alt="صورة" class="file-thumbnail">
                                    </div>
                                    <div class="file-name">صورة1.jpg</div>
                                    <div class="file-info">2.4 MB</div>
                                </div>
                            </div>
                            
                            <!-- PDF File -->
                            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                <div class="file-item pdf-item" onclick="previewFile('document.pdf')">
                                    <div class="file-icon">
                                        <i class="fas fa-file-pdf text-danger"></i>
                                    </div>
                                    <div class="file-name">مستند.pdf</div>
                                    <div class="file-info">1.2 MB</div>
                                </div>
                            </div>
                            
                            <!-- Word File -->
                            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                <div class="file-item word-item" onclick="previewFile('report.docx')">
                                    <div class="file-icon">
                                        <i class="fas fa-file-word text-primary"></i>
                                    </div>
                                    <div class="file-name">تقرير.docx</div>
                                    <div class="file-info">856 KB</div>
                                </div>
                            </div>
                            
                            <!-- Excel File -->
                            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                <div class="file-item excel-item" onclick="previewFile('data.xlsx')">
                                    <div class="file-icon">
                                        <i class="fas fa-file-excel text-success"></i>
                                    </div>
                                    <div class="file-name">بيانات.xlsx</div>
                                    <div class="file-info">3.1 MB</div>
                                </div>
                            </div>
                            
                            <!-- Video File -->
                            <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                                <div class="file-item video-item" onclick="previewFile('video.mp4')">
                                    <div class="file-icon">
                                        <i class="fas fa-file-video text-info"></i>
                                    </div>
                                    <div class="file-name">فيديو.mp4</div>
                                    <div class="file-info">45.2 MB</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- File List View (Hidden by default) -->
                    <div id="fileListView" class="file-list d-none">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="50">
                                            <input type="checkbox" class="form-check-input" onchange="toggleSelectAll(this)">
                                        </th>
                                        <th>الاسم</th>
                                        <th>النوع</th>
                                        <th>الحجم</th>
                                        <th>تاريخ التعديل</th>
                                        <th width="120">الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input file-checkbox" value="documents">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-folder text-warning me-2"></i>
                                                <span>المستندات</span>
                                            </div>
                                        </td>
                                        <td>مجلد</td>
                                        <td>15 ملف</td>
                                        <td>منذ يومين</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-primary" onclick="openFolder('documents')" title="فتح">
                                                    <i class="fas fa-folder-open"></i>
                                                </button>
                                                <button class="btn btn-outline-warning" onclick="renameItem('documents')" title="إعادة تسمية">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteItem('documents')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>
                                            <input type="checkbox" class="form-check-input file-checkbox" value="image1.jpg">
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-file-image text-info me-2"></i>
                                                <span>صورة1.jpg</span>
                                            </div>
                                        </td>
                                        <td>صورة</td>
                                        <td>2.4 MB</td>
                                        <td>منذ ساعة</td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-info" onclick="previewFile('image1.jpg')" title="معاينة">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button class="btn btn-outline-success" onclick="downloadFile('image1.jpg')" title="تحميل">
                                                    <i class="fas fa-download"></i>
                                                </button>
                                                <button class="btn btn-outline-danger" onclick="deleteItem('image1.jpg')" title="حذف">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- File Actions Footer -->
                <div class="card-footer bg-light border-0" id="fileActions" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted" id="selectedCount">0 ملف محدد</span>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-success" onclick="downloadSelected()">
                                <i class="fas fa-download me-1"></i>
                                تحميل
                            </button>
                            <button class="btn btn-outline-warning" onclick="moveSelected()">
                                <i class="fas fa-cut me-1"></i>
                                نقل
                            </button>
                            <button class="btn btn-outline-info" onclick="copySelected()">
                                <i class="fas fa-copy me-1"></i>
                                نسخ
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteSelected()">
                                <i class="fas fa-trash me-1"></i>
                                حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- File Upload Modal -->
<x-backend.advanced-modal 
    id="uploadModal"
    title="رفع الملفات"
    size="modal-lg"
    :centered="true">
    
    <div class="upload-area" id="uploadArea">
        <div class="upload-icon">
            <i class="fas fa-cloud-upload-alt fa-3x text-primary"></i>
        </div>
        <h5>اسحب الملفات هنا أو انقر للاختيار</h5>
        <p class="text-muted">يمكنك رفع ملفات متعددة في نفس الوقت</p>
        <input type="file" id="fileInput" multiple style="display: none;">
        <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
            اختيار الملفات
        </button>
    </div>
    
    <div id="uploadProgress" style="display: none;">
        <div class="upload-files-list"></div>
    </div>
    
    <x-slot name="footerLeft">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
    </x-slot>
    
    <button type="button" class="btn btn-success" onclick="startUpload()" id="uploadBtn" style="display: none;">
        بدء الرفع
    </button>
</x-backend.advanced-modal>

<!-- File Preview Modal -->
<x-backend.advanced-modal 
    id="previewModal"
    title="معاينة الملف"
    size="modal-xl"
    :centered="true">
    
    <div id="previewContent" class="text-center">
        <!-- Preview content will be loaded here -->
    </div>
    
    <x-slot name="footerLeft">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
    </x-slot>
    
    <button type="button" class="btn btn-primary" onclick="downloadCurrentFile()">
        <i class="fas fa-download me-2"></i>
        تحميل الملف
    </button>
</x-backend.advanced-modal>
@endsection

@push('after-styles')
<style>
.storage-icon {
    opacity: 0.7;
}

.file-item {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.file-item:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.file-item.selected {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.1);
}

.file-icon {
    margin-bottom: 0.75rem;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.file-icon i {
    font-size: 2.5rem;
}

.file-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 8px;
}

.file-name {
    font-weight: 600;
    font-size: 0.875rem;
    color: #374151;
    margin-bottom: 0.25rem;
    word-break: break-word;
}

.file-info {
    font-size: 0.75rem;
    color: #6b7280;
}

.folder-item .file-icon i {
    color: #f59e0b !important;
}

.upload-area {
    border: 2px dashed #d1d5db;
    border-radius: 12px;
    padding: 3rem 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.upload-area:hover,
.upload-area.dragover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.upload-icon {
    margin-bottom: 1rem;
}

.upload-files-list {
    max-height: 300px;
    overflow-y: auto;
}

.upload-file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: #f8f9fa;
}

.upload-file-info {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.upload-file-progress {
    flex: 1;
    margin: 0 1rem;
}

.upload-file-status {
    font-size: 0.75rem;
    font-weight: 600;
}

.upload-file-status.success {
    color: #059669;
}

.upload-file-status.error {
    color: #dc2626;
}

.upload-file-status.uploading {
    color: #2563eb;
}

.breadcrumb {
    background: transparent;
    padding: 0;
}

.breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #4f46e5;
}

.breadcrumb-item.active {
    color: #6b7280;
}

.btn-group .btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.table th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    color: #6c757d;
}

.table td {
    border: none;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.file-checkbox:checked {
    background-color: #667eea;
    border-color: #667eea;
}

#fileActions {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Context Menu */
.context-menu {
    position: fixed;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    box-shadow: 0 10px 40px rgba(0,0,0,0.15);
    padding: 0.5rem 0;
    z-index: 9999;
    min-width: 150px;
}

.context-menu-item {
    padding: 0.5rem 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
    font-size: 0.875rem;
}

.context-menu-item:hover {
    background: #f8f9fa;
}

.context-menu-item i {
    width: 16px;
    margin-left: 0.5rem;
}

.context-menu-divider {
    height: 1px;
    background: #e9ecef;
    margin: 0.25rem 0;
}

/* Responsive */
@media (max-width: 768px) {
    .file-grid .col-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .file-item {
        padding: 0.75rem 0.5rem;
    }

    .file-icon {
        height: 40px;
        margin-bottom: 0.5rem;
    }

    .file-icon i {
        font-size: 1.5rem;
    }

    .file-thumbnail {
        width: 40px;
        height: 40px;
    }

    .file-name {
        font-size: 0.75rem;
    }

    .file-info {
        font-size: 0.625rem;
    }

    .upload-area {
        padding: 2rem 1rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.25rem;
    }
}

/* Loading Animation */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* File Type Colors */
.pdf-item .file-icon i { color: #dc3545 !important; }
.word-item .file-icon i { color: #2b579a !important; }
.excel-item .file-icon i { color: #217346 !important; }
.powerpoint-item .file-icon i { color: #d24726 !important; }
.video-item .file-icon i { color: #17a2b8 !important; }
.audio-item .file-icon i { color: #6f42c1 !important; }
.archive-item .file-icon i { color: #fd7e14 !important; }
.text-item .file-icon i { color: #6c757d !important; }
</style>
@endpush

@push('after-scripts')
<script>
let currentPath = '/';
let selectedFiles = [];
let currentView = 'grid';
let currentFile = null;

document.addEventListener('DOMContentLoaded', function() {
    initializeFileManager();
    setupEventListeners();
});

function initializeFileManager() {
    // Setup drag and drop for upload
    setupDragAndDrop();

    // Setup file input change
    document.getElementById('fileInput').addEventListener('change', handleFileSelect);

    // Setup search
    document.getElementById('fileSearch').addEventListener('input', handleSearch);

    // Setup context menu
    setupContextMenu();
}

function setupEventListeners() {
    // File selection
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('file-checkbox')) {
            updateSelectedFiles();
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'a':
                    e.preventDefault();
                    selectAllFiles();
                    break;
                case 'c':
                    if (selectedFiles.length > 0) {
                        e.preventDefault();
                        copySelected();
                    }
                    break;
                case 'x':
                    if (selectedFiles.length > 0) {
                        e.preventDefault();
                        moveSelected();
                    }
                    break;
                case 'Delete':
                    if (selectedFiles.length > 0) {
                        e.preventDefault();
                        deleteSelected();
                    }
                    break;
            }
        }
    });
}

function setupDragAndDrop() {
    const uploadArea = document.getElementById('uploadArea');

    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, preventDefaults, false);
    });

    ['dragenter', 'dragover'].forEach(eventName => {
        uploadArea.addEventListener(eventName, highlight, false);
    });

    ['dragleave', 'drop'].forEach(eventName => {
        uploadArea.addEventListener(eventName, unhighlight, false);
    });

    uploadArea.addEventListener('drop', handleDrop, false);

    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }

    function highlight() {
        uploadArea.classList.add('dragover');
    }

    function unhighlight() {
        uploadArea.classList.remove('dragover');
    }

    function handleDrop(e) {
        const files = e.dataTransfer.files;
        handleFiles(files);
    }
}

function handleFileSelect(e) {
    const files = e.target.files;
    handleFiles(files);
}

function handleFiles(files) {
    const fileList = Array.from(files);
    displaySelectedFiles(fileList);
    document.getElementById('uploadBtn').style.display = 'block';
}

function displaySelectedFiles(files) {
    const uploadProgress = document.getElementById('uploadProgress');
    const filesList = uploadProgress.querySelector('.upload-files-list');

    filesList.innerHTML = '';

    files.forEach((file, index) => {
        const fileItem = document.createElement('div');
        fileItem.className = 'upload-file-item';
        fileItem.innerHTML = `
            <div class="upload-file-info">
                <i class="fas fa-file text-muted"></i>
                <div>
                    <div class="fw-bold">${file.name}</div>
                    <small class="text-muted">${formatFileSize(file.size)}</small>
                </div>
            </div>
            <div class="upload-file-progress">
                <div class="progress" style="height: 4px;">
                    <div class="progress-bar" style="width: 0%"></div>
                </div>
            </div>
            <div class="upload-file-status">في الانتظار</div>
        `;
        filesList.appendChild(fileItem);
    });

    uploadProgress.style.display = 'block';
}

function startUpload() {
    const files = document.getElementById('fileInput').files;
    const fileItems = document.querySelectorAll('.upload-file-item');

    Array.from(files).forEach((file, index) => {
        uploadFile(file, fileItems[index]);
    });
}

function uploadFile(file, fileItem) {
    const progressBar = fileItem.querySelector('.progress-bar');
    const status = fileItem.querySelector('.upload-file-status');

    status.textContent = 'جاري الرفع...';
    status.className = 'upload-file-status uploading';

    // Simulate upload progress
    let progress = 0;
    const interval = setInterval(() => {
        progress += Math.random() * 15;
        if (progress > 100) progress = 100;

        progressBar.style.width = progress + '%';

        if (progress >= 100) {
            clearInterval(interval);
            status.textContent = 'تم الرفع';
            status.className = 'upload-file-status success';

            // Refresh file list
            setTimeout(() => {
                refreshFileList();
            }, 500);
        }
    }, 200);
}

function setView(viewType) {
    currentView = viewType;

    // Update button states
    document.querySelectorAll('[onclick*="setView"]').forEach(btn => {
        btn.classList.remove('active');
    });
    event.target.classList.add('active');

    // Toggle views
    const gridView = document.getElementById('fileGridView');
    const listView = document.getElementById('fileListView');

    if (viewType === 'grid') {
        gridView.classList.remove('d-none');
        listView.classList.add('d-none');
    } else {
        gridView.classList.add('d-none');
        listView.classList.remove('d-none');
    }
}

function sortFiles(sortBy) {
    // Implement file sorting logic
    console.log('Sorting by:', sortBy);

    // Show loading
    showLoading();

    // Simulate API call
    setTimeout(() => {
        hideLoading();
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.info(`تم ترتيب الملفات حسب ${getSortLabel(sortBy)}`);
        }
    }, 1000);
}

function getSortLabel(sortBy) {
    const labels = {
        'name': 'الاسم',
        'date': 'التاريخ',
        'size': 'الحجم',
        'type': 'النوع'
    };
    return labels[sortBy] || sortBy;
}

function handleSearch() {
    const searchTerm = document.getElementById('fileSearch').value.toLowerCase();
    const fileItems = document.querySelectorAll('.file-item');

    fileItems.forEach(item => {
        const fileName = item.querySelector('.file-name').textContent.toLowerCase();
        if (fileName.includes(searchTerm)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
}

function navigateToPath(path) {
    currentPath = path;
    updateBreadcrumb();
    refreshFileList();
}

function updateBreadcrumb() {
    const breadcrumb = document.getElementById('fileBreadcrumb');
    const pathParts = currentPath.split('/').filter(part => part);

    let breadcrumbHTML = `
        <li class="breadcrumb-item">
            <a href="#" onclick="navigateToPath('/')">
                <i class="fas fa-home"></i>
                الجذر
            </a>
        </li>
    `;

    let currentPathBuild = '';
    pathParts.forEach((part, index) => {
        currentPathBuild += '/' + part;
        if (index === pathParts.length - 1) {
            breadcrumbHTML += `<li class="breadcrumb-item active">${part}</li>`;
        } else {
            breadcrumbHTML += `
                <li class="breadcrumb-item">
                    <a href="#" onclick="navigateToPath('${currentPathBuild}')">${part}</a>
                </li>
            `;
        }
    });

    breadcrumb.innerHTML = breadcrumbHTML;
}

function openFolder(folderName) {
    navigateToPath(currentPath + '/' + folderName);
}

function previewFile(fileName) {
    currentFile = fileName;
    const modal = new bootstrap.Modal(document.getElementById('previewModal'));
    const previewContent = document.getElementById('previewContent');

    // Show loading
    previewContent.innerHTML = '<div class="loading-spinner"></div><p>جاري تحميل المعاينة...</p>';

    modal.show();

    // Simulate loading preview
    setTimeout(() => {
        const fileExt = fileName.split('.').pop().toLowerCase();
        let previewHTML = '';

        switch(fileExt) {
            case 'jpg':
            case 'jpeg':
            case 'png':
            case 'gif':
                previewHTML = `<img src="/images/sample.jpg" class="img-fluid" alt="${fileName}">`;
                break;
            case 'pdf':
                previewHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-file-pdf fa-2x mb-2"></i>
                        <h5>ملف PDF</h5>
                        <p>اسم الملف: ${fileName}</p>
                        <button class="btn btn-primary" onclick="downloadCurrentFile()">
                            <i class="fas fa-download me-2"></i>
                            تحميل للعرض
                        </button>
                    </div>
                `;
                break;
            case 'mp4':
            case 'avi':
            case 'mov':
                previewHTML = `
                    <video controls class="w-100" style="max-height: 400px;">
                        <source src="/videos/sample.mp4" type="video/mp4">
                        متصفحك لا يدعم عرض الفيديو.
                    </video>
                `;
                break;
            default:
                previewHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-file fa-2x mb-2"></i>
                        <h5>لا يمكن معاينة هذا النوع من الملفات</h5>
                        <p>اسم الملف: ${fileName}</p>
                        <button class="btn btn-primary" onclick="downloadCurrentFile()">
                            <i class="fas fa-download me-2"></i>
                            تحميل الملف
                        </button>
                    </div>
                `;
        }

        previewContent.innerHTML = previewHTML;
    }, 1000);
}

function downloadFile(fileName) {
    // Simulate file download
    const link = document.createElement('a');
    link.href = `/admin/files/download/${fileName}`;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    if (typeof NotificationSystem !== 'undefined') {
        NotificationSystem.success(`تم بدء تحميل ${fileName}`);
    }
}

function downloadCurrentFile() {
    if (currentFile) {
        downloadFile(currentFile);
    }
}

function deleteItem(itemName) {
    if (confirm(`هل أنت متأكد من حذف "${itemName}"؟`)) {
        showLoading();

        // Simulate deletion
        setTimeout(() => {
            hideLoading();
            refreshFileList();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success(`تم حذف "${itemName}" بنجاح`);
            }
        }, 1000);
    }
}

function renameItem(itemName) {
    const newName = prompt('أدخل الاسم الجديد:', itemName);
    if (newName && newName !== itemName) {
        showLoading();

        // Simulate rename
        setTimeout(() => {
            hideLoading();
            refreshFileList();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success(`تم تغيير اسم "${itemName}" إلى "${newName}"`);
            }
        }, 1000);
    }
}

function toggleSelectAll(checkbox) {
    const fileCheckboxes = document.querySelectorAll('.file-checkbox');
    fileCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateSelectedFiles();
}

function updateSelectedFiles() {
    const checkedBoxes = document.querySelectorAll('.file-checkbox:checked');
    selectedFiles = Array.from(checkedBoxes).map(cb => cb.value);

    const selectedCount = document.getElementById('selectedCount');
    const fileActions = document.getElementById('fileActions');

    if (selectedFiles.length > 0) {
        selectedCount.textContent = `${selectedFiles.length} ملف محدد`;
        fileActions.style.display = 'block';
    } else {
        fileActions.style.display = 'none';
    }
}

function downloadSelected() {
    if (selectedFiles.length === 0) return;

    if (typeof NotificationSystem !== 'undefined') {
        NotificationSystem.info(`جاري تحميل ${selectedFiles.length} ملف...`);
    }

    // Simulate batch download
    selectedFiles.forEach((file, index) => {
        setTimeout(() => {
            downloadFile(file);
        }, index * 500);
    });
}

function deleteSelected() {
    if (selectedFiles.length === 0) return;

    if (confirm(`هل أنت متأكد من حذف ${selectedFiles.length} ملف؟`)) {
        showLoading();

        setTimeout(() => {
            hideLoading();
            refreshFileList();
            selectedFiles = [];
            updateSelectedFiles();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم حذف الملفات المحددة بنجاح');
            }
        }, 2000);
    }
}

function copySelected() {
    if (selectedFiles.length === 0) return;

    if (typeof NotificationSystem !== 'undefined') {
        NotificationSystem.info(`تم نسخ ${selectedFiles.length} ملف إلى الحافظة`);
    }
}

function moveSelected() {
    if (selectedFiles.length === 0) return;

    const destination = prompt('أدخل مسار الوجهة:');
    if (destination) {
        showLoading();

        setTimeout(() => {
            hideLoading();
            refreshFileList();
            selectedFiles = [];
            updateSelectedFiles();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success(`تم نقل الملفات إلى ${destination}`);
            }
        }, 2000);
    }
}

function createFolder() {
    const folderName = prompt('أدخل اسم المجلد الجديد:');
    if (folderName) {
        showLoading();

        setTimeout(() => {
            hideLoading();
            refreshFileList();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success(`تم إنشاء المجلد "${folderName}" بنجاح`);
            }
        }, 1000);
    }
}

function uploadFiles() {
    const modal = new bootstrap.Modal(document.getElementById('uploadModal'));
    modal.show();
}

function refreshFileList() {
    showLoading();

    // Simulate API call to refresh file list
    setTimeout(() => {
        hideLoading();
        // Update file list here
    }, 1000);
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';

    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function showLoading() {
    // Add loading overlay
    const overlay = document.createElement('div');
    overlay.id = 'loadingOverlay';
    overlay.innerHTML = `
        <div class="d-flex justify-content-center align-items-center h-100">
            <div class="loading-spinner"></div>
            <span class="ms-2">جاري التحميل...</span>
        </div>
    `;
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255,255,255,0.8);
        z-index: 9999;
    `;
    document.body.appendChild(overlay);
}

function hideLoading() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.remove();
    }
}

function setupContextMenu() {
    // Right-click context menu for files
    document.addEventListener('contextmenu', function(e) {
        const fileItem = e.target.closest('.file-item');
        if (fileItem) {
            e.preventDefault();
            showContextMenu(e.pageX, e.pageY, fileItem);
        }
    });

    // Hide context menu on click
    document.addEventListener('click', function() {
        hideContextMenu();
    });
}

function showContextMenu(x, y, fileItem) {
    hideContextMenu();

    const contextMenu = document.createElement('div');
    contextMenu.className = 'context-menu';
    contextMenu.innerHTML = `
        <div class="context-menu-item" onclick="previewFile('${fileItem.dataset.filename}')">
            <i class="fas fa-eye"></i>
            معاينة
        </div>
        <div class="context-menu-item" onclick="downloadFile('${fileItem.dataset.filename}')">
            <i class="fas fa-download"></i>
            تحميل
        </div>
        <div class="context-menu-divider"></div>
        <div class="context-menu-item" onclick="renameItem('${fileItem.dataset.filename}')">
            <i class="fas fa-edit"></i>
            إعادة تسمية
        </div>
        <div class="context-menu-item" onclick="copySelected()">
            <i class="fas fa-copy"></i>
            نسخ
        </div>
        <div class="context-menu-item" onclick="moveSelected()">
            <i class="fas fa-cut"></i>
            قص
        </div>
        <div class="context-menu-divider"></div>
        <div class="context-menu-item" onclick="deleteItem('${fileItem.dataset.filename}')">
            <i class="fas fa-trash"></i>
            حذف
        </div>
    `;

    contextMenu.style.left = x + 'px';
    contextMenu.style.top = y + 'px';

    document.body.appendChild(contextMenu);
}

function hideContextMenu() {
    const contextMenu = document.querySelector('.context-menu');
    if (contextMenu) {
        contextMenu.remove();
    }
}
</script>
@endpush
