<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Models\ContactReply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    /**
     * Display a listing of contacts.
     */
    public function index()
    {
        $contacts = Contact::latest()->paginate(15);
        
        $stats = [
            'total_messages' => Contact::count(),
            'unread_messages' => Contact::where('is_read', false)->count(),
            'read_messages' => Contact::where('is_read', true)->count(),
            'today_messages' => Contact::whereDate('created_at', today())->count(),
        ];

        return view('admin.contacts.index', compact('contacts', 'stats'));
    }

    /**
     * Display the specified contact.
     */
    public function show(Contact $contact)
    {
        // Mark as read
        if (!$contact->is_read) {
            $contact->update(['is_read' => true]);
        }

        $replies = ContactReply::where('contact_id', $contact->id)
            ->orderBy('created_at', 'desc')
            ->get();

        return view('admin.contacts.show', compact('contact', 'replies'));
    }

    /**
     * Store a reply to the contact.
     */
    public function reply(Request $request, Contact $contact)
    {
        $request->validate([
            'message' => 'required|string',
            'send_email' => 'boolean'
        ]);

        $reply = ContactReply::create([
            'contact_id' => $contact->id,
            'admin_id' => auth('admin')->id(),
            'message' => $request->message,
            'sent_via_email' => $request->boolean('send_email')
        ]);

        // Send email if requested
        if ($request->boolean('send_email')) {
            try {
                // Here you would implement email sending
                // Mail::to($contact->email)->send(new ContactReplyMail($reply));
                
                $reply->update(['email_sent_at' => now()]);
                
                return redirect()->back()
                    ->with('success', 'تم إرسال الرد بنجاح عبر البريد الإلكتروني');
            } catch (\Exception $e) {
                return redirect()->back()
                    ->with('warning', 'تم حفظ الرد ولكن فشل في إرسال البريد الإلكتروني');
            }
        }

        return redirect()->back()
            ->with('success', 'تم حفظ الرد بنجاح');
    }

    /**
     * Mark contact as read/unread.
     */
    public function toggleRead(Contact $contact)
    {
        $contact->update([
            'is_read' => !$contact->is_read
        ]);

        return response()->json([
            'success' => true,
            'message' => $contact->is_read ? 'تم وضع علامة مقروء' : 'تم وضع علامة غير مقروء',
            'is_read' => $contact->is_read
        ]);
    }

    /**
     * Delete the specified contact.
     */
    public function destroy(Contact $contact)
    {
        // Delete associated replies
        ContactReply::where('contact_id', $contact->id)->delete();
        
        $contact->delete();

        return redirect()->route('admin.contacts.index')
            ->with('success', 'تم حذف الرسالة بنجاح');
    }

    /**
     * Mark multiple contacts as read.
     */
    public function markAsRead(Request $request)
    {
        $request->validate([
            'contact_ids' => 'required|array',
            'contact_ids.*' => 'exists:contacts,id'
        ]);

        Contact::whereIn('id', $request->contact_ids)
            ->update(['is_read' => true]);

        return response()->json([
            'success' => true,
            'message' => 'تم وضع علامة مقروء على الرسائل المحددة'
        ]);
    }

    /**
     * Delete multiple contacts.
     */
    public function bulkDelete(Request $request)
    {
        $request->validate([
            'contact_ids' => 'required|array',
            'contact_ids.*' => 'exists:contacts,id'
        ]);

        // Delete associated replies
        ContactReply::whereIn('contact_id', $request->contact_ids)->delete();
        
        // Delete contacts
        Contact::whereIn('id', $request->contact_ids)->delete();

        return response()->json([
            'success' => true,
            'message' => 'تم حذف الرسائل المحددة بنجاح'
        ]);
    }

    /**
     * Export contacts to CSV.
     */
    public function export()
    {
        $contacts = Contact::all();
        
        $filename = 'contacts_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => 'attachment; filename="' . $filename . '"',
        ];

        $callback = function() use ($contacts) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fprintf($file, chr(0xEF).chr(0xBB).chr(0xBF));
            
            // Add headers
            fputcsv($file, ['الاسم', 'البريد الإلكتروني', 'الموضوع', 'الرسالة', 'مقروءة', 'تاريخ الإرسال']);
            
            foreach ($contacts as $contact) {
                fputcsv($file, [
                    $contact->name,
                    $contact->email,
                    $contact->subject,
                    $contact->message,
                    $contact->is_read ? 'نعم' : 'لا',
                    $contact->created_at->format('Y-m-d H:i:s')
                ]);
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get contact statistics for dashboard.
     */
    public function getStats()
    {
        $stats = [
            'total_messages' => Contact::count(),
            'unread_messages' => Contact::where('is_read', false)->count(),
            'today_messages' => Contact::whereDate('created_at', today())->count(),
            'this_week_messages' => Contact::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek()
            ])->count(),
            'this_month_messages' => Contact::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
        ];

        return response()->json($stats);
    }
}
