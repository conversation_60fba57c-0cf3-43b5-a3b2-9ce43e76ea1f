<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class UltraFastBlogSeeder extends Seeder
{
    public function run(): void
    {
        $this->command->info('🚀 بدء إنشاء مليون مقال بسرعة فائقة...');
        
        try {
            // تحسينات قاعدة البيانات للسرعة القصوى
            DB::statement('SET SESSION sql_mode = ""');
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
            DB::statement('SET UNIQUE_CHECKS=0');
            DB::statement('SET AUTOCOMMIT=0');
            
            // مسح البيانات الموجودة
            DB::statement('TRUNCATE TABLE blog_posts');
            
            $this->command->info('✅ تم تحضير قاعدة البيانات للسرعة القصوى');
            
            $totalPosts = 1000000;
            $batchSize = 50000; // دفعات كبيرة جداً
            $batches = ceil($totalPosts / $batchSize);
            
            // بيانات ثابتة للسرعة
            $categories = ['البرمجة', 'التصميم', 'التقنية', 'الأمان', 'تحليلات'];
            $authors = ['أحمد محمد', 'سارة أحمد', 'محمد عبدالله', 'فاطمة سالم', 'خالد عمر'];
            $techs = ['React', 'Laravel', 'Python', 'JavaScript', 'PHP'];
            $images = [
                'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=1200',
                'https://images.unsplash.com/photo-1555066931-4365d14bab8c?w=1200',
                'https://images.unsplash.com/photo-1627398242454-45a1465c2479?w=1200'
            ];
            
            for ($batch = 0; $batch < $batches; $batch++) {
                $startTime = microtime(true);
                
                $currentBatchSize = min($batchSize, $totalPosts - ($batch * $batchSize));
                $values = [];
                
                // إنشاء SQL مباشر للسرعة القصوى
                for ($i = 0; $i < $currentBatchSize; $i++) {
                    $postId = ($batch * $batchSize) + $i + 1;
                    $category = $categories[$postId % 5];
                    $author = $authors[$postId % 5];
                    $tech = $techs[$postId % 5];
                    $image = $images[$postId % 3];
                    
                    $title = "دليل {$tech} الشامل - المقال {$postId}";
                    $slug = "guide-{$tech}-{$postId}";
                    $excerpt = "تعلم {$tech} في {$category} بطريقة عملية ومبسطة.";
                    $content = "<h2>مقدمة</h2><p>دليل شامل لتعلم {$tech} في مجال {$category}.</p><h2>الأساسيات</h2><p>أساسيات {$tech} المهمة.</p><h2>التطبيق</h2><p>تطبيق عملي لـ {$tech}.</p><h2>الخلاصة</h2><p>خلاصة تعلم {$tech}.</p>";
                    $readTime = (($postId % 20) + 3) . ' دقيقة';
                    $views = $postId % 10000;
                    $isFeatured = ($postId % 100 == 0) ? 1 : 0;
                    $tags = json_encode(['تقنية', 'برمجة', $category, $tech]);
                    $metaData = json_encode(['difficulty' => ['مبتدئ', 'متوسط', 'متقدم'][$postId % 3]]);
                    $createdAt = date('Y-m-d H:i:s', strtotime('-' . ($postId % 365) . ' days'));
                    $updatedAt = date('Y-m-d H:i:s');
                    
                    $values[] = "('{$title}', '{$slug}', '{$excerpt}', '{$content}', '{$image}', '{$category}', '{$author}', '{$readTime}', {$isFeatured}, 1, {$views}, '{$tags}', '{$metaData}', '{$createdAt}', '{$updatedAt}')";
                }
                
                // تنفيذ SQL مباشر للسرعة القصوى
                $sql = "INSERT INTO blog_posts (title, slug, excerpt, content, image, category, author, read_time, is_featured, is_published, views, tags, meta_data, created_at, updated_at) VALUES " . implode(',', $values);
                
                DB::statement($sql);
                DB::statement('COMMIT');
                
                $completed = min(($batch + 1) * $batchSize, $totalPosts);
                $percentage = round(($completed / $totalPosts) * 100, 1);
                $elapsed = round(microtime(true) - $startTime, 2);
                
                $this->command->info("⚡ الدفعة " . ($batch + 1) . "/{$batches} - " . number_format($completed) . " مقال ({$percentage}%) - {$elapsed}s");
                
                // تنظيف الذاكرة
                unset($values);
                
                // استراحة قصيرة لتجنب إرهاق الخادم
                if ($batch % 5 == 0 && $batch > 0) {
                    usleep(100000); // 0.1 ثانية
                }
            }
            
            // إعادة تفعيل القيود
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
            DB::statement('SET UNIQUE_CHECKS=1');
            DB::statement('SET AUTOCOMMIT=1');
            
            $this->command->info('🎉 تم إنشاء مليون مقال بنجاح!');
            $this->command->info('📊 إجمالي المقالات: ' . number_format($totalPosts));
            
            // التحقق من العدد النهائي
            $actualCount = DB::table('blog_posts')->count();
            $this->command->info("✅ تأكيد العدد في قاعدة البيانات: " . number_format($actualCount));
            
        } catch (\Exception $e) {
            $this->command->error('❌ خطأ: ' . $e->getMessage());
            
            // إعادة تفعيل القيود في حالة الخطأ
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
            DB::statement('SET UNIQUE_CHECKS=1');
            DB::statement('SET AUTOCOMMIT=1');
            
            throw $e;
        }
    }
}
