// Admin Dashboard JavaScript

class AdminDashboard {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeCharts();
        this.setupRealTimeUpdates();
        this.setupNotifications();
        this.setupSearch();
    }

    setupEventListeners() {
        // Balance card toggles
        document.querySelectorAll('.balance-toggle').forEach(toggle => {
            toggle.addEventListener('click', this.handleBalanceToggle.bind(this));
        });

        // Search functionality
        const searchInput = document.querySelector('.search-input');
        if (searchInput) {
            searchInput.addEventListener('input', this.handleSearch.bind(this));
        }

        // Notification buttons
        document.querySelectorAll('.notification-btn').forEach(btn => {
            btn.addEventListener('click', this.handleNotificationClick.bind(this));
        });

        // Filter button
        const filterBtn = document.querySelector('.filter-btn');
        if (filterBtn) {
            filterBtn.addEventListener('click', this.handleFilterClick.bind(this));
        }

        // Mobile sidebar toggle
        this.setupMobileSidebar();

        // Keyboard shortcuts
        document.addEventListener('keydown', this.handleKeyboardShortcuts.bind(this));
    }

    handleBalanceToggle(event) {
        const toggle = event.currentTarget;
        toggle.classList.toggle('active');
        
        // Add animation effect
        toggle.style.transform = 'scale(0.95)';
        setTimeout(() => {
            toggle.style.transform = 'scale(1)';
        }, 150);
    }

    handleSearch(event) {
        const query = event.target.value.toLowerCase();
        console.log('البحث عن:', query);
        
        // Implement search functionality here
        // You can search through dashboard data, navigation items, etc.
        this.performSearch(query);
    }

    performSearch(query) {
        // Example search implementation
        const searchableElements = document.querySelectorAll('[data-searchable]');
        
        searchableElements.forEach(element => {
            const text = element.textContent.toLowerCase();
            const isMatch = text.includes(query);
            
            if (query === '') {
                element.style.display = '';
            } else {
                element.style.display = isMatch ? '' : 'none';
            }
        });
    }

    handleNotificationClick(event) {
        const btn = event.currentTarget;
        const badge = btn.querySelector('.notification-badge');
        
        // Add click animation
        btn.style.transform = 'scale(0.95)';
        setTimeout(() => {
            btn.style.transform = 'scale(1)';
        }, 150);

        // Simulate notification handling
        if (badge) {
            const count = parseInt(badge.textContent);
            if (count > 0) {
                badge.textContent = Math.max(0, count - 1);
                if (count === 1) {
                    badge.style.display = 'none';
                }
            }
        }

        console.log('تم النقر على الإشعار');
    }

    handleFilterClick(event) {
        console.log('تم النقر على زر التصفية');
        
        // Add click animation
        const btn = event.currentTarget;
        btn.style.transform = 'translateY(-2px) scale(0.98)';
        setTimeout(() => {
            btn.style.transform = 'translateY(-2px) scale(1)';
        }, 150);

        // Show filter modal or dropdown
        this.showFilterOptions();
    }

    showFilterOptions() {
        // Create and show filter dropdown
        const existingDropdown = document.querySelector('.filter-dropdown');
        if (existingDropdown) {
            existingDropdown.remove();
            return;
        }

        const dropdown = document.createElement('div');
        dropdown.className = 'filter-dropdown';
        dropdown.style.cssText = `
            position: absolute;
            top: 100%;
            right: 0;
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            padding: 16px;
            min-width: 200px;
            z-index: 1000;
            margin-top: 8px;
        `;

        dropdown.innerHTML = `
            <div style="margin-bottom: 12px; font-weight: 600; color: #1a202c;">فترة التصفية</div>
            <div style="display: flex; flex-direction: column; gap: 8px;">
                <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                    <input type="radio" name="period" value="today" style="margin: 0;">
                    <span>اليوم</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                    <input type="radio" name="period" value="week" style="margin: 0;">
                    <span>هذا الأسبوع</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                    <input type="radio" name="period" value="month" checked style="margin: 0;">
                    <span>هذا الشهر</span>
                </label>
                <label style="display: flex; align-items: center; gap: 8px; cursor: pointer;">
                    <input type="radio" name="period" value="year" style="margin: 0;">
                    <span>هذا العام</span>
                </label>
            </div>
        `;

        const filterBtn = document.querySelector('.filter-btn');
        filterBtn.style.position = 'relative';
        filterBtn.appendChild(dropdown);

        // Close dropdown when clicking outside
        setTimeout(() => {
            document.addEventListener('click', function closeDropdown(e) {
                if (!filterBtn.contains(e.target)) {
                    dropdown.remove();
                    document.removeEventListener('click', closeDropdown);
                }
            });
        }, 100);
    }

    setupMobileSidebar() {
        // Create mobile toggle button
        const toggleBtn = document.createElement('button');
        toggleBtn.innerHTML = '<i class="fas fa-bars"></i>';
        toggleBtn.className = 'mobile-sidebar-toggle';
        toggleBtn.style.cssText = `
            display: none;
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            width: 44px;
            height: 44px;
            border-radius: 12px;
            cursor: pointer;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
        `;

        document.body.appendChild(toggleBtn);

        toggleBtn.addEventListener('click', () => {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        });

        // Show/hide toggle button based on screen size
        const checkMobile = () => {
            if (window.innerWidth <= 768) {
                toggleBtn.style.display = 'flex';
            } else {
                toggleBtn.style.display = 'none';
                document.querySelector('.sidebar').classList.remove('open');
            }
        };

        window.addEventListener('resize', checkMobile);
        checkMobile();
    }

    handleKeyboardShortcuts(event) {
        // Ctrl/Cmd + K for search
        if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
            event.preventDefault();
            const searchInput = document.querySelector('.search-input');
            if (searchInput) {
                searchInput.focus();
            }
        }

        // Escape to close modals/dropdowns
        if (event.key === 'Escape') {
            const dropdown = document.querySelector('.filter-dropdown');
            if (dropdown) {
                dropdown.remove();
            }
        }
    }

    initializeCharts() {
        // Charts are already initialized in the main HTML file
        // This method can be used for additional chart configurations
        this.setupChartInteractions();
    }

    setupChartInteractions() {
        // Add chart interaction handlers
        const chartContainers = document.querySelectorAll('.chart-container');
        
        chartContainers.forEach(container => {
            container.addEventListener('mouseenter', () => {
                container.style.transform = 'scale(1.02)';
            });
            
            container.addEventListener('mouseleave', () => {
                container.style.transform = 'scale(1)';
            });
        });
    }

    setupRealTimeUpdates() {
        // Simulate real-time updates every 30 seconds
        setInterval(() => {
            this.updateDashboardData();
        }, 30000);
    }

    updateDashboardData() {
        // Simulate data updates
        const statValues = document.querySelectorAll('.stat-value');
        
        statValues.forEach(element => {
            const currentValue = parseFloat(element.textContent.replace(/[^0-9.]/g, ''));
            const change = (Math.random() - 0.5) * 0.1; // ±5% change
            const newValue = currentValue * (1 + change);
            
            // Animate the change
            element.style.transform = 'scale(1.1)';
            setTimeout(() => {
                element.textContent = element.textContent.replace(currentValue.toString(), Math.round(newValue).toString());
                element.style.transform = 'scale(1)';
            }, 200);
        });
    }

    setupNotifications() {
        // Setup notification system
        this.notifications = [];
        this.createNotificationContainer();
    }

    createNotificationContainer() {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            display: flex;
            flex-direction: column;
            gap: 12px;
            pointer-events: none;
        `;
        document.body.appendChild(container);
    }

    showNotification(message, type = 'info', duration = 5000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.style.cssText = `
            background: white;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 16px 20px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
            max-width: 400px;
            pointer-events: auto;
            transform: translateX(-100%);
            transition: transform 0.3s ease;
        `;

        const colors = {
            info: '#3b82f6',
            success: '#10b981',
            warning: '#f59e0b',
            error: '#ef4444'
        };

        notification.style.borderLeftColor = colors[type];
        notification.style.borderLeftWidth = '4px';
        notification.textContent = message;

        const container = document.getElementById('notification-container');
        container.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(-100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);
    }

    setupSearch() {
        // Enhanced search functionality
        const searchInput = document.querySelector('.search-input');
        if (!searchInput) return;

        let searchTimeout;
        
        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                this.performAdvancedSearch(e.target.value);
            }, 300);
        });
    }

    performAdvancedSearch(query) {
        if (!query.trim()) {
            this.clearSearchResults();
            return;
        }

        // Simulate search API call
        console.log('البحث المتقدم عن:', query);
        
        // You can implement actual search functionality here
        // For example, search through users, orders, domains, etc.
    }

    clearSearchResults() {
        // Clear search highlighting and filters
        const highlightedElements = document.querySelectorAll('.search-highlight');
        highlightedElements.forEach(el => {
            el.classList.remove('search-highlight');
        });
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.adminDashboard = new AdminDashboard();
});

// Export for use in other scripts
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdminDashboard;
}
