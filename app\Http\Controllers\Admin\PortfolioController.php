<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Portfolio;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PortfolioController extends Controller
{
    /**
     * Display a listing of portfolios.
     */
    public function index()
    {
        $portfolios = Portfolio::latest()->paginate(15);
        
        $stats = [
            'total_portfolios' => Portfolio::count(),
            'published_portfolios' => Portfolio::where('status', 'published')->count(),
            'draft_portfolios' => Portfolio::where('status', 'draft')->count(),
            'featured_portfolios' => Portfolio::where('featured', true)->count(),
        ];

        return view('admin.portfolios.index', compact('portfolios', 'stats'));
    }

    /**
     * Show the form for creating a new portfolio.
     */
    public function create()
    {
        return view('admin.portfolios.create');
    }

    /**
     * Store a newly created portfolio in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:100',
            'technologies' => 'nullable|string',
            'client_name' => 'nullable|string|max:255',
            'project_url' => 'nullable|url',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:draft,published',
            'featured' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->title);
        $data['featured'] = $request->boolean('featured');

        // Handle main image upload
        if ($request->hasFile('image')) {
            $data['image'] = $request->file('image')->store('portfolios', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery')) {
            $galleryImages = [];
            foreach ($request->file('gallery') as $file) {
                $galleryImages[] = $file->store('portfolios/gallery', 'public');
            }
            $data['gallery'] = json_encode($galleryImages);
        }

        Portfolio::create($data);

        return redirect()->route('admin.portfolios.index')
            ->with('success', 'تم إضافة العمل بنجاح');
    }

    /**
     * Display the specified portfolio.
     */
    public function show(Portfolio $portfolio)
    {
        return view('admin.portfolios.show', compact('portfolio'));
    }

    /**
     * Show the form for editing the specified portfolio.
     */
    public function edit(Portfolio $portfolio)
    {
        return view('admin.portfolios.edit', compact('portfolio'));
    }

    /**
     * Update the specified portfolio in storage.
     */
    public function update(Request $request, Portfolio $portfolio)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'category' => 'nullable|string|max:100',
            'technologies' => 'nullable|string',
            'client_name' => 'nullable|string|max:255',
            'project_url' => 'nullable|url',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'status' => 'required|in:draft,published',
            'featured' => 'boolean',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery.*' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->all();
        $data['slug'] = Str::slug($request->title);
        $data['featured'] = $request->boolean('featured');

        // Handle main image upload
        if ($request->hasFile('image')) {
            // Delete old image
            if ($portfolio->image) {
                Storage::disk('public')->delete($portfolio->image);
            }
            $data['image'] = $request->file('image')->store('portfolios', 'public');
        }

        // Handle gallery images upload
        if ($request->hasFile('gallery')) {
            // Delete old gallery images
            if ($portfolio->gallery) {
                $oldGallery = json_decode($portfolio->gallery, true);
                foreach ($oldGallery as $oldImage) {
                    Storage::disk('public')->delete($oldImage);
                }
            }
            
            $galleryImages = [];
            foreach ($request->file('gallery') as $file) {
                $galleryImages[] = $file->store('portfolios/gallery', 'public');
            }
            $data['gallery'] = json_encode($galleryImages);
        }

        $portfolio->update($data);

        return redirect()->route('admin.portfolios.index')
            ->with('success', 'تم تحديث العمل بنجاح');
    }

    /**
     * Remove the specified portfolio from storage.
     */
    public function destroy(Portfolio $portfolio)
    {
        // Delete associated images
        if ($portfolio->image) {
            Storage::disk('public')->delete($portfolio->image);
        }

        if ($portfolio->gallery) {
            $galleryImages = json_decode($portfolio->gallery, true);
            foreach ($galleryImages as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $portfolio->delete();

        return redirect()->route('admin.portfolios.index')
            ->with('success', 'تم حذف العمل بنجاح');
    }

    /**
     * Toggle portfolio status.
     */
    public function toggleStatus(Portfolio $portfolio)
    {
        $portfolio->update([
            'status' => $portfolio->status === 'published' ? 'draft' : 'published'
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تغيير حالة العمل بنجاح',
            'status' => $portfolio->status
        ]);
    }

    /**
     * Toggle featured status.
     */
    public function toggleFeatured(Portfolio $portfolio)
    {
        $portfolio->update([
            'featured' => !$portfolio->featured
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم تغيير حالة التمييز بنجاح',
            'featured' => $portfolio->featured
        ]);
    }
}
