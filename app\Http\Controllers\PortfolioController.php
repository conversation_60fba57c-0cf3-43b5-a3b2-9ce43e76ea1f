<?php

namespace App\Http\Controllers;

use App\Models\Portfolio;
use Illuminate\Http\Request;

class PortfolioController extends Controller
{
    /**
     * Display the portfolio page
     */
    public function index(Request $request)
    {
        $query = Portfolio::published()->ordered();

        // Filter by category if provided
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Get portfolios with pagination
        $portfolios = $query->paginate(12);

        // Get featured portfolios for hero section
        $featuredPortfolios = Portfolio::published()
            ->featured()
            ->ordered()
            ->limit(6)
            ->get();

        // Get categories for filter
        $categories = [
            'websites' => 'مواقع إلكترونية',
            'apps' => 'تطبيقات موبايل',
            'ecommerce' => 'متاجر إلكترونية',
            'systems' => 'أنظمة إدارة',
            'apis' => 'واجهات برمجية',
            'other' => 'أخرى'
        ];

        // Get portfolio counts by category
        $categoryCounts = [];
        foreach ($categories as $key => $name) {
            $categoryCounts[$key] = Portfolio::published()->where('category', $key)->count();
        }

        return view('portfolio', compact('portfolios', 'featuredPortfolios', 'categories', 'categoryCounts'));
    }

    /**
     * Display a specific portfolio item
     */
    public function show(Portfolio $portfolio)
    {
        // Only show published portfolios to public
        if (!$portfolio->isPublished()) {
            abort(404);
        }

        // Get related portfolios (same category, excluding current)
        $relatedPortfolios = Portfolio::published()
            ->where('category', $portfolio->category)
            ->where('id', '!=', $portfolio->id)
            ->ordered()
            ->limit(3)
            ->get();

        // Get categories for display
        $categories = [
            'websites' => 'مواقع إلكترونية',
            'apps' => 'تطبيقات موبايل',
            'ecommerce' => 'متاجر إلكترونية',
            'systems' => 'أنظمة إدارة',
            'apis' => 'واجهات برمجية',
            'other' => 'أخرى'
        ];

        return view('portfolio-detail', compact('portfolio', 'relatedPortfolios', 'categories'));
    }
}
