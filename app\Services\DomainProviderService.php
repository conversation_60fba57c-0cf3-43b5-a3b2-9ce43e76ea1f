<?php

namespace App\Services;

use App\Models\DomainProvider;
use App\Models\Order;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class DomainProviderService
{
    protected DomainProvider $provider;

    public function __construct(DomainProvider $provider)
    {
        $this->provider = $provider;
    }

    /**
     * Check domain availability
     */
    public function checkAvailability(string $domain): array
    {
        try {
            switch ($this->provider->type) {
                case 'namecheap':
                    return $this->checkNamecheapAvailability($domain);
                case 'godaddy':
                    return $this->checkGoDaddyAvailability($domain);
                case 'cloudflare':
                    return $this->checkCloudflareAvailability($domain);
                default:
                    return $this->checkGenericAvailability($domain);
            }
        } catch (\Exception $e) {
            Log::error('Domain availability check failed', [
                'provider' => $this->provider->type,
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'available' => false,
                'message' => 'فشل في التحقق من توفر الدومين'
            ];
        }
    }

    /**
     * Register domain
     */
    public function registerDomain(Order $order): array
    {
        try {
            switch ($this->provider->type) {
                case 'namecheap':
                    return $this->registerNamecheapDomain($order);
                case 'godaddy':
                    return $this->registerGoDaddyDomain($order);
                case 'cloudflare':
                    return $this->registerCloudflareDomain($order);
                default:
                    return $this->registerGenericDomain($order);
            }
        } catch (\Exception $e) {
            Log::error('Domain registration failed', [
                'provider' => $this->provider->type,
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'فشل في تسجيل الدومين: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Check Namecheap domain availability
     */
    private function checkNamecheapAvailability(string $domain): array
    {
        $response = Http::get($this->provider->api_endpoint . '/xml.response', [
            'ApiUser' => $this->provider->api_config['api_user'] ?? '',
            'ApiKey' => $this->provider->api_key,
            'UserName' => $this->provider->api_config['username'] ?? '',
            'Command' => 'namecheap.domains.check',
            'ClientIp' => request()->ip(),
            'DomainList' => $domain,
        ]);

        if ($response->successful()) {
            $xml = simplexml_load_string($response->body());
            $available = (string)$xml->CommandResponse->DomainCheckResult['Available'] === 'true';
            
            return [
                'success' => true,
                'available' => $available,
                'domain' => $domain,
                'provider' => 'Namecheap'
            ];
        }

        return [
            'success' => false,
            'available' => false,
            'message' => 'Namecheap API error'
        ];
    }

    /**
     * Check GoDaddy domain availability
     */
    private function checkGoDaddyAvailability(string $domain): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'sso-key ' . $this->provider->api_key . ':' . $this->provider->api_secret,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->timeout(10)->get($this->provider->api_endpoint . '/v1/domains/available', [
                'domain' => $domain,
                'checkType' => 'FAST',
                'forTransfer' => false
            ]);

            if ($response->successful()) {
                $data = $response->json();

                return [
                    'success' => true,
                    'available' => $data['available'] ?? false,
                    'domain' => $domain,
                    'provider' => 'GoDaddy',
                    'price' => $data['price'] ?? null,
                    'currency' => $data['currency'] ?? 'USD',
                    'period' => $data['period'] ?? 1
                ];
            }

            Log::warning('GoDaddy API response not successful', [
                'domain' => $domain,
                'status' => $response->status(),
                'response' => $response->body()
            ]);

            return [
                'success' => false,
                'available' => false,
                'message' => 'GoDaddy API error: ' . $response->status()
            ];
        } catch (\Exception $e) {
            Log::error('GoDaddy API exception', [
                'domain' => $domain,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'available' => false,
                'message' => 'GoDaddy API connection error'
            ];
        }
    }

    /**
     * Check multiple domains availability with GoDaddy
     */
    public function checkMultipleGoDaddyAvailability(array $domains): array
    {
        try {
            $response = Http::withHeaders([
                'Authorization' => 'sso-key ' . $this->provider->api_key . ':' . $this->provider->api_secret,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
            ])->timeout(15)->post($this->provider->api_endpoint . '/v1/domains/available', $domains);

            if ($response->successful()) {
                return [
                    'success' => true,
                    'results' => $response->json()
                ];
            }

            return [
                'success' => false,
                'message' => 'GoDaddy bulk API error'
            ];
        } catch (\Exception $e) {
            Log::error('GoDaddy bulk API exception', [
                'domains' => $domains,
                'error' => $e->getMessage()
            ]);

            return [
                'success' => false,
                'message' => 'GoDaddy bulk API connection error'
            ];
        }
    }

    /**
     * Check Cloudflare domain availability
     */
    private function checkCloudflareAvailability(string $domain): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->provider->api_key,
            'Content-Type' => 'application/json',
        ])->get($this->provider->api_endpoint . '/client/v4/registrar/domains/' . $domain . '/availability');

        if ($response->successful()) {
            $data = $response->json();
            
            return [
                'success' => true,
                'available' => $data['result']['available'] ?? false,
                'domain' => $domain,
                'provider' => 'Cloudflare'
            ];
        }

        return [
            'success' => false,
            'available' => false,
            'message' => 'Cloudflare API error'
        ];
    }

    /**
     * Generic availability check (fallback)
     */
    private function checkGenericAvailability(string $domain): array
    {
        // Use WHOIS or other generic method
        $whoisServer = $this->provider->whois_server ?? 'whois.internic.net';
        
        // Simulate availability check
        return [
            'success' => true,
            'available' => rand(0, 1) === 1, // Random for demo
            'domain' => $domain,
            'provider' => $this->provider->name,
            'note' => 'Manual verification required'
        ];
    }

    /**
     * Register Namecheap domain
     */
    private function registerNamecheapDomain(Order $order): array
    {
        $response = Http::get($this->provider->api_endpoint . '/xml.response', [
            'ApiUser' => $this->provider->api_config['api_user'] ?? '',
            'ApiKey' => $this->provider->api_key,
            'UserName' => $this->provider->api_config['username'] ?? '',
            'Command' => 'namecheap.domains.create',
            'ClientIp' => request()->ip(),
            'DomainName' => $order->domain_name,
            'Years' => $order->order_details['years'] ?? 1,
            'RegistrantFirstName' => explode(' ', $order->customer_name)[0],
            'RegistrantLastName' => explode(' ', $order->customer_name)[1] ?? '',
            'RegistrantEmailAddress' => $order->customer_email,
            'RegistrantPhone' => $order->customer_phone ?? '+966.501234567',
        ]);

        if ($response->successful()) {
            $xml = simplexml_load_string($response->body());
            
            return [
                'success' => true,
                'domain_id' => (string)$xml->CommandResponse->DomainCreateResult['Domain'],
                'registration_date' => now(),
                'expiry_date' => now()->addYears($order->order_details['years'] ?? 1),
                'provider_response' => $response->json()
            ];
        }

        return [
            'success' => false,
            'message' => 'Namecheap registration failed'
        ];
    }

    /**
     * Register GoDaddy domain
     */
    private function registerGoDaddyDomain(Order $order): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'sso-key ' . $this->provider->api_key . ':' . $this->provider->api_secret,
            'Content-Type' => 'application/json',
        ])->post($this->provider->api_endpoint . '/v1/domains/purchase', [
            'domain' => $order->domain_name,
            'period' => $order->order_details['years'] ?? 1,
            'contactRegistrant' => [
                'nameFirst' => explode(' ', $order->customer_name)[0],
                'nameLast' => explode(' ', $order->customer_name)[1] ?? '',
                'email' => $order->customer_email,
                'phone' => $order->customer_phone ?? '+966.501234567',
            ],
        ]);

        if ($response->successful()) {
            $data = $response->json();
            
            return [
                'success' => true,
                'domain_id' => $data['orderId'] ?? null,
                'registration_date' => now(),
                'expiry_date' => now()->addYears($order->order_details['years'] ?? 1),
                'provider_response' => $data
            ];
        }

        return [
            'success' => false,
            'message' => 'GoDaddy registration failed'
        ];
    }

    /**
     * Register Cloudflare domain
     */
    private function registerCloudflareDomain(Order $order): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->provider->api_key,
            'Content-Type' => 'application/json',
        ])->post($this->provider->api_endpoint . '/client/v4/registrar/domains', [
            'name' => $order->domain_name,
            'years' => $order->order_details['years'] ?? 1,
        ]);

        if ($response->successful()) {
            $data = $response->json();
            
            return [
                'success' => true,
                'domain_id' => $data['result']['id'] ?? null,
                'registration_date' => now(),
                'expiry_date' => now()->addYears($order->order_details['years'] ?? 1),
                'provider_response' => $data
            ];
        }

        return [
            'success' => false,
            'message' => 'Cloudflare registration failed'
        ];
    }

    /**
     * Generic domain registration (fallback)
     */
    private function registerGenericDomain(Order $order): array
    {
        // Manual registration process
        return [
            'success' => true,
            'domain_id' => 'manual_' . $order->id,
            'registration_date' => now(),
            'expiry_date' => now()->addYears($order->order_details['years'] ?? 1),
            'provider_response' => [
                'type' => 'manual_registration',
                'note' => 'Manual registration required - team will process within 24 hours'
            ]
        ];
    }

    /**
     * Transfer domain
     */
    public function transferDomain(Order $order): array
    {
        // Implementation for domain transfer
        return [
            'success' => true,
            'message' => 'Domain transfer initiated'
        ];
    }

    /**
     * Renew domain
     */
    public function renewDomain(Order $order): array
    {
        // Implementation for domain renewal
        return [
            'success' => true,
            'message' => 'Domain renewed successfully'
        ];
    }
}
