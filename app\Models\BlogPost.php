<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class BlogPost extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'excerpt',
        'content',
        'image',
        'category',
        'author',
        'read_time',
        'is_featured',
        'is_published',
        'views',
        'tags',
        'meta_data'
    ];

    protected $casts = [
        'tags' => 'array',
        'meta_data' => 'array',
        'is_featured' => 'boolean',
        'is_published' => 'boolean',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    // Auto-generate slug from title
    public static function boot()
    {
        parent::boot();

        static::creating(function ($post) {
            if (empty($post->slug)) {
                $post->slug = Str::slug($post->title);

                // Ensure unique slug
                $originalSlug = $post->slug;
                $counter = 1;
                while (static::where('slug', $post->slug)->exists()) {
                    $post->slug = $originalSlug . '-' . $counter;
                    $counter++;
                }
            }
        });
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('is_published', true);
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeRecent($query, $limit = 10)
    {
        return $query->orderBy('created_at', 'desc')->limit($limit);
    }

    // Accessors
    public function getFormattedDateAttribute()
    {
        return $this->created_at->format('d F Y');
    }

    public function getReadTimeInMinutesAttribute()
    {
        return (int) filter_var($this->read_time, FILTER_EXTRACT_NUMBER);
    }

    // Methods
    public function incrementViews()
    {
        $this->increment('views');
    }

    public function getRelatedPosts($limit = 3)
    {
        return static::published()
            ->where('category', $this->category)
            ->where('id', '!=', $this->id)
            ->recent($limit)
            ->get();
    }
}
