@extends('layouts.app')

@section('title', 'تم إنشاء الطلب بنجاح - CodeCraft Solutions')

@section('content')
<!-- Success Hero -->
<section class="relative bg-gradient-to-br from-emerald-900 via-green-900 to-gray-800 py-20">
    <div class="absolute inset-0 bg-black/30"></div>
    <div class="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div data-aos="fade-up">
            <!-- Success Icon -->
            <div class="w-24 h-24 bg-emerald-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                </svg>
            </div>
            
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                تم إنشاء طلبك بنجاح!
            </h1>
            <p class="text-xl text-gray-300 mb-8">
                شكراً لك على اختيار CodeCraft Solutions. سيتم تفعيل خدمة الاستضافة خلال دقائق قليلة.
            </p>
            
            <!-- Order Number -->
            <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20 max-w-md mx-auto">
                <p class="text-gray-300 mb-2">رقم الطلب</p>
                <p class="text-2xl font-bold text-emerald-400">{{ $order->order_number }}</p>
            </div>
        </div>
    </div>
</section>

<!-- Order Details -->
<section class="py-20 bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Order Information -->
            <div class="bg-gray-800 rounded-2xl p-8 border border-gray-700" data-aos="fade-up">
                <h2 class="text-2xl font-bold text-white mb-6">تفاصيل الطلب</h2>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">رقم الطلب</span>
                        <span class="text-white font-semibold">{{ $order->order_number }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">نوع الخدمة</span>
                        <span class="text-white font-semibold">استضافة مواقع</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">الخطة</span>
                        <span class="text-white font-semibold">{{ $order->hostingPlan->name_ar ?? 'غير محدد' }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">الدومين</span>
                        <span class="text-white font-semibold">{{ $order->domain_name }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">تاريخ البداية</span>
                        <span class="text-white font-semibold">{{ $order->service_start_date?->format('Y-m-d') }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">تاريخ الانتهاء</span>
                        <span class="text-white font-semibold">{{ $order->service_end_date?->format('Y-m-d') }}</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3">
                        <span class="text-gray-300">حالة الطلب</span>
                        <span class="px-3 py-1 rounded-full text-sm font-semibold
                            @if($order->status === 'active') bg-emerald-500/20 text-emerald-400
                            @elseif($order->status === 'processing') bg-yellow-500/20 text-yellow-400
                            @elseif($order->status === 'pending') bg-blue-500/20 text-blue-400
                            @else bg-gray-500/20 text-gray-400
                            @endif">
                            @switch($order->status)
                                @case('active') نشط @break
                                @case('processing') قيد المعالجة @break
                                @case('pending') في الانتظار @break
                                @default {{ $order->status }}
                            @endswitch
                        </span>
                    </div>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="bg-gray-800 rounded-2xl p-8 border border-gray-700" data-aos="fade-up" data-aos-delay="100">
                <h2 class="text-2xl font-bold text-white mb-6">معلومات الدفع</h2>
                
                <div class="space-y-4">
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">المبلغ الأساسي</span>
                        <span class="text-white font-semibold">{{ number_format($order->subtotal, 2) }} ريال</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">ضريبة القيمة المضافة</span>
                        <span class="text-white font-semibold">{{ number_format($order->tax_amount, 2) }} ريال</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-lg font-semibold text-white">المجموع</span>
                        <span class="text-xl font-bold text-emerald-400">{{ number_format($order->total_amount, 2) }} ريال</span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3 border-b border-gray-700">
                        <span class="text-gray-300">طريقة الدفع</span>
                        <span class="text-white font-semibold">
                            @switch($order->payment_method)
                                @case('credit_card') بطاقة ائتمانية @break
                                @case('bank_transfer') تحويل بنكي @break
                                @case('paypal') PayPal @break
                                @default {{ $order->payment_method }}
                            @endswitch
                        </span>
                    </div>
                    
                    <div class="flex justify-between items-center py-3">
                        <span class="text-gray-300">حالة الدفع</span>
                        <span class="px-3 py-1 rounded-full text-sm font-semibold
                            @if($order->payment_status === 'paid') bg-emerald-500/20 text-emerald-400
                            @elseif($order->payment_status === 'pending') bg-yellow-500/20 text-yellow-400
                            @elseif($order->payment_status === 'failed') bg-red-500/20 text-red-400
                            @else bg-gray-500/20 text-gray-400
                            @endif">
                            @switch($order->payment_status)
                                @case('paid') مدفوع @break
                                @case('pending') في الانتظار @break
                                @case('failed') فشل @break
                                @case('refunded') مسترد @break
                                @default {{ $order->payment_status }}
                            @endswitch
                        </span>
                    </div>
                    
                    @if($order->transaction_id)
                        <div class="flex justify-between items-center py-3">
                            <span class="text-gray-300">رقم المعاملة</span>
                            <span class="text-white font-mono text-sm">{{ $order->transaction_id }}</span>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="mt-12 bg-gradient-to-r from-emerald-500/10 to-blue-500/10 rounded-2xl p-8 border border-emerald-500/30" data-aos="fade-up" data-aos-delay="200">
            <h2 class="text-2xl font-bold text-white mb-6 text-center">الخطوات التالية</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="w-16 h-16 bg-emerald-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl">1</span>
                    </div>
                    <h3 class="text-lg font-semibold text-white mb-2">تفعيل الحساب</h3>
                    <p class="text-gray-300 text-sm">سيتم تفعيل حساب الاستضافة خلال 5-10 دقائق</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl">2</span>
                    </div>
                    <h3 class="text-lg font-semibold text-white mb-2">استلام البيانات</h3>
                    <p class="text-gray-300 text-sm">ستصلك بيانات الدخول عبر البريد الإلكتروني</p>
                </div>
                
                <div class="text-center">
                    <div class="w-16 h-16 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-white font-bold text-xl">3</span>
                    </div>
                    <h3 class="text-lg font-semibold text-white mb-2">رفع الموقع</h3>
                    <p class="text-gray-300 text-sm">ابدأ برفع ملفات موقعك عبر لوحة التحكم</p>
                </div>
            </div>
        </div>

        <!-- Contact Support -->
        <div class="mt-8 text-center" data-aos="fade-up" data-aos-delay="300">
            <div class="bg-gray-800 rounded-2xl p-6 border border-gray-700">
                <h3 class="text-xl font-bold text-white mb-4">تحتاج مساعدة؟</h3>
                <p class="text-gray-300 mb-6">فريق الدعم الفني متاح على مدار الساعة لمساعدتك</p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="mailto:<EMAIL>" 
                       class="bg-emerald-500 hover:bg-emerald-600 text-white px-6 py-3 rounded-lg font-semibold transition-all">
                        📧 البريد الإلكتروني
                    </a>
                    <a href="tel:+966123456789" 
                       class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold transition-all">
                        📞 الهاتف
                    </a>
                    <a href="#" 
                       class="bg-purple-500 hover:bg-purple-600 text-white px-6 py-3 rounded-lg font-semibold transition-all">
                        💬 الدردشة المباشرة
                    </a>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="mt-8 flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="400">
            <a href="{{ route('home') }}" 
               class="bg-gray-700 hover:bg-gray-600 text-white px-8 py-3 rounded-lg font-semibold transition-all text-center">
                العودة للرئيسية
            </a>
            <a href="{{ route('hosting.dashboard') }}"
               class="bg-emerald-500 hover:bg-emerald-600 text-white px-8 py-3 rounded-lg font-semibold transition-all text-center">
                طلب خدمة أخرى
            </a>
        </div>
    </div>
</section>
@endsection
