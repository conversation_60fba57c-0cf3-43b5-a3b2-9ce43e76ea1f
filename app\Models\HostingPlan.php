<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class HostingPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'type',
        'storage',
        'bandwidth',
        'websites_limit',
        'email_accounts',
        'databases_limit',
        'ssl_certificate',
        'support_24_7',
        'monthly_price',
        'yearly_price',
        'features',
        'is_popular',
        'is_active',
        'sort_order',
        'hosting_provider_id',
        'commission_rate',
    ];

    protected $casts = [
        'features' => 'array',
        'ssl_certificate' => 'boolean',
        'support_24_7' => 'boolean',
        'is_popular' => 'boolean',
        'is_active' => 'boolean',
        'monthly_price' => 'decimal:2',
        'yearly_price' => 'decimal:2',
        'commission_rate' => 'decimal:2',
    ];

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function hostingProvider(): BelongsTo
    {
        return $this->belongsTo(HostingProvider::class);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopePopular($query)
    {
        return $query->where('is_popular', true);
    }

    public function getName($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $locale === 'ar' ? $this->name_ar : ($this->name_en ?: $this->name_ar);
    }

    public function getDescription($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $locale === 'ar' ? $this->description_ar : ($this->description_en ?: $this->description_ar);
    }
}
