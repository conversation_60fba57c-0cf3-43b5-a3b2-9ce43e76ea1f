<?xml version="1.0" encoding="UTF-8"?>
<phpunit
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd"
    bootstrap="vendor/autoload.php"
    colors="true"
>
  <coverage processUncoveredFiles="true">
    <include>
      <directory suffix=".php">./app</directory>
    </include>
  </coverage>
  <testsuites>
<!--    <testsuite name="Unit">-->
<!--        <directory suffix="Test.php">./tests/Unit</directory>-->
<!--    </testsuite>-->
    <testsuite name="Feature">
      <directory suffix="Test.php">./tests/Feature</directory>
    </testsuite>
  </testsuites>
  <!--    <logging>-->
  <!--        <log type="junit" target="build/report.junit.xml"/>-->
  <!--        <log type="coverage-html" target="build/coverage"/>-->
  <!--        <log type="coverage-text" target="build/coverage.txt"/>-->
  <!--        <log type="coverage-clover" target="build/logs/clover.xml"/>-->
  <!--    </logging>-->
  <php>
    <server name="APP_ENV" value="testing"/>
    <server name="APP_READ_ONLY" value="false"/>
    <server name="APP_TESTING" value="true"/>
    <server name="BCRYPT_ROUNDS" value="4"/>
    <server name="CACHE_DRIVER" value="array"/>
    <server name="DB_CONNECTION" value="sqlite"/>
    <server name="DB_DATABASE" value=":memory:"/>
    <server name="ENABLE_REGISTRATION" value="true"/>
    <server name="LOGIN_CAPTCHA_STATUS" value="false"/>
    <server name="REGISTRATION_CAPTCHA_STATUS" value="false"/>
    <server name="MAIL_MAILER" value="array"/>
    <server name="QUEUE_CONNECTION" value="sync"/>
    <server name="SESSION_DRIVER" value="array"/>
    <server name="TELESCOPE_ENABLED" value="false"/>
  </php>
</phpunit>
