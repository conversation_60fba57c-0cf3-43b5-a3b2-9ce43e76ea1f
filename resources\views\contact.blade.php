@extends('layouts.app')

@section('title', 'تواصل معنا - ميتاء تك للحلول البرمجية')

@section('head')
<!-- Leaflet CSS -->
<link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
@endsection

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900">
    <!-- Hero Section -->
    <section class="relative py-20 px-4">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="relative max-w-7xl mx-auto text-center">
            <span class="inline-block px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-sm font-semibold rounded-full mb-4">
                <i class="fas fa-phone mr-2"></i>
                تواصل مع ميتاء تك
            </span>
            <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                تواصل معنا
            </h1>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
                نحن هنا لمساعدتك في تحويل أفكارك إلى واقع رقمي. تواصل معنا اليوم واحصل على استشارة مجانية
            </p>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-12 px-4">
        <div class="max-w-7xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                
                <!-- Contact Form -->
                <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                    <h2 class="text-3xl font-bold text-white mb-6">
                        <i class="fas fa-paper-plane text-blue-400 ml-3"></i>
                        أرسل لنا رسالة
                    </h2>
                    @if(session('success'))
                        <div class="bg-green-500/20 border border-green-500/50 text-green-300 px-4 py-3 rounded-xl mb-6">
                            <i class="fas fa-check-circle mr-2"></i>
                            {{ session('success') }}
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="bg-red-500/20 border border-red-500/50 text-red-300 px-4 py-3 rounded-xl mb-6">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <ul class="list-disc list-inside">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    @endif

                    <form action="{{ route('contact.store') }}" method="POST" class="space-y-6">
                        @csrf
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-gray-300 text-sm font-semibold mb-2">الاسم الكامل *</label>
                                <input type="text" name="name" value="{{ old('name') }}" class="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-all @error('name') border-red-500 @enderror" placeholder="أدخل اسمك الكامل" required>
                            </div>
                            <div>
                                <label class="block text-gray-300 text-sm font-semibold mb-2">البريد الإلكتروني *</label>
                                <input type="email" name="email" value="{{ old('email') }}" class="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-all @error('email') border-red-500 @enderror" placeholder="أدخل بريدك الإلكتروني" required>
                            </div>
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <label class="block text-gray-300 text-sm font-semibold mb-2">رقم الهاتف</label>
                                <input type="tel" name="phone" value="{{ old('phone') }}" class="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-all @error('phone') border-red-500 @enderror" placeholder="أدخل رقم هاتفك">
                            </div>
                            <div>
                                <label class="block text-gray-300 text-sm font-semibold mb-2">اسم الشركة</label>
                                <input type="text" name="company_name" value="{{ old('company_name') }}" class="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-all @error('company_name') border-red-500 @enderror" placeholder="أدخل اسم شركتك (اختياري)">
                            </div>
                        </div>
                        <div>
                            <label class="block text-gray-300 text-sm font-semibold mb-2">نوع الخدمة المطلوبة</label>
                            <div class="relative">
                                <select name="service_type" class="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white focus:outline-none focus:border-blue-500 transition-all appearance-none cursor-pointer @error('service_type') border-red-500 @enderror" style="background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns=&quot;http://www.w3.org/2000/svg&quot; viewBox=&quot;0 0 4 5&quot;><path fill=&quot;%23ffffff&quot; d=&quot;M2 0L0 2h4zm0 5L0 3h4z&quot;/></svg>'); background-repeat: no-repeat; background-position: left 12px center; background-size: 12px;">
                                    <option value="" style="background-color: #1f2937; color: #9ca3af;">اختر نوع الخدمة المطلوبة</option>
                                    <option value="website" {{ old('service_type') == 'website' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        🌐 تطوير موقع إلكتروني
                                    </option>
                                    <option value="mobile" {{ old('service_type') == 'mobile' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        📱 تطبيق موبايل (iOS/Android)
                                    </option>
                                    <option value="ecommerce" {{ old('service_type') == 'ecommerce' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        🛒 متجر إلكتروني
                                    </option>
                                    <option value="hosting" {{ old('service_type') == 'hosting' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        🖥️ خدمات الاستضافة
                                    </option>
                                    <option value="domain" {{ old('service_type') == 'domain' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        🌍 تسجيل دومين
                                    </option>
                                    <option value="email" {{ old('service_type') == 'email' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        📧 خدمات البريد الإلكتروني
                                    </option>
                                    <option value="seo" {{ old('service_type') == 'seo' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        📈 تحسين محركات البحث (SEO)
                                    </option>
                                    <option value="design" {{ old('service_type') == 'design' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        🎨 تصميم جرافيك وهوية بصرية
                                    </option>
                                    <option value="maintenance" {{ old('service_type') == 'maintenance' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        🔧 صيانة ودعم فني
                                    </option>
                                    <option value="consultation" {{ old('service_type') == 'consultation' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        💡 استشارات تقنية
                                    </option>
                                    <option value="custom" {{ old('service_type') == 'custom' ? 'selected' : '' }} style="background-color: #1f2937; color: #ffffff;">
                                        ⚙️ حلول مخصصة أخرى
                                    </option>
                                </select>
                                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                    <i class="fas fa-chevron-down text-gray-400 text-sm"></i>
                                </div>
                            </div>
                        </div>
                        <div>
                            <label class="block text-gray-300 text-sm font-semibold mb-2">موضوع الرسالة *</label>
                            <input type="text" name="subject" value="{{ old('subject') }}" class="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-all @error('subject') border-red-500 @enderror" placeholder="أدخل موضوع رسالتك" required>
                        </div>
                        <div>
                            <label class="block text-gray-300 text-sm font-semibold mb-2">تفاصيل المشروع *</label>
                            <textarea rows="6" name="message" class="w-full px-4 py-3 bg-white/10 backdrop-blur-lg border border-white/20 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:border-blue-500 transition-all resize-none @error('message') border-red-500 @enderror" placeholder="اكتب تفاصيل مشروعك أو استفسارك هنا..." required>{{ old('message') }}</textarea>
                        </div>
                        <button type="submit" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-4 rounded-xl font-bold text-lg hover:scale-105 transition-all duration-300 shadow-lg">
                            <i class="fas fa-paper-plane ml-2"></i>
                            إرسال الرسالة
                        </button>
                    </form>
                </div>

                <!-- Contact Info -->
                <div class="space-y-8">
                    <!-- Contact Cards -->
                    <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                        <h3 class="text-2xl font-bold text-white mb-6">
                            <i class="fas fa-info-circle text-green-400 ml-3"></i>
                            معلومات التواصل
                        </h3>
                        <div class="space-y-6">
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center ml-4">
                                    <i class="fas fa-envelope text-white"></i>
                                </div>
                                <div>
                                    <h4 class="text-white font-semibold">البريد الإلكتروني</h4>
                                    <p class="text-gray-300"><EMAIL></p>
                                    <p class="text-gray-300"><EMAIL></p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center ml-4">
                                    <i class="fas fa-phone text-white"></i>
                                </div>
                                <div>
                                    <h4 class="text-white font-semibold">رقم الهاتف</h4>
                                    <p class="text-gray-300 text-lg font-semibold">782871439</p>
                                    <p class="text-gray-400 text-sm">متاح 24/7</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center ml-4">
                                    <i class="fas fa-map-marker-alt text-white"></i>
                                </div>
                                <div>
                                    <h4 class="text-white font-semibold">العنوان</h4>
                                    <p class="text-gray-300">اليمن - حضرموت - المكلا</p>
                                    <p class="text-gray-400 text-sm">نخدم جميع أنحاء العالم العربي</p>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center ml-4">
                                    <i class="fas fa-clock text-white"></i>
                                </div>
                                <div>
                                    <h4 class="text-white font-semibold">ساعات العمل</h4>
                                    <p class="text-gray-300">الأحد - الخميس: 9:00 ص - 6:00 م</p>
                                    <p class="text-gray-300">دعم فني 24/7</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Social Media -->
                    <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                        <h3 class="text-2xl font-bold text-white mb-6">
                            <i class="fas fa-share-alt text-blue-400 ml-3"></i>
                            تابعنا على
                        </h3>
                        <div class="grid grid-cols-2 gap-4">
                            <a href="#" class="flex items-center justify-center p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl text-white hover:scale-105 transition-all duration-300">
                                <i class="fab fa-twitter text-xl ml-2"></i>
                                تويتر
                            </a>
                            <a href="#" class="flex items-center justify-center p-4 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl text-white hover:scale-105 transition-all duration-300">
                                <i class="fab fa-facebook text-xl ml-2"></i>
                                فيسبوك
                            </a>
                            <a href="#" class="flex items-center justify-center p-4 bg-gradient-to-br from-pink-500 to-purple-600 rounded-xl text-white hover:scale-105 transition-all duration-300">
                                <i class="fab fa-instagram text-xl ml-2"></i>
                                إنستغرام
                            </a>
                            <a href="#" class="flex items-center justify-center p-4 bg-gradient-to-br from-blue-700 to-blue-800 rounded-xl text-white hover:scale-105 transition-all duration-300">
                                <i class="fab fa-linkedin text-xl ml-2"></i>
                                لينكد إن
                            </a>
                            <a href="#" class="flex items-center justify-center p-4 bg-gradient-to-br from-green-500 to-green-600 rounded-xl text-white hover:scale-105 transition-all duration-300">
                                <i class="fab fa-whatsapp text-xl ml-2"></i>
                                واتساب
                            </a>
                            <a href="#" class="flex items-center justify-center p-4 bg-gradient-to-br from-red-500 to-red-600 rounded-xl text-white hover:scale-105 transition-all duration-300">
                                <i class="fab fa-youtube text-xl ml-2"></i>
                                يوتيوب
                            </a>
                        </div>
                    </div>

                    <!-- Quick Response -->
                    <div class="bg-gradient-to-r from-green-500/20 to-emerald-600/20 backdrop-blur-lg rounded-2xl p-8 border border-green-500/30">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-bolt text-white text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-bold text-white mb-3">استجابة سريعة</h3>
                            <p class="text-gray-300 mb-4">نرد على جميع الاستفسارات خلال 24 ساعة</p>
                            <div class="flex justify-center space-x-2">
                                <span class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></span>
                                <span class="w-2 h-2 bg-emerald-500 rounded-full animate-pulse animation-delay-200"></span>
                                <span class="w-2 h-2 bg-teal-500 rounded-full animate-pulse animation-delay-400"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-12 px-4">
        <div class="max-w-7xl mx-auto">
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-8 border border-white/10">
                <h2 class="text-3xl font-bold text-white mb-6 text-center">
                    <i class="fas fa-map-marked-alt text-purple-400 ml-3"></i>
                    موقعنا على الخريطة
                </h2>
                <div class="h-96 bg-gradient-to-br from-gray-700 to-gray-800 rounded-xl overflow-hidden relative">
                    <!-- خريطة تفاعلية -->
                    <div id="map" class="w-full h-full"></div>

                    <!-- معلومات الموقع -->
                    <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-lg rounded-lg p-4 shadow-lg max-w-xs">
                        <div class="flex items-center mb-2">
                            <i class="fas fa-map-marker-alt text-red-500 ml-2"></i>
                            <h4 class="font-bold text-gray-800">موقعنا</h4>
                        </div>
                        <p class="text-gray-700 text-sm">اليمن، حضرموت، المكلا</p>
                        <p class="text-gray-600 text-xs mt-1">ميتاء تك للحلول البرمجية</p>
                    </div>

                    <!-- أزرار التحكم -->
                    <div class="absolute bottom-4 left-4 flex gap-2">
                        <button onclick="zoomIn()" class="bg-white/90 hover:bg-white text-gray-800 p-2 rounded-lg shadow-lg transition-all">
                            <i class="fas fa-plus"></i>
                        </button>
                        <button onclick="zoomOut()" class="bg-white/90 hover:bg-white text-gray-800 p-2 rounded-lg shadow-lg transition-all">
                            <i class="fas fa-minus"></i>
                        </button>
                        <button onclick="resetView()" class="bg-white/90 hover:bg-white text-gray-800 p-2 rounded-lg shadow-lg transition-all">
                            <i class="fas fa-home"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<style>
/* تحسين القائمة المنسدلة */
select {
    background-image: url('data:image/svg+xml;charset=US-ASCII,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 4 5"><path fill="%23ffffff" d="M2 0L0 2h4zm0 5L0 3h4z"/></svg>');
    background-repeat: no-repeat;
    background-position: left 12px center;
    background-size: 12px;
    padding-left: 40px;
}

select option {
    background-color: #1f2937 !important;
    color: #ffffff !important;
    padding: 10px;
    border: none;
}

select option:hover {
    background-color: #374151 !important;
}

select option:checked {
    background-color: #3b82f6 !important;
}

/* تحسين التركيز */
select:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    border-color: #3b82f6;
}

/* تحسين للموبايل */
@media (max-width: 768px) {
    select {
        font-size: 16px; /* منع التكبير في iOS */
    }
}

/* تأثيرات إضافية */
.form-group {
    position: relative;
}

.form-group::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
    border-radius: 12px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.form-group:focus-within::before {
    opacity: 1;
}

/* تحسين الأيقونات */
.service-icon {
    display: inline-block;
    width: 20px;
    text-align: center;
    margin-left: 8px;
}
</style>

<!-- Leaflet JavaScript -->
<script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>

<script>
let map;
let marker;

document.addEventListener('DOMContentLoaded', function() {
    // إعداد الخريطة التفاعلية
    initializeMap();

    // تحسين تفاعل القائمة المنسدلة
    const serviceSelect = document.querySelector('select[name="service_type"]');

    if (serviceSelect) {
        serviceSelect.addEventListener('change', function() {
            const selectedOption = this.options[this.selectedIndex];
            if (selectedOption.value) {
                this.style.color = '#ffffff';
                this.style.fontWeight = '500';
            } else {
                this.style.color = '#9ca3af';
                this.style.fontWeight = '400';
            }
        });

        // تطبيق التنسيق الأولي
        if (serviceSelect.value) {
            serviceSelect.style.color = '#ffffff';
            serviceSelect.style.fontWeight = '500';
        }
    }

    // تحسين تجربة المستخدم للنموذج
    const form = document.querySelector('form');
    const submitBtn = form.querySelector('button[type="submit"]');

    form.addEventListener('submit', function(e) {
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري الإرسال...';
        submitBtn.disabled = true;
    });

    // تحسين رسائل التحقق
    const inputs = form.querySelectorAll('input, select, textarea');
    inputs.forEach(input => {
        input.addEventListener('blur', function() {
            if (this.hasAttribute('required') && !this.value.trim()) {
                this.style.borderColor = '#ef4444';
            } else if (this.value.trim()) {
                this.style.borderColor = '#10b981';
            }
        });

        input.addEventListener('focus', function() {
            this.style.borderColor = '#3b82f6';
        });
    });
});

// إعداد الخريطة التفاعلية
function initializeMap() {
    // إحداثيات المكلا، حضرموت، اليمن
    const mukalla = [14.5425, 49.1242];

    // إنشاء الخريطة
    map = L.map('map', {
        center: mukalla,
        zoom: 13,
        zoomControl: false // إخفاء أزرار التحكم الافتراضية
    });

    // إضافة طبقة الخريطة
    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 19
    }).addTo(map);

    // إضافة علامة مخصصة
    const customIcon = L.divIcon({
        html: '<div class="custom-marker"><i class="fas fa-map-marker-alt text-red-500 text-2xl"></i></div>',
        className: 'custom-div-icon',
        iconSize: [30, 30],
        iconAnchor: [15, 30]
    });

    marker = L.marker(mukalla, { icon: customIcon }).addTo(map);

    // إضافة نافذة منبثقة
    marker.bindPopup(`
        <div class="text-center p-2">
            <h4 class="font-bold text-gray-800 mb-2">ميتاء تك للحلول البرمجية</h4>
            <p class="text-gray-600 text-sm mb-2">اليمن، حضرموت، المكلا</p>
            <div class="flex justify-center gap-2">
                <a href="tel:782871439" class="bg-blue-500 text-white px-3 py-1 rounded text-xs hover:bg-blue-600 transition-all">
                    <i class="fas fa-phone ml-1"></i>اتصل بنا
                </a>
                <a href="https://maps.google.com/?q=14.5425,49.1242" target="_blank" class="bg-green-500 text-white px-3 py-1 rounded text-xs hover:bg-green-600 transition-all">
                    <i class="fas fa-directions ml-1"></i>الاتجاهات
                </a>
            </div>
        </div>
    `).openPopup();

    // تأثيرات إضافية
    setTimeout(() => {
        map.invalidateSize();
    }, 100);
}

// وظائف التحكم في الخريطة
function zoomIn() {
    map.zoomIn();
}

function zoomOut() {
    map.zoomOut();
}

function resetView() {
    const mukalla = [14.5425, 49.1242];
    map.setView(mukalla, 13);
    marker.openPopup();
}
</script>

<style>
/* تنسيق العلامة المخصصة */
.custom-div-icon {
    background: transparent;
    border: none;
}

.custom-marker {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 30px;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));
}

/* تنسيق النافذة المنبثقة */
.leaflet-popup-content-wrapper {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.leaflet-popup-content {
    margin: 8px 12px;
    line-height: 1.4;
}

/* تحسين أزرار التحكم */
.leaflet-control-zoom {
    display: none;
}

/* تأثيرات الخريطة */
#map {
    border-radius: 12px;
}

/* تحسين للموبايل */
@media (max-width: 768px) {
    .absolute.top-4.right-4 {
        position: relative;
        top: auto;
        right: auto;
        margin: 10px;
        max-width: none;
    }

    .absolute.bottom-4.left-4 {
        position: absolute;
        bottom: 10px;
        left: 10px;
    }
}
</style>
</script>
@endsection
