/* Browser Compatibility CSS for Meta Tech Website */
/* Ensures consistent appearance across all browsers */

/* CSS Reset for Cross-Browser Compatibility */
* {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
}

/* Fix for Internet Explorer */
html {
    -ms-text-size-adjust: 100%;
    -webkit-text-size-adjust: 100%;
}

/* Font Smoothing for Better Text Rendering */
body, * {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Flexbox Fixes for Older Browsers */
.flex {
    display: -webkit-box;
    display: -webkit-flex;
    display: -ms-flexbox;
    display: flex;
}

.flex-wrap {
    -webkit-flex-wrap: wrap;
    -ms-flex-wrap: wrap;
    flex-wrap: wrap;
}

.items-center {
    -webkit-box-align: center;
    -webkit-align-items: center;
    -ms-flex-align: center;
    align-items: center;
}

.justify-center {
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -ms-flex-pack: center;
    justify-content: center;
}

.justify-between {
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

/* Grid Layout Fallbacks */
.grid {
    display: -ms-grid;
    display: grid;
}

/* Fallback for browsers without grid support */
@supports not (display: grid) {
    .grid {
        display: flex;
        flex-wrap: wrap;
    }
    
    .grid > * {
        flex: 1;
        min-width: 300px;
        margin: 10px;
    }
}

/* Transform Prefixes */
.transform {
    -webkit-transform: translateZ(0);
    -moz-transform: translateZ(0);
    -ms-transform: translateZ(0);
    -o-transform: translateZ(0);
    transform: translateZ(0);
}

/* Transition Prefixes */
.transition {
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
    transition: all 0.3s ease;
}

/* Border Radius for Older Browsers */
.rounded {
    -webkit-border-radius: 0.375rem;
    -moz-border-radius: 0.375rem;
    border-radius: 0.375rem;
}

.rounded-lg {
    -webkit-border-radius: 0.5rem;
    -moz-border-radius: 0.5rem;
    border-radius: 0.5rem;
}

/* Box Shadow Prefixes */
.shadow {
    -webkit-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.shadow-lg {
    -webkit-box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    -moz-box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Gradient Backgrounds with Fallbacks */
.gradient-bg {
    background: #8b5cf6; /* Fallback */
    background: -webkit-linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    background: -moz-linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    background: -ms-linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    background: -o-linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
    background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
}

/* Backdrop Filter Fallback */
.backdrop-blur {
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

/* Fallback for browsers without backdrop-filter */
@supports not (backdrop-filter: blur(10px)) {
    .backdrop-blur {
        background: rgba(255, 255, 255, 0.9);
    }
}

/* CSS Variables Fallbacks */
:root {
    --primary-color: #8b5cf6;
    --secondary-color: #3b82f6;
    --text-color: #1f2937;
    --bg-color: #ffffff;
}

/* Fallbacks for browsers without CSS variables */
.btn-primary {
    background-color: #8b5cf6;
    background-color: var(--primary-color, #8b5cf6);
    color: #ffffff;
}

.text-primary {
    color: #8b5cf6;
    color: var(--primary-color, #8b5cf6);
}

/* Internet Explorer Specific Fixes */
@media screen and (-ms-high-contrast: active), (-ms-high-contrast: none) {
    /* IE10+ specific styles */
    .flex {
        display: -ms-flexbox;
    }
    
    .grid {
        display: block;
    }
    
    .grid > * {
        display: inline-block;
        width: 48%;
        vertical-align: top;
        margin: 1%;
    }
}

/* Safari Specific Fixes */
@media not all and (min-resolution:.001dpcm) {
    @supports (-webkit-appearance:none) {
        /* Safari-specific styles */
        .btn {
            -webkit-appearance: none;
        }
        
        input, textarea, select {
            -webkit-appearance: none;
            border-radius: 0;
        }
    }
}

/* Firefox Specific Fixes */
@-moz-document url-prefix() {
    /* Firefox-specific styles */
    .btn {
        -moz-appearance: none;
    }
    
    input[type="number"] {
        -moz-appearance: textfield;
    }
}

/* Edge Specific Fixes */
@supports (-ms-ime-align:auto) {
    /* Edge-specific styles */
    .grid {
        display: -ms-grid;
    }
}

/* Mobile Safari Fixes */
@supports (-webkit-touch-callout: none) {
    /* iOS Safari specific */
    .btn {
        -webkit-touch-callout: none;
        -webkit-user-select: none;
        -webkit-tap-highlight-color: transparent;
    }
    
    input, textarea {
        -webkit-appearance: none;
        border-radius: 0;
    }
}

/* Print Styles for All Browsers */
@media print {
    * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
    }
    
    .no-print {
        display: none !important;
    }
    
    .print-only {
        display: block !important;
    }
}

/* High Contrast Mode Support */
@media (prefers-contrast: high) {
    .btn {
        border: 2px solid;
    }
    
    .card {
        border: 1px solid;
    }
}

/* Reduced Motion Support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    :root {
        --bg-color: #1f2937;
        --text-color: #f9fafb;
    }
}

/* RTL Support Enhancements */
[dir="rtl"] .text-left {
    text-align: right;
}

[dir="rtl"] .text-right {
    text-align: left;
}

[dir="rtl"] .ml-auto {
    margin-left: 0;
    margin-right: auto;
}

[dir="rtl"] .mr-auto {
    margin-right: 0;
    margin-left: auto;
}

/* Focus Styles for Accessibility */
*:focus {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* Remove outline for mouse users */
.js-focus-visible *:focus:not(.focus-visible) {
    outline: none;
}

/* Ensure images are responsive in all browsers */
img {
    max-width: 100%;
    height: auto;
    -ms-interpolation-mode: bicubic; /* IE */
}

/* Fix for sticky positioning in older browsers */
@supports not (position: sticky) {
    .sticky {
        position: fixed;
    }
}
