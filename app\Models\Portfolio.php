<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

class Portfolio extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'slug',
        'description',
        'short_description',
        'category',
        'technologies',
        'client_name',
        'project_url',
        'github_url',
        'images',
        'featured_image',
        'completion_date',
        'duration_days',
        'budget',
        'status',
        'is_featured',
        'is_active',
        'sort_order',
        'seo_meta'
    ];

    protected $casts = [
        'technologies' => 'array',
        'images' => 'array',
        'seo_meta' => 'array',
        'completion_date' => 'date',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
        'budget' => 'decimal:2'
    ];

    // Auto-generate slug from title
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($portfolio) {
            if (empty($portfolio->slug)) {
                $portfolio->slug = Str::slug($portfolio->title);
            }
        });

        static::updating(function ($portfolio) {
            if ($portfolio->isDirty('title') && empty($portfolio->slug)) {
                $portfolio->slug = Str::slug($portfolio->title);
            }
        });
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('created_at', 'desc');
    }

    // Accessors
    public function getFeaturedImageUrlAttribute()
    {
        return $this->featured_image ? asset('storage/' . $this->featured_image) : null;
    }

    public function getImageUrlsAttribute()
    {
        if (!$this->images) return [];

        return collect($this->images)->map(function ($image) {
            return asset('storage/' . $image);
        })->toArray();
    }

    public function getTechnologiesListAttribute()
    {
        return $this->technologies ? implode(', ', $this->technologies) : '';
    }

    public function getCategoryNameAttribute()
    {
        $categories = [
            'websites' => 'مواقع إلكترونية',
            'apps' => 'تطبيقات موبايل',
            'ecommerce' => 'متاجر إلكترونية',
            'systems' => 'أنظمة إدارة',
            'apis' => 'واجهات برمجية',
            'other' => 'أخرى'
        ];

        return $categories[$this->category] ?? $this->category;
    }

    public function getStatusNameAttribute()
    {
        $statuses = [
            'draft' => 'مسودة',
            'published' => 'منشور',
            'featured' => 'مميز'
        ];

        return $statuses[$this->status] ?? $this->status;
    }

    // Helper methods
    public function getRouteKeyName()
    {
        return 'slug';
    }

    public function isPublished()
    {
        return $this->status === 'published';
    }

    public function isFeatured()
    {
        return $this->is_featured;
    }

    public function isActive()
    {
        return $this->is_active;
    }
}
