@extends('backend.layouts.app')

@section('title', 'التقارير والإحصائيات')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-chart-bar me-2 text-primary"></i>
                        التقارير والإحصائيات
                    </h1>
                    <p class="text-muted mb-0">تحليل شامل لأداء النظام والمستخدمين</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="exportReport()">
                        <i class="fas fa-download me-2"></i>
                        تصدير التقرير
                    </button>
                    <button class="btn btn-primary" onclick="generateReport()">
                        <i class="fas fa-sync-alt me-2"></i>
                        تحديث البيانات
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Date Range Filter -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-end">
                        <div class="col-md-3">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="startDate" value="{{ now()->subDays(30)->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="endDate" value="{{ now()->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">نوع التقرير</label>
                            <select class="form-select" id="reportType">
                                <option value="overview">نظرة عامة</option>
                                <option value="users">المستخدمين</option>
                                <option value="activity">النشاط</option>
                                <option value="performance">الأداء</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-primary w-100" onclick="filterReports()">
                                <i class="fas fa-filter me-2"></i>
                                تطبيق الفلتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Key Metrics -->
    <div class="row mb-4">
        <div class="col-12">
            <x-backend.stats-cards 
                :cards="[
                    [
                        'title' => 'إجمالي الزيارات',
                        'value' => 15420,
                        'previous_value' => 14200,
                        'change' => 8.6,
                        'change_period' => 'من الشهر الماضي',
                        'icon' => 'fas fa-eye',
                        'gradient' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        'chart_data' => [
                            'labels' => ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                            'values' => [3200, 3800, 4100, 4320]
                        ]
                    ],
                    [
                        'title' => 'المستخدمين النشطين',
                        'value' => 2847,
                        'previous_value' => 2650,
                        'change' => 7.4,
                        'change_period' => 'من الشهر الماضي',
                        'icon' => 'fas fa-users',
                        'gradient' => 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                        'chart_data' => [
                            'labels' => ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                            'values' => [680, 720, 750, 697]
                        ]
                    ],
                    [
                        'title' => 'معدل التحويل',
                        'value' => 3.2,
                        'previous_value' => 2.8,
                        'change' => 14.3,
                        'change_period' => 'من الشهر الماضي',
                        'icon' => 'fas fa-percentage',
                        'gradient' => 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                        'chart_data' => [
                            'labels' => ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                            'values' => [2.8, 3.1, 3.4, 3.2]
                        ]
                    ],
                    [
                        'title' => 'متوسط وقت الجلسة',
                        'value' => 245,
                        'previous_value' => 220,
                        'change' => 11.4,
                        'change_period' => 'ثانية من الشهر الماضي',
                        'icon' => 'fas fa-clock',
                        'gradient' => 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                        'chart_data' => [
                            'labels' => ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'],
                            'values' => [220, 235, 250, 245]
                        ]
                    ]
                ]"
                :animated="true"
                :realtime="true"
                :chart-enabled="true"
                :compare-enabled="true" />
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <!-- Traffic Chart -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-chart-line text-primary me-2"></i>
                            إحصائيات الزيارات
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary active" data-period="7">7 أيام</button>
                            <button class="btn btn-outline-primary" data-period="30">30 يوم</button>
                            <button class="btn btn-outline-primary" data-period="90">90 يوم</button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <canvas id="trafficChart" height="300"></canvas>
                </div>
            </div>
        </div>

        <!-- User Activity -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-clock text-success me-2"></i>
                        نشاط المستخدمين
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="userActivityChart" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Detailed Reports -->
    <div class="row mb-4">
        <!-- Top Pages -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt text-info me-2"></i>
                        أكثر الصفحات زيارة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الصفحة</th>
                                    <th>الزيارات</th>
                                    <th>النسبة</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-home text-primary me-2"></i>
                                            الصفحة الرئيسية
                                        </div>
                                    </td>
                                    <td><strong>4,520</strong></td>
                                    <td>
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar bg-primary" style="width: 85%"></div>
                                        </div>
                                        <small class="text-muted">85%</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-user text-success me-2"></i>
                                            لوحة التحكم
                                        </div>
                                    </td>
                                    <td><strong>2,340</strong></td>
                                    <td>
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar bg-success" style="width: 65%"></div>
                                        </div>
                                        <small class="text-muted">65%</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-cog text-warning me-2"></i>
                                            الإعدادات
                                        </div>
                                    </td>
                                    <td><strong>1,890</strong></td>
                                    <td>
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar bg-warning" style="width: 45%"></div>
                                        </div>
                                        <small class="text-muted">45%</small>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <i class="fas fa-chart-bar text-info me-2"></i>
                                            التقارير
                                        </div>
                                    </td>
                                    <td><strong>1,250</strong></td>
                                    <td>
                                        <div class="progress" style="height: 6px;">
                                            <div class="progress-bar bg-info" style="width: 35%"></div>
                                        </div>
                                        <small class="text-muted">35%</small>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Browser Stats -->
        <div class="col-lg-6 mb-4">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-globe text-warning me-2"></i>
                        إحصائيات المتصفحات
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="browserChart" height="250"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- System Performance -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-server text-danger me-2"></i>
                        أداء النظام
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="performance-metric">
                                <div class="metric-icon bg-primary">
                                    <i class="fas fa-microchip"></i>
                                </div>
                                <div class="metric-info">
                                    <h6>استخدام المعالج</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-primary" style="width: 45%"></div>
                                    </div>
                                    <small class="text-muted">45% متوسط</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="performance-metric">
                                <div class="metric-icon bg-success">
                                    <i class="fas fa-memory"></i>
                                </div>
                                <div class="metric-info">
                                    <h6>استخدام الذاكرة</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-success" style="width: 68%"></div>
                                    </div>
                                    <small class="text-muted">68% من 8GB</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="performance-metric">
                                <div class="metric-icon bg-warning">
                                    <i class="fas fa-hdd"></i>
                                </div>
                                <div class="metric-info">
                                    <h6>مساحة القرص</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-warning" style="width: 32%"></div>
                                    </div>
                                    <small class="text-muted">32% من 500GB</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="performance-metric">
                                <div class="metric-icon bg-info">
                                    <i class="fas fa-wifi"></i>
                                </div>
                                <div class="metric-info">
                                    <h6>سرعة الشبكة</h6>
                                    <div class="progress mb-2">
                                        <div class="progress-bar bg-info" style="width: 85%"></div>
                                    </div>
                                    <small class="text-muted">85 Mbps</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Log -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-history text-secondary me-2"></i>
                            سجل النشاط الأخير
                        </h5>
                        <a href="{{ route('admin.activity-log') }}" class="btn btn-sm btn-outline-primary">
                            عرض السجل الكامل
                            <i class="fas fa-arrow-left ms-1"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="activity-timeline">
                        <div class="activity-item">
                            <div class="activity-icon bg-success">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="activity-title">تم إنشاء مستخدم جديد</h6>
                                <p class="activity-description">تم تسجيل المستخدم "أحمد محمد" في النظام</p>
                                <small class="text-muted">منذ 5 دقائق</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon bg-info">
                                <i class="fas fa-edit"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="activity-title">تحديث الإعدادات</h6>
                                <p class="activity-description">تم تحديث إعدادات النظام العامة</p>
                                <small class="text-muted">منذ 15 دقيقة</small>
                            </div>
                        </div>
                        <div class="activity-item">
                            <div class="activity-icon bg-warning">
                                <i class="fas fa-shield-alt"></i>
                            </div>
                            <div class="activity-content">
                                <h6 class="activity-title">تحديث الصلاحيات</h6>
                                <p class="activity-description">تم تعديل صلاحيات دور "المشرف"</p>
                                <small class="text-muted">منذ 30 دقيقة</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('after-styles')
<style>
.performance-metric {
    display: flex;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 1rem;
}

.metric-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-left: 1rem;
    font-size: 1.2rem;
}

.metric-info {
    flex: 1;
}

.metric-info h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.progress {
    height: 8px;
    border-radius: 4px;
}

.activity-timeline {
    position: relative;
    padding-right: 30px;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    right: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #e9ecef;
}

.activity-item {
    position: relative;
    margin-bottom: 30px;
    display: flex;
    align-items: flex-start;
}

.activity-icon {
    position: absolute;
    right: -22px;
    top: 5px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.activity-content {
    background: #f8f9fa;
    padding: 1rem 1.5rem;
    border-radius: 8px;
    border-right: 3px solid #667eea;
    width: 100%;
}

.activity-title {
    margin-bottom: 0.5rem;
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
}

.activity-description {
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
    color: #6b7280;
}

.btn-group .btn {
    border-radius: 6px;
    font-size: 0.8rem;
    padding: 0.375rem 0.75rem;
}

.btn-group .btn.active {
    background: #667eea;
    border-color: #667eea;
    color: white;
}

.card {
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1) !important;
}

.table th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    color: #6c757d;
    padding: 1rem 0.75rem;
}

.table td {
    border: none;
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.table tbody tr {
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.chart-container {
    position: relative;
    height: 300px;
}

@media (max-width: 768px) {
    .performance-metric {
        flex-direction: column;
        text-align: center;
    }

    .metric-icon {
        margin: 0 0 1rem 0;
    }

    .activity-timeline {
        padding-right: 20px;
    }

    .activity-timeline::before {
        right: 10px;
    }

    .activity-icon {
        right: -15px;
        width: 30px;
        height: 30px;
        font-size: 0.75rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.25rem;
    }
}

/* Chart Animations */
@keyframes chartFadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chart-animated {
    animation: chartFadeIn 0.8s ease-out;
}

/* Loading States */
.chart-loading {
    position: relative;
}

.chart-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
</style>
@endpush

@push('after-scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let trafficChart, userActivityChart, browserChart;

// Initialize charts when page loads
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    setupEventListeners();
    startRealTimeUpdates();
});

function initializeCharts() {
    // Traffic Chart
    const trafficCtx = document.getElementById('trafficChart').getContext('2d');
    trafficChart = new Chart(trafficCtx, {
        type: 'line',
        data: {
            labels: ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'],
            datasets: [{
                label: 'الزيارات',
                data: [1200, 1900, 3000, 5000, 2000, 3000, 4500],
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#667eea',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }, {
                label: 'المستخدمين الفريدين',
                data: [800, 1200, 2000, 3200, 1500, 2100, 3000],
                borderColor: '#f093fb',
                backgroundColor: 'rgba(240, 147, 251, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: '#f093fb',
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0,0,0,0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            },
            elements: {
                point: {
                    hoverRadius: 8
                }
            }
        }
    });

    // User Activity Chart (Doughnut)
    const userActivityCtx = document.getElementById('userActivityChart').getContext('2d');
    userActivityChart = new Chart(userActivityCtx, {
        type: 'doughnut',
        data: {
            labels: ['نشط', 'غير نشط', 'جديد', 'محظور'],
            datasets: [{
                data: [65, 20, 12, 3],
                backgroundColor: [
                    '#28a745',
                    '#6c757d',
                    '#007bff',
                    '#dc3545'
                ],
                borderWidth: 0,
                cutout: '70%'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });

    // Browser Chart (Bar)
    const browserCtx = document.getElementById('browserChart').getContext('2d');
    browserChart = new Chart(browserCtx, {
        type: 'bar',
        data: {
            labels: ['Chrome', 'Firefox', 'Safari', 'Edge', 'أخرى'],
            datasets: [{
                label: 'النسبة المئوية',
                data: [45, 25, 15, 10, 5],
                backgroundColor: [
                    '#4285f4',
                    '#ff9500',
                    '#00d4ff',
                    '#0078d4',
                    '#6c757d'
                ],
                borderRadius: 6,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    max: 50,
                    grid: {
                        color: 'rgba(0,0,0,0.05)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });

    // Add animation class
    document.querySelectorAll('canvas').forEach(canvas => {
        canvas.parentElement.classList.add('chart-animated');
    });
}

function setupEventListeners() {
    // Period buttons for traffic chart
    document.querySelectorAll('[data-period]').forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            document.querySelectorAll('[data-period]').forEach(b => b.classList.remove('active'));
            // Add active class to clicked button
            this.classList.add('active');

            // Update chart data based on period
            updateTrafficChart(this.dataset.period);
        });
    });
}

function updateTrafficChart(period) {
    const chartContainer = trafficChart.canvas.parentElement;
    chartContainer.classList.add('chart-loading');

    // Simulate API call
    setTimeout(() => {
        let newData, newLabels;

        switch(period) {
            case '7':
                newLabels = ['الاثنين', 'الثلاثاء', 'الأربعاء', 'الخميس', 'الجمعة', 'السبت', 'الأحد'];
                newData = [1200, 1900, 3000, 5000, 2000, 3000, 4500];
                break;
            case '30':
                newLabels = ['الأسبوع 1', 'الأسبوع 2', 'الأسبوع 3', 'الأسبوع 4'];
                newData = [8500, 12000, 15000, 18500];
                break;
            case '90':
                newLabels = ['الشهر 1', 'الشهر 2', 'الشهر 3'];
                newData = [45000, 52000, 48000];
                break;
        }

        trafficChart.data.labels = newLabels;
        trafficChart.data.datasets[0].data = newData;
        trafficChart.update('active');

        chartContainer.classList.remove('chart-loading');
    }, 1000);
}

function filterReports() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const reportType = document.getElementById('reportType').value;

    if (!startDate || !endDate) {
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.warning('يرجى تحديد تاريخ البداية والنهاية');
        } else {
            alert('يرجى تحديد تاريخ البداية والنهاية');
        }
        return;
    }

    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    btn.disabled = true;

    // Simulate API call
    setTimeout(() => {
        // Update charts and data based on filters
        updateAllCharts();

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم تحديث التقرير بنجاح');
        }

        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 2000);
}

function updateAllCharts() {
    // Update all charts with new data
    trafficChart.update('active');
    userActivityChart.update('active');
    browserChart.update('active');
}

function generateReport() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحديث...';
    btn.disabled = true;

    // Simulate report generation
    setTimeout(() => {
        updateAllCharts();
        updateStatistics();

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم تحديث البيانات بنجاح');
        }

        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 3000);
}

function exportReport() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...';
    btn.disabled = true;

    // Simulate export
    setTimeout(() => {
        // Create and download file
        const reportData = generateReportData();
        downloadReport(reportData);

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم تصدير التقرير بنجاح');
        }

        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 2000);
}

function generateReportData() {
    return {
        title: 'تقرير النظام الشامل',
        date: new Date().toLocaleDateString('ar-SA'),
        statistics: {
            totalVisits: 15420,
            activeUsers: 2847,
            conversionRate: 3.2,
            avgSessionTime: 245
        }
    };
}

function downloadReport(data) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `report_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function updateStatistics() {
    // Update statistics cards with new data
    const stats = document.querySelectorAll('.stats-card .h3');
    stats.forEach(stat => {
        const currentValue = parseInt(stat.textContent.replace(/[^\d]/g, ''));
        const newValue = currentValue + Math.floor(Math.random() * 100);
        animateNumber(stat, currentValue, newValue);
    });
}

function animateNumber(element, start, end) {
    const duration = 1000;
    const startTime = Date.now();

    function update() {
        const elapsed = Date.now() - startTime;
        const progress = Math.min(elapsed / duration, 1);

        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current.toLocaleString('ar-SA');

        if (progress < 1) {
            requestAnimationFrame(update);
        }
    }

    update();
}

function startRealTimeUpdates() {
    // Update data every 30 seconds
    setInterval(() => {
        updateStatistics();
    }, 30000);
}
</script>
@endpush
