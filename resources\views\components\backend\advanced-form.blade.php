{{-- قالب النماذج المتقدمة --}}
@props([
    'title' => 'نموذج جديد',
    'action' => '',
    'method' => 'POST',
    'enctype' => null,
    'submitText' => 'حفظ',
    'cancelRoute' => null,
    'steps' => null,
    'validation' => true,
    'autosave' => false,
    'confirmBeforeSubmit' => false
])

<div class="advanced-form-container" x-data="advancedForm()" x-init="init()">
    <form action="{{ $action }}" 
          method="{{ $method === 'GET' ? 'GET' : 'POST' }}" 
          @if($enctype) enctype="{{ $enctype }}" @endif
          x-on:submit="handleSubmit"
          novalidate>
        
        @if($method !== 'GET' && $method !== 'POST')
            @method($method)
        @endif
        @csrf

        <!-- Form Header -->
        <div class="card shadow-sm border-0 mb-4">
            <div class="card-header bg-gradient-primary text-white">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-edit me-2"></i>
                            {{ $title }}
                        </h5>
                    </div>
                    <div class="col-md-4 text-end">
                        @if($autosave)
                        <span class="badge bg-light text-dark" x-show="autoSaveStatus">
                            <i class="fas fa-save me-1"></i>
                            <span x-text="autoSaveStatus"></span>
                        </span>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Multi-Step Progress -->
            @if($steps)
            <div class="card-body border-bottom">
                <div class="step-progress">
                    <div class="progress mb-3">
                        <div class="progress-bar bg-gradient-primary" 
                             :style="`width: ${(currentStep / totalSteps) * 100}%`"></div>
                    </div>
                    <div class="row">
                        @foreach($steps as $index => $step)
                        <div class="col step-item" 
                             :class="{ 
                                 'active': currentStep === {{ $index + 1 }}, 
                                 'completed': currentStep > {{ $index + 1 }} 
                             }">
                            <div class="step-circle">
                                <i class="fas" 
                                   :class="currentStep > {{ $index + 1 }} ? 'fa-check' : '{{ $step['icon'] ?? 'fa-circle' }}'"></i>
                            </div>
                            <div class="step-label">{{ $step['title'] }}</div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Form Content -->
            <div class="card-body">
                @if($steps)
                    <!-- Multi-Step Content -->
                    @foreach($steps as $index => $step)
                    <div class="step-content" x-show="currentStep === {{ $index + 1 }}" x-transition>
                        <h6 class="step-title mb-3">{{ $step['title'] }}</h6>
                        @if(isset($step['description']))
                        <p class="text-muted mb-4">{{ $step['description'] }}</p>
                        @endif
                        
                        {{ ${'step' . ($index + 1)} ?? '' }}
                    </div>
                    @endforeach
                @else
                    <!-- Single Step Content -->
                    {{ $slot }}
                @endif

                <!-- Validation Errors -->
                @if($validation && $errors->any())
                <div class="alert alert-danger mt-4" x-show="showErrors" x-transition>
                    <h6 class="alert-heading">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        يرجى تصحيح الأخطاء التالية:
                    </h6>
                    <ul class="mb-0">
                        @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif
            </div>

            <!-- Form Actions -->
            <div class="card-footer bg-light">
                <div class="row">
                    <div class="col-md-6">
                        @if($cancelRoute)
                        <a href="{{ $cancelRoute }}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>
                            إلغاء
                        </a>
                        @endif
                    </div>
                    <div class="col-md-6 text-end">
                        @if($steps)
                        <!-- Multi-Step Navigation -->
                        <button type="button" 
                                class="btn btn-outline-primary me-2" 
                                x-show="currentStep > 1"
                                x-on:click="previousStep()">
                            <i class="fas fa-arrow-right me-1"></i>
                            السابق
                        </button>
                        
                        <button type="button" 
                                class="btn btn-primary" 
                                x-show="currentStep < totalSteps"
                                x-on:click="nextStep()">
                            التالي
                            <i class="fas fa-arrow-left ms-1"></i>
                        </button>
                        
                        <button type="submit" 
                                class="btn btn-success" 
                                x-show="currentStep === totalSteps"
                                :disabled="isSubmitting">
                            <i class="fas fa-save me-1" x-show="!isSubmitting"></i>
                            <i class="fas fa-spinner fa-spin me-1" x-show="isSubmitting"></i>
                            <span x-text="isSubmitting ? 'جاري الحفظ...' : '{{ $submitText }}'"></span>
                        </button>
                        @else
                        <!-- Single Step Submit -->
                        <button type="submit" 
                                class="btn btn-success" 
                                :disabled="isSubmitting">
                            <i class="fas fa-save me-1" x-show="!isSubmitting"></i>
                            <i class="fas fa-spinner fa-spin me-1" x-show="isSubmitting"></i>
                            <span x-text="isSubmitting ? 'جاري الحفظ...' : '{{ $submitText }}'"></span>
                        </button>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<!-- Confirmation Modal -->
@if($confirmBeforeSubmit)
<div class="modal fade" id="confirmModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد العملية</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>هل أنت متأكد من أنك تريد حفظ هذه البيانات؟</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                <button type="button" class="btn btn-success" x-on:click="confirmSubmit()">تأكيد</button>
            </div>
        </div>
    </div>
</div>
@endif

<script>
function advancedForm() {
    return {
        currentStep: 1,
        totalSteps: {{ $steps ? count($steps) : 1 }},
        isSubmitting: false,
        showErrors: {{ $errors->any() ? 'true' : 'false' }},
        autoSaveStatus: '',
        formData: {},
        validationErrors: {},
        
        init() {
            @if($autosave)
            this.setupAutoSave();
            @endif
            this.setupValidation();
            this.loadSavedData();
        },
        
        nextStep() {
            if (this.validateCurrentStep()) {
                if (this.currentStep < this.totalSteps) {
                    this.currentStep++;
                    this.saveProgress();
                }
            }
        },
        
        previousStep() {
            if (this.currentStep > 1) {
                this.currentStep--;
            }
        },
        
        validateCurrentStep() {
            const currentStepFields = this.getCurrentStepFields();
            let isValid = true;
            
            currentStepFields.forEach(field => {
                const element = document.querySelector(`[name="${field}"]`);
                if (element && element.hasAttribute('required') && !element.value.trim()) {
                    this.showFieldError(field, 'هذا الحقل مطلوب');
                    isValid = false;
                } else {
                    this.clearFieldError(field);
                }
            });
            
            return isValid;
        },
        
        getCurrentStepFields() {
            const stepContent = document.querySelector(`.step-content:nth-child(${this.currentStep})`);
            const fields = stepContent ? stepContent.querySelectorAll('[name]') : [];
            return Array.from(fields).map(field => field.name);
        },
        
        showFieldError(fieldName, message) {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.add('is-invalid');
                
                let errorDiv = field.parentNode.querySelector('.invalid-feedback');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'invalid-feedback';
                    field.parentNode.appendChild(errorDiv);
                }
                errorDiv.textContent = message;
            }
        },
        
        clearFieldError(fieldName) {
            const field = document.querySelector(`[name="${fieldName}"]`);
            if (field) {
                field.classList.remove('is-invalid');
                const errorDiv = field.parentNode.querySelector('.invalid-feedback');
                if (errorDiv) {
                    errorDiv.remove();
                }
            }
        },
        
        handleSubmit(event) {
            @if($confirmBeforeSubmit)
            event.preventDefault();
            const modal = new bootstrap.Modal(document.getElementById('confirmModal'));
            modal.show();
            @else
            if (!this.validateForm()) {
                event.preventDefault();
                return false;
            }
            
            this.isSubmitting = true;
            this.clearSavedData();
            @endif
        },
        
        confirmSubmit() {
            const modal = bootstrap.Modal.getInstance(document.getElementById('confirmModal'));
            modal.hide();
            
            if (this.validateForm()) {
                this.isSubmitting = true;
                this.clearSavedData();
                document.querySelector('form').submit();
            }
        },
        
        validateForm() {
            const form = document.querySelector('form');
            const requiredFields = form.querySelectorAll('[required]');
            let isValid = true;
            
            requiredFields.forEach(field => {
                if (!field.value.trim()) {
                    this.showFieldError(field.name, 'هذا الحقل مطلوب');
                    isValid = false;
                } else {
                    this.clearFieldError(field.name);
                }
            });
            
            return isValid;
        },
        
        setupAutoSave() {
            const form = document.querySelector('form');
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                input.addEventListener('input', () => {
                    this.autoSave();
                });
            });
        },
        
        autoSave() {
            this.autoSaveStatus = 'جاري الحفظ...';
            
            setTimeout(() => {
                this.saveFormData();
                this.autoSaveStatus = 'تم الحفظ تلقائياً';
                
                setTimeout(() => {
                    this.autoSaveStatus = '';
                }, 2000);
            }, 1000);
        },
        
        saveFormData() {
            const form = document.querySelector('form');
            const formData = new FormData(form);
            const data = {};
            
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            localStorage.setItem('form_autosave_{{ $action }}', JSON.stringify({
                data: data,
                step: this.currentStep,
                timestamp: Date.now()
            }));
        },
        
        loadSavedData() {
            const saved = localStorage.getItem('form_autosave_{{ $action }}');
            if (saved) {
                const parsedData = JSON.parse(saved);
                
                // Load data if it's less than 24 hours old
                if (Date.now() - parsedData.timestamp < 24 * 60 * 60 * 1000) {
                    Object.keys(parsedData.data).forEach(key => {
                        const field = document.querySelector(`[name="${key}"]`);
                        if (field) {
                            field.value = parsedData.data[key];
                        }
                    });
                    
                    this.currentStep = parsedData.step || 1;
                }
            }
        },
        
        saveProgress() {
            this.saveFormData();
        },
        
        clearSavedData() {
            localStorage.removeItem('form_autosave_{{ $action }}');
        },
        
        setupValidation() {
            // Real-time validation setup
            const form = document.querySelector('form');
            const inputs = form.querySelectorAll('input, textarea, select');
            
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });
        },
        
        validateField(field) {
            if (field.hasAttribute('required') && !field.value.trim()) {
                this.showFieldError(field.name, 'هذا الحقل مطلوب');
            } else if (field.type === 'email' && field.value && !this.isValidEmail(field.value)) {
                this.showFieldError(field.name, 'يرجى إدخال بريد إلكتروني صحيح');
            } else {
                this.clearFieldError(field.name);
            }
        },
        
        isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
    }
}
</script>

<style>
.advanced-form-container .bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.step-progress .step-item {
    text-align: center;
    position: relative;
}

.step-progress .step-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #e9ecef;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 10px;
    transition: all 0.3s ease;
}

.step-progress .step-item.active .step-circle {
    background: #667eea;
    color: white;
}

.step-progress .step-item.completed .step-circle {
    background: #28a745;
    color: white;
}

.step-progress .step-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.step-progress .step-item.active .step-label {
    color: #667eea;
    font-weight: 600;
}

.step-content {
    min-height: 300px;
}

.invalid-feedback {
    display: block;
}
</style>
