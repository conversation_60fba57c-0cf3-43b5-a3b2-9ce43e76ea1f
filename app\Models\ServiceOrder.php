<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'service_type',
        'service_name',
        'description',
        'budget',
        'deadline',
        'requirements',
        'contact_method',
        'status',
        'progress',
        'rating',
        'review',
        'rated_at',
        'files',
        'admin_notes',
    ];

    protected $casts = [
        'deadline' => 'date',
        'rated_at' => 'datetime',
        'files' => 'array',
        'budget' => 'decimal:2',
    ];

    /**
     * Get the user that owns the service order.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'in_progress' => 'primary',
            'completed' => 'success',
            'cancelled' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Get the status text in Arabic
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'pending' => 'في الانتظار',
            'in_progress' => 'قيد التنفيذ',
            'completed' => 'مكتمل',
            'cancelled' => 'ملغي',
            default => 'غير محدد'
        };
    }

    /**
     * Get the service type text in Arabic
     */
    public function getServiceTypeTextAttribute(): string
    {
        return match($this->service_type) {
            'web_development' => 'تطوير المواقع',
            'mobile_app' => 'تطوير التطبيقات',
            'graphic_design' => 'التصميم الجرافيكي',
            'digital_marketing' => 'التسويق الرقمي',
            default => 'خدمة أخرى'
        };
    }
}
