<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BankTransfer extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_id',
        'full_name',
        'transfer_type',
        'amount',
        'service_type',
        'status',
        'reference_number',
        'admin_notes',
        'confirmed_at',
        'confirmed_by',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'confirmed_at' => 'datetime',
    ];

    /**
     * Get the status badge color
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->status) {
            'pending' => 'warning',
            'confirmed' => 'success',
            'rejected' => 'danger',
            default => 'secondary'
        };
    }

    /**
     * Get the status text in Arabic
     */
    public function getStatusTextAttribute(): string
    {
        return match($this->status) {
            'pending' => 'في الانتظار',
            'confirmed' => 'مؤكد',
            'rejected' => 'مرفوض',
            default => 'غير محدد'
        };
    }

    /**
     * Get the transfer type text in Arabic
     */
    public function getTransferTypeTextAttribute(): string
    {
        return match($this->transfer_type) {
            'تحويل' => 'تحويل بنكي',
            'ايداع' => 'إيداع نقدي',
            default => $this->transfer_type
        };
    }
}
