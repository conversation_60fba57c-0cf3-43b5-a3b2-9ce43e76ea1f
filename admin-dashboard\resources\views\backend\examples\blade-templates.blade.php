@extends('backend.layouts.app')

@section('title', 'قوالب Blade المتقدمة')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">💠 قوالب Blade المتقدمة</h1>
                    <p class="text-muted">مجموعة شاملة من القوالب الجاهزة للوحة التحكم الإدارية</p>
                </div>
                <div>
                    <button class="btn btn-primary" onclick="AdvancedModal.show('exampleModal')">
                        <i class="fas fa-plus me-2"></i>
                        مثال على المودال
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Stats Cards Example -->
    <div class="row mb-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-chart-bar me-2 text-primary"></i>
                البطاقات الإحصائية
            </h4>
            
            <x-backend.stats-cards 
                :cards="[
                    [
                        'title' => 'إجمالي المستخدمين',
                        'value' => 1250,
                        'previous_value' => 1180,
                        'change' => 5.9,
                        'change_period' => 'من الشهر الماضي',
                        'icon' => 'fas fa-users',
                        'gradient' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                        'action_url' => route('admin.users.index'),
                        'action_text' => 'إدارة المستخدمين',
                        'chart_data' => [
                            'labels' => ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو'],
                            'values' => [1000, 1050, 1100, 1180, 1250]
                        ],
                        'additional_info' => 'نمو مستمر في قاعدة المستخدمين'
                    ],
                    [
                        'title' => 'الطلبات الجديدة',
                        'value' => 89,
                        'previous_value' => 76,
                        'change' => 17.1,
                        'change_period' => 'من الأسبوع الماضي',
                        'icon' => 'fas fa-shopping-cart',
                        'gradient' => 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
                        'action_url' => '#',
                        'action_text' => 'عرض الطلبات'
                    ],
                    [
                        'title' => 'إجمالي المبيعات',
                        'value' => 45680,
                        'previous_value' => 42300,
                        'change' => 8.0,
                        'change_period' => 'من الشهر الماضي',
                        'icon' => 'fas fa-dollar-sign',
                        'gradient' => 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
                        'action_url' => '#',
                        'action_text' => 'تقرير المبيعات'
                    ],
                    [
                        'title' => 'معدل التحويل',
                        'value' => 3.2,
                        'previous_value' => 2.8,
                        'change' => 14.3,
                        'change_period' => 'من الشهر الماضي',
                        'icon' => 'fas fa-percentage',
                        'gradient' => 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
                        'action_url' => '#',
                        'action_text' => 'تحليل التحويل'
                    ]
                ]"
                :animated="true"
                :realtime="true"
                :chart-enabled="true"
                :compare-enabled="true" />
        </div>
    </div>

    <!-- Data Table Example -->
    <div class="row mb-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-table me-2 text-success"></i>
                جدول البيانات التفاعلي
            </h4>
            
            <x-backend.data-table 
                title="إدارة المستخدمين"
                :columns="[
                    ['key' => 'id', 'label' => 'الرقم'],
                    ['key' => 'name', 'label' => 'الاسم'],
                    ['key' => 'email', 'label' => 'البريد الإلكتروني'],
                    ['key' => 'status', 'label' => 'الحالة', 'type' => 'badge'],
                    ['key' => 'created_at', 'label' => 'تاريخ التسجيل', 'type' => 'date']
                ]"
                :data="[
                    ['id' => 1, 'name' => 'أحمد محمد', 'email' => '<EMAIL>', 'status' => 'active', 'created_at' => '2024-01-15'],
                    ['id' => 2, 'name' => 'فاطمة علي', 'email' => '<EMAIL>', 'status' => 'active', 'created_at' => '2024-01-14'],
                    ['id' => 3, 'name' => 'محمد حسن', 'email' => '<EMAIL>', 'status' => 'inactive', 'created_at' => '2024-01-13'],
                    ['id' => 4, 'name' => 'عائشة سالم', 'email' => '<EMAIL>', 'status' => 'pending', 'created_at' => '2024-01-12'],
                    ['id' => 5, 'name' => 'عبدالله أحمد', 'email' => '<EMAIL>', 'status' => 'active', 'created_at' => '2024-01-11']
                ]"
                :searchable="true"
                :sortable="true"
                :filterable="true"
                :exportable="true"
                :pagination="true"
                :actions="true"
                :bulk-actions="true"
                create-route="#"
                edit-route="#"
                delete-route="#"
                view-route="#" />
        </div>
    </div>

    <!-- Advanced Form Example -->
    <div class="row mb-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-edit me-2 text-warning"></i>
                النموذج المتقدم متعدد الخطوات
            </h4>
            
            <x-backend.advanced-form 
                title="إضافة مستخدم جديد"
                action="#"
                method="POST"
                submit-text="حفظ المستخدم"
                cancel-route="#"
                :steps="[
                    ['title' => 'المعلومات الأساسية', 'icon' => 'fa-user', 'description' => 'أدخل المعلومات الشخصية للمستخدم'],
                    ['title' => 'معلومات الحساب', 'icon' => 'fa-key', 'description' => 'إعداد بيانات تسجيل الدخول'],
                    ['title' => 'الصلاحيات', 'icon' => 'fa-shield-alt', 'description' => 'تحديد صلاحيات المستخدم'],
                    ['title' => 'المراجعة', 'icon' => 'fa-check', 'description' => 'مراجعة البيانات قبل الحفظ']
                ]"
                :validation="true"
                :autosave="true"
                :confirm-before-submit="true">
                
                <x-slot name="step1">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الأول <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="first_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">الاسم الأخير <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" name="last_name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" name="phone">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تاريخ الميلاد</label>
                                <input type="date" class="form-control" name="birth_date">
                            </div>
                        </div>
                    </div>
                </x-slot>
                
                <x-slot name="step2">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                <input type="password" class="form-control" name="password_confirmation" required>
                            </div>
                        </div>
                    </div>
                </x-slot>
                
                <x-slot name="step3">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">الدور</label>
                                <select class="form-select" name="role">
                                    <option value="user">مستخدم عادي</option>
                                    <option value="admin">مدير</option>
                                    <option value="moderator">مشرف</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-12">
                            <div class="mb-3">
                                <label class="form-label">الصلاحيات</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions[]" value="read" id="perm_read">
                                    <label class="form-check-label" for="perm_read">قراءة</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions[]" value="write" id="perm_write">
                                    <label class="form-check-label" for="perm_write">كتابة</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="permissions[]" value="delete" id="perm_delete">
                                    <label class="form-check-label" for="perm_delete">حذف</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </x-slot>
                
                <x-slot name="step4">
                    <div class="alert alert-info">
                        <h6 class="alert-heading">مراجعة البيانات</h6>
                        <p>يرجى مراجعة جميع البيانات المدخلة قبل الحفظ النهائي.</p>
                    </div>
                    <div class="review-data">
                        <!-- سيتم ملء هذا القسم بـ JavaScript -->
                    </div>
                </x-slot>
            </x-backend.advanced-form>
        </div>
    </div>

    <!-- Notifications Example -->
    <div class="row mb-5">
        <div class="col-12">
            <h4 class="mb-3">
                <i class="fas fa-bell me-2 text-info"></i>
                أمثلة على الإشعارات
            </h4>
            
            <div class="card">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <button class="btn btn-success w-100" onclick="showSuccessNotification()">
                                <i class="fas fa-check me-2"></i>
                                إشعار نجاح
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-danger w-100" onclick="showErrorNotification()">
                                <i class="fas fa-times me-2"></i>
                                إشعار خطأ
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-warning w-100" onclick="showWarningNotification()">
                                <i class="fas fa-exclamation me-2"></i>
                                إشعار تحذير
                            </button>
                        </div>
                        <div class="col-md-3">
                            <button class="btn btn-info w-100" onclick="showInfoNotification()">
                                <i class="fas fa-info me-2"></i>
                                إشعار معلومات
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Example Modal -->
<x-backend.advanced-modal 
    id="exampleModal"
    title="مثال على المودال المتقدم"
    size="modal-lg"
    :centered="true"
    :scrollable="true"
    :steps="[
        ['title' => 'الخطوة الأولى', 'description' => 'هذه هي الخطوة الأولى من المودال'],
        ['title' => 'الخطوة الثانية', 'description' => 'هذه هي الخطوة الثانية'],
        ['title' => 'الخطوة الأخيرة', 'description' => 'هذه هي الخطوة الأخيرة']
    ]"
    :draggable="true"
    confirm-text="إنهاء"
    cancel-text="إلغاء">
    
    <x-slot name="step1">
        <p>محتوى الخطوة الأولى من المودال. يمكنك إضافة أي محتوى هنا.</p>
        <div class="mb-3">
            <label class="form-label">مثال على حقل إدخال</label>
            <input type="text" class="form-control" placeholder="أدخل النص هنا">
        </div>
    </x-slot>
    
    <x-slot name="step2">
        <p>محتوى الخطوة الثانية. يمكن أن تحتوي على نماذج أو أي عناصر أخرى.</p>
        <div class="mb-3">
            <label class="form-label">اختر خيار</label>
            <select class="form-select">
                <option>الخيار الأول</option>
                <option>الخيار الثاني</option>
                <option>الخيار الثالث</option>
            </select>
        </div>
    </x-slot>
    
    <x-slot name="step3">
        <div class="alert alert-success">
            <h6 class="alert-heading">تم بنجاح!</h6>
            <p>هذه هي الخطوة الأخيرة. يمكنك الآن إنهاء العملية.</p>
        </div>
    </x-slot>
</x-backend.advanced-modal>

<script>
// Notification Examples
function showSuccessNotification() {
    NotificationSystem.success('تم حفظ البيانات بنجاح!', 'عملية ناجحة', {
        duration: 3000,
        actions: [
            { text: 'عرض', class: 'btn-outline-success', href: '#' },
            { text: 'تراجع', class: 'btn-outline-secondary', onclick: 'console.log("تراجع")' }
        ]
    });
}

function showErrorNotification() {
    NotificationSystem.error('حدث خطأ أثناء معالجة الطلب!', 'خطأ في النظام', {
        duration: 5000,
        persistent: true
    });
}

function showWarningNotification() {
    NotificationSystem.warning('يرجى مراجعة البيانات المدخلة!', 'تحذير', {
        duration: 4000
    });
}

function showInfoNotification() {
    NotificationSystem.info('تم تحديث النظام إلى الإصدار الجديد.', 'معلومة مهمة', {
        duration: 6000,
        actions: [
            { text: 'اقرأ المزيد', class: 'btn-outline-info', href: '#' }
        ]
    });
}
</script>
@endsection

@push('styles')
<style>
.page-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    border-radius: 15px;
}

.section-title {
    border-bottom: 3px solid #667eea;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize any additional functionality here
    console.log('قوالب Blade المتقدمة تم تحميلها بنجاح!');
});
</script>
@endpush
