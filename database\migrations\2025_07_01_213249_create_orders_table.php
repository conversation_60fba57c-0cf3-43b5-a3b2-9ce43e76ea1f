<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('orders', function (Blueprint $table) {
            $table->id();
            $table->string('order_number')->unique();
            $table->string('customer_name');
            $table->string('customer_email');
            $table->string('customer_phone')->nullable();
            $table->enum('service_type', ['hosting', 'domain', 'email', 'ssl']);
            $table->foreignId('hosting_plan_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('hosting_provider_id')->nullable()->constrained()->onDelete('set null');
            $table->string('domain_name')->nullable();
            $table->json('order_details'); // Detailed order information
            $table->decimal('subtotal', 10, 2);
            $table->decimal('tax_amount', 10, 2)->default(0);
            $table->decimal('total_amount', 10, 2);
            $table->enum('status', ['pending', 'processing', 'active', 'suspended', 'cancelled', 'completed']);
            $table->enum('payment_status', ['pending', 'paid', 'failed', 'refunded']);
            $table->string('payment_method')->nullable();
            $table->string('transaction_id')->nullable();
            $table->string('provider_order_id')->nullable(); // Order ID from hosting provider
            $table->json('provider_response')->nullable(); // Response from hosting provider API
            $table->date('service_start_date')->nullable();
            $table->date('service_end_date')->nullable();
            $table->text('admin_notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('orders');
    }
};
