<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;

class EmailController extends Controller
{
    /**
     * Display professional email services.
     */
    public function index()
    {
        $plans = [
            [
                'id' => 'basic',
                'name' => 'البريد الأساسي',
                'price' => 25,
                'period' => 'شهرياً',
                'features' => [
                    'صندوق بريد 5 جيجا',
                    'حساب بريد واحد',
                    'دعم IMAP/POP3',
                    'حماية من الفيروسات',
                    'فلترة الرسائل المزعجة',
                    'دعم فني 24/7'
                ],
                'popular' => false
            ],
            [
                'id' => 'professional',
                'name' => 'البريد المهني',
                'price' => 45,
                'period' => 'شهرياً',
                'features' => [
                    'صندوق بريد 25 جيجا',
                    '5 حسابات بريد',
                    'دعم IMAP/POP3',
                    'حماية من الفيروسات',
                    'فلترة الرسائل المزعجة',
                    'دعم فني 24/7',
                    'تقويم مشترك',
                    'دفتر عناوين مشترك'
                ],
                'popular' => true
            ],
            [
                'id' => 'business',
                'name' => 'بريد الأعمال',
                'price' => 85,
                'period' => 'شهرياً',
                'features' => [
                    'صندوق بريد 50 جيجا',
                    '15 حساب بريد',
                    'دعم IMAP/POP3',
                    'حماية من الفيروسات',
                    'فلترة الرسائل المزعجة',
                    'دعم فني 24/7',
                    'تقويم مشترك',
                    'دفتر عناوين مشترك',
                    'مزامنة مع الهواتف الذكية',
                    'أرشفة البريد الإلكتروني'
                ],
                'popular' => false
            ]
        ];

        $features = [
            [
                'icon' => 'fas fa-shield-alt',
                'title' => 'حماية متقدمة',
                'description' => 'حماية من الفيروسات والبرمجيات الخبيثة مع فلترة متقدمة للرسائل المزعجة'
            ],
            [
                'icon' => 'fas fa-sync',
                'title' => 'مزامنة تلقائية',
                'description' => 'مزامنة البريد الإلكتروني والتقويم وجهات الاتصال عبر جميع الأجهزة'
            ],
            [
                'icon' => 'fas fa-mobile-alt',
                'title' => 'دعم الهواتف الذكية',
                'description' => 'إعداد سهل على جميع الهواتف الذكية والأجهزة اللوحية'
            ],
            [
                'icon' => 'fas fa-headset',
                'title' => 'دعم فني 24/7',
                'description' => 'فريق دعم فني متخصص متاح على مدار الساعة لمساعدتك'
            ]
        ];

        return view('email.index', compact('plans', 'features'));
    }
}
