/**
 * Browser Compatibility Script for Meta Tech Website
 * Ensures functionality across all browsers
 */

(function() {
    'use strict';

    // Browser Detection
    const BrowserDetector = {
        isIE: function() {
            return navigator.userAgent.indexOf('MSIE') !== -1 || navigator.appVersion.indexOf('Trident/') > -1;
        },
        
        isEdge: function() {
            return navigator.userAgent.indexOf('Edge') !== -1;
        },
        
        isChrome: function() {
            return navigator.userAgent.indexOf('Chrome') !== -1 && !this.isEdge();
        },
        
        isFirefox: function() {
            return navigator.userAgent.indexOf('Firefox') !== -1;
        },
        
        isSafari: function() {
            return navigator.userAgent.indexOf('Safari') !== -1 && !this.isChrome() && !this.isEdge();
        },
        
        isMobile: function() {
            return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }
    };

    // Polyfills for older browsers
    const Polyfills = {
        // Array.from polyfill
        arrayFrom: function() {
            if (!Array.from) {
                Array.from = function(arrayLike) {
                    return Array.prototype.slice.call(arrayLike);
                };
            }
        },

        // Object.assign polyfill
        objectAssign: function() {
            if (typeof Object.assign !== 'function') {
                Object.assign = function(target) {
                    if (target == null) {
                        throw new TypeError('Cannot convert undefined or null to object');
                    }
                    var to = Object(target);
                    for (var index = 1; index < arguments.length; index++) {
                        var nextSource = arguments[index];
                        if (nextSource != null) {
                            for (var nextKey in nextSource) {
                                if (Object.prototype.hasOwnProperty.call(nextSource, nextKey)) {
                                    to[nextKey] = nextSource[nextKey];
                                }
                            }
                        }
                    }
                    return to;
                };
            }
        },

        // Promise polyfill for IE
        promise: function() {
            if (typeof Promise === 'undefined') {
                // Simple Promise polyfill
                window.Promise = function(executor) {
                    var self = this;
                    self.state = 'pending';
                    self.value = undefined;
                    self.handlers = [];

                    function resolve(result) {
                        if (self.state === 'pending') {
                            self.state = 'fulfilled';
                            self.value = result;
                            self.handlers.forEach(handle);
                            self.handlers = null;
                        }
                    }

                    function reject(error) {
                        if (self.state === 'pending') {
                            self.state = 'rejected';
                            self.value = error;
                            self.handlers.forEach(handle);
                            self.handlers = null;
                        }
                    }

                    function handle(handler) {
                        if (self.state === 'pending') {
                            self.handlers.push(handler);
                        } else {
                            if (self.state === 'fulfilled' && typeof handler.onFulfilled === 'function') {
                                handler.onFulfilled(self.value);
                            }
                            if (self.state === 'rejected' && typeof handler.onRejected === 'function') {
                                handler.onRejected(self.value);
                            }
                        }
                    }

                    this.then = function(onFulfilled, onRejected) {
                        return new Promise(function(resolve, reject) {
                            handle({
                                onFulfilled: function(result) {
                                    try {
                                        resolve(onFulfilled ? onFulfilled(result) : result);
                                    } catch (ex) {
                                        reject(ex);
                                    }
                                },
                                onRejected: function(error) {
                                    try {
                                        resolve(onRejected ? onRejected(error) : error);
                                    } catch (ex) {
                                        reject(ex);
                                    }
                                }
                            });
                        });
                    };

                    executor(resolve, reject);
                };
            }
        },

        // IntersectionObserver polyfill
        intersectionObserver: function() {
            if (!('IntersectionObserver' in window)) {
                // Simple fallback for IntersectionObserver
                window.IntersectionObserver = function(callback) {
                    this.observe = function(element) {
                        // Fallback: trigger callback immediately
                        setTimeout(function() {
                            callback([{
                                target: element,
                                isIntersecting: true
                            }]);
                        }, 100);
                    };
                    
                    this.unobserve = function() {};
                    this.disconnect = function() {};
                };
            }
        },

        // CustomEvent polyfill for IE
        customEvent: function() {
            if (typeof window.CustomEvent !== 'function') {
                function CustomEvent(event, params) {
                    params = params || { bubbles: false, cancelable: false, detail: undefined };
                    var evt = document.createEvent('CustomEvent');
                    evt.initCustomEvent(event, params.bubbles, params.cancelable, params.detail);
                    return evt;
                }
                CustomEvent.prototype = window.Event.prototype;
                window.CustomEvent = CustomEvent;
            }
        }
    };

    // CSS Feature Detection and Fallbacks
    const CSSSupport = {
        supportsCSS: function(property, value) {
            var element = document.createElement('div');
            element.style[property] = value;
            return element.style[property] === value;
        },

        addFallbacks: function() {
            var html = document.documentElement;
            
            // Test for CSS Grid support
            if (!this.supportsCSS('display', 'grid')) {
                html.classList.add('no-grid');
            }
            
            // Test for Flexbox support
            if (!this.supportsCSS('display', 'flex')) {
                html.classList.add('no-flexbox');
            }
            
            // Test for CSS Variables support
            if (!this.supportsCSS('--test', 'test')) {
                html.classList.add('no-css-variables');
            }
            
            // Test for backdrop-filter support
            if (!this.supportsCSS('backdrop-filter', 'blur(10px)')) {
                html.classList.add('no-backdrop-filter');
            }
        }
    };

    // Event Handling Compatibility
    const EventCompat = {
        addEventListener: function(element, event, handler) {
            if (element.addEventListener) {
                element.addEventListener(event, handler, false);
            } else if (element.attachEvent) {
                element.attachEvent('on' + event, handler);
            }
        },

        removeEventListener: function(element, event, handler) {
            if (element.removeEventListener) {
                element.removeEventListener(event, handler, false);
            } else if (element.detachEvent) {
                element.detachEvent('on' + event, handler);
            }
        }
    };

    // Focus Management for Accessibility
    const FocusManager = {
        init: function() {
            // Add focus-visible polyfill behavior
            var hadKeyboardEvent = true;
            var keyboardThrottleTimeout = 100;
            var keyboardThrottleTimeoutID = 0;

            function onPointerDown() {
                hadKeyboardEvent = false;
            }

            function onKeyDown(e) {
                if (e.metaKey || e.altKey || e.ctrlKey) {
                    return;
                }
                hadKeyboardEvent = true;
            }

            function onFocus(e) {
                if (hadKeyboardEvent || e.target.matches(':focus-visible')) {
                    e.target.classList.add('focus-visible');
                }
            }

            function onBlur(e) {
                e.target.classList.remove('focus-visible');
            }

            EventCompat.addEventListener(document, 'keydown', onKeyDown);
            EventCompat.addEventListener(document, 'mousedown', onPointerDown);
            EventCompat.addEventListener(document, 'pointerdown', onPointerDown);
            EventCompat.addEventListener(document, 'touchstart', onPointerDown);
            EventCompat.addEventListener(document, 'focus', onFocus, true);
            EventCompat.addEventListener(document, 'blur', onBlur, true);
        }
    };

    // Initialize all compatibility features
    function init() {
        // Add browser classes to HTML element
        var html = document.documentElement;
        
        if (BrowserDetector.isIE()) html.classList.add('ie');
        if (BrowserDetector.isEdge()) html.classList.add('edge');
        if (BrowserDetector.isChrome()) html.classList.add('chrome');
        if (BrowserDetector.isFirefox()) html.classList.add('firefox');
        if (BrowserDetector.isSafari()) html.classList.add('safari');
        if (BrowserDetector.isMobile()) html.classList.add('mobile');

        // Load polyfills
        Polyfills.arrayFrom();
        Polyfills.objectAssign();
        Polyfills.promise();
        Polyfills.intersectionObserver();
        Polyfills.customEvent();

        // Add CSS fallbacks
        CSSSupport.addFallbacks();

        // Initialize focus management
        FocusManager.init();

        // IE-specific fixes
        if (BrowserDetector.isIE()) {
            // Fix for IE not supporting forEach on NodeList
            if (window.NodeList && !NodeList.prototype.forEach) {
                NodeList.prototype.forEach = Array.prototype.forEach;
            }
        }
    }

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        EventCompat.addEventListener(document, 'DOMContentLoaded', init);
    } else {
        init();
    }

    // Export for global use
    window.BrowserCompat = {
        detector: BrowserDetector,
        events: EventCompat,
        css: CSSSupport
    };

})();
