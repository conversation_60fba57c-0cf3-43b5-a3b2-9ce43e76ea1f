<?php

namespace Database\Seeders;

use App\Models\EmailProvider;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class EmailProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $providers = [
            [
                'name' => 'Zoho Mail',
                'type' => 'zoho',
                'api_endpoint' => 'https://mail.zoho.com/api/',
                'api_key' => 'your_zoho_api_key',
                'api_secret' => 'your_zoho_api_secret',
                'api_config' => [
                    'client_id' => 'your_zoho_client_id',
                    'client_secret' => 'your_zoho_client_secret',
                    'refresh_token' => 'your_zoho_refresh_token',
                ],
                'white_label_url' => 'https://mail.zoho.com',
                'commission_rate' => 20.00,
                'supported_features' => [
                    'email_hosting',
                    'calendar',
                    'contacts',
                    'notes',
                    'tasks',
                    'mobile_sync',
                    'pop_imap',
                    'activesync',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Google Workspace',
                'type' => 'google',
                'api_endpoint' => 'https://admin.googleapis.com/',
                'api_key' => 'your_google_api_key',
                'api_secret' => 'your_google_api_secret',
                'api_config' => [
                    'client_id' => 'your_google_client_id',
                    'client_secret' => 'your_google_client_secret',
                    'service_account_key' => 'path_to_service_account_json',
                ],
                'white_label_url' => 'https://workspace.google.com',
                'commission_rate' => 15.00,
                'supported_features' => [
                    'gmail',
                    'google_drive',
                    'google_meet',
                    'google_calendar',
                    'google_docs',
                    'google_sheets',
                    'google_slides',
                    'admin_console',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Microsoft 365',
                'type' => 'microsoft',
                'api_endpoint' => 'https://graph.microsoft.com/',
                'api_key' => 'your_microsoft_api_key',
                'api_secret' => 'your_microsoft_api_secret',
                'api_config' => [
                    'tenant_id' => 'your_microsoft_tenant_id',
                    'client_id' => 'your_microsoft_client_id',
                    'client_secret' => 'your_microsoft_client_secret',
                ],
                'white_label_url' => 'https://admin.microsoft.com',
                'commission_rate' => 18.00,
                'supported_features' => [
                    'outlook',
                    'onedrive',
                    'teams',
                    'word_online',
                    'excel_online',
                    'powerpoint_online',
                    'sharepoint',
                    'admin_center',
                ],
                'is_active' => true,
            ],
            [
                'name' => 'Titan Mail',
                'type' => 'titan',
                'api_endpoint' => 'https://api.titan.email/',
                'api_key' => 'your_titan_api_key',
                'api_secret' => 'your_titan_api_secret',
                'api_config' => [
                    'reseller_id' => 'your_titan_reseller_id',
                    'domain_provider' => 'namecheap',
                ],
                'white_label_url' => 'https://titan.email',
                'commission_rate' => 25.00,
                'supported_features' => [
                    'email_hosting',
                    'calendar',
                    'contacts',
                    'mobile_app',
                    'pop_imap',
                    'smtp',
                    'webmail',
                    'arabic_support',
                ],
                'is_active' => true,
            ],
        ];

        foreach ($providers as $provider) {
            EmailProvider::updateOrCreate(
                ['type' => $provider['type']],
                $provider
            );
        }
    }
}
