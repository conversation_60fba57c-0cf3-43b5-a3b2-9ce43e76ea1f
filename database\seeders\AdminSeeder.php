<?php

namespace Database\Seeders;

use App\Models\Admin;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class AdminSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Create default admin users
        $admins = [
            [
                'name' => 'مدير ميتاء تك',
                'email' => '<EMAIL>',
                'password' => Hash::make('123456'),
                'is_active' => true,
            ],
            [
                'name' => 'محمد عبد المنعم الأحمدي',
                'email' => '<EMAIL>',
                'password' => Hash::make('MetaTech2025!'),
                'is_active' => true,
            ],
            [
                'name' => 'مدير النظام الفرعي',
                'email' => '<EMAIL>',
                'password' => Hash::make('System123!'),
                'is_active' => true,
            ]
        ];

        foreach ($admins as $adminData) {
            Admin::firstOrCreate([
                'email' => $adminData['email']
            ], $adminData);
        }

        $this->command->info('تم إنشاء المدير الافتراضي بنجاح!');
        $this->command->info('البريد الإلكتروني: <EMAIL>');
        $this->command->info('كلمة المرور: 123456');
    }
}
