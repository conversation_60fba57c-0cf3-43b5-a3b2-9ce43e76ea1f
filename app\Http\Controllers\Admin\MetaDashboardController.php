<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use App\Models\Order;
use App\Models\Contact;
use App\Models\Portfolio;
use App\Models\UserDomain;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class MetaDashboardController extends Controller
{
    /**
     * عرض لوحة التحكم الحديثة
     */
    public function index()
    {
        try {
            // إحصائيات المؤشرات الرئيسية
            $stats = $this->getMainStats();

            // بيانات الرسوم البيانية
            $chartData = $this->getChartData();

            // بيانات الحسابات
            $accounts = $this->getAccountsData();

            return view('admin.meta-dashboard', compact('stats', 'chartData', 'accounts'));
        } catch (\Exception $e) {
            // في حالة وجود خطأ، عرض بيانات افتراضية
            $stats = $this->getDefaultStats();
            $chartData = $this->getDefaultChartData();
            $accounts = $this->getDefaultAccountsData();

            return view('admin.meta-dashboard', compact('stats', 'chartData', 'accounts'));
        }
    }

    /**
     * الحصول على الإحصائيات الرئيسية
     */
    private function getMainStats()
    {
        $currentMonth = Carbon::now()->startOfMonth();
        $lastMonth = Carbon::now()->subMonth()->startOfMonth();
        
        // حساب الإحصائيات
        $totalUsers = User::count();
        $totalOrders = Order::count();
        $totalRevenue = Order::where('status', 'completed')->sum('total_amount') ?? 0;
        $totalExpenses = $totalRevenue * 0.3; // افتراض أن المصاريف 30% من الإيرادات
        
        // حساب النمو الشهري
        $lastMonthUsers = User::where('created_at', '<', $currentMonth)->count();
        $currentMonthUsers = User::where('created_at', '>=', $currentMonth)->count();
        $userGrowth = $lastMonthUsers > 0 ? (($currentMonthUsers / $lastMonthUsers) * 100) - 100 : 0;
        
        $lastMonthRevenue = Order::where('status', 'completed')
            ->where('created_at', '<', $currentMonth)
            ->sum('total_amount') ?? 0;
        $currentMonthRevenue = Order::where('status', 'completed')
            ->where('created_at', '>=', $currentMonth)
            ->sum('total_amount') ?? 0;
        $revenueGrowth = $lastMonthRevenue > 0 ? (($currentMonthRevenue / $lastMonthRevenue) * 100) - 100 : 0;

        return [
            'btc_balance' => [
                'value' => '$984',
                'change' => '+45',
                'period' => 'هذا الأسبوع'
            ],
            'current_earnings' => [
                'value' => '$' . number_format($totalRevenue, 0),
                'change' => $revenueGrowth > 0 ? '+' . round($revenueGrowth, 1) : round($revenueGrowth, 1),
                'period' => 'هذا الشهر'
            ],
            'digital_assets' => [
                'value' => '$168,331.09',
                'change' => '-45',
                'period' => 'إجمالي'
            ],
            'weekly_expenses' => [
                'value' => '$' . number_format($totalExpenses, 0),
                'change' => '-15',
                'period' => 'هذا الأسبوع'
            ]
        ];
    }

    /**
     * الحصول على بيانات الرسوم البيانية
     */
    private function getChartData()
    {
        // بيانات الإحصائيات الحالية (دونات)
        $totalRevenue = Order::where('status', 'completed')->sum('total_amount') ?? 100000;
        $currentStats = [
            'income' => $totalRevenue * 0.6,
            'expenses' => $totalRevenue * 0.2,
            'installments' => $totalRevenue * 0.15,
            'investments' => $totalRevenue * 0.05
        ];

        // بيانات السوق (خطي)
        $marketData = [
            'ETH' => [650, 700, 680, 720, 740, 760, 748, 780, 790, 800],
            'XMR' => [450, 480, 460, 490, 510, 520, 500, 530, 540, 550],
            'LTC' => [120, 130, 125, 140, 145, 150, 148, 155, 160, 165],
            'XRP' => [0.8, 0.9, 0.85, 0.95, 1.0, 1.1, 1.05, 1.15, 1.2, 1.25]
        ];

        return [
            'current_stats' => $currentStats,
            'market_data' => $marketData
        ];
    }

    /**
     * الحصول على بيانات الحسابات
     */
    private function getAccountsData()
    {
        $totalRevenue = Order::where('status', 'completed')->sum('total_amount') ?? 100000;
        
        return [
            [
                'type' => 'الرصيد الأساسي',
                'amount' => '$' . number_format($totalRevenue * 0.4, 2),
                'gradient' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'holder' => 'محمد عبد المنعم',
                'expiry' => '04/26'
            ],
            [
                'type' => 'الرصيد الثانوي',
                'amount' => '$' . number_format($totalRevenue * 0.3, 2),
                'gradient' => 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                'holder' => 'محمد عبد المنعم',
                'expiry' => '04/26'
            ],
            [
                'type' => 'الرصيد الثالث',
                'amount' => '$' . number_format($totalRevenue * 0.2, 2),
                'gradient' => 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                'holder' => 'محمد عبد المنعم',
                'expiry' => '04/26'
            ],
            [
                'type' => 'الرصيد الرابع',
                'amount' => '$' . number_format($totalRevenue * 0.1, 2),
                'gradient' => 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                'holder' => 'محمد عبد المنعم',
                'expiry' => '08/26'
            ]
        ];
    }

    /**
     * الحصول على البيانات عبر API
     */
    public function getData(Request $request)
    {
        $type = $request->get('type', 'all');
        
        switch ($type) {
            case 'stats':
                return response()->json($this->getMainStats());
            case 'charts':
                return response()->json($this->getChartData());
            case 'accounts':
                return response()->json($this->getAccountsData());
            default:
                return response()->json([
                    'stats' => $this->getMainStats(),
                    'charts' => $this->getChartData(),
                    'accounts' => $this->getAccountsData()
                ]);
        }
    }

    /**
     * تحديث البيانات في الوقت الفعلي
     */
    public function realTimeUpdate()
    {
        // محاكاة تحديث البيانات
        $stats = $this->getMainStats();
        
        // إضافة تغييرات عشوائية صغيرة
        foreach ($stats as $key => &$stat) {
            if (isset($stat['change'])) {
                $currentChange = (float) str_replace(['+', '%'], '', $stat['change']);
                $randomChange = rand(-2, 2);
                $newChange = $currentChange + $randomChange;
                $stat['change'] = ($newChange >= 0 ? '+' : '') . $newChange;
            }
        }
        
        return response()->json([
            'success' => true,
            'data' => $stats,
            'timestamp' => now()->toISOString()
        ]);
    }

    /**
     * الحصول على إحصائيات افتراضية في حالة الخطأ
     */
    private function getDefaultStats()
    {
        return [
            'btc_balance' => [
                'value' => '$984',
                'change' => '+45',
                'period' => 'هذا الأسبوع'
            ],
            'current_earnings' => [
                'value' => '$22,567',
                'change' => '+45',
                'period' => 'هذا الشهر'
            ],
            'digital_assets' => [
                'value' => '$168,331.09',
                'change' => '-45',
                'period' => 'إجمالي'
            ],
            'weekly_expenses' => [
                'value' => '$7,784',
                'change' => '-15',
                'period' => 'هذا الأسبوع'
            ]
        ];
    }

    /**
     * الحصول على بيانات رسوم بيانية افتراضية
     */
    private function getDefaultChartData()
    {
        return [
            'current_stats' => [
                'income' => 167884.21,
                'expenses' => 56411.33,
                'installments' => 81981.22,
                'investments' => 12432.51
            ],
            'market_data' => [
                'ETH' => [650, 700, 680, 720, 740, 760, 748, 780, 790, 800],
                'XMR' => [450, 480, 460, 490, 510, 520, 500, 530, 540, 550],
                'LTC' => [120, 130, 125, 140, 145, 150, 148, 155, 160, 165],
                'XRP' => [0.8, 0.9, 0.85, 0.95, 1.0, 1.1, 1.05, 1.15, 1.2, 1.25]
            ]
        ];
    }

    /**
     * الحصول على بيانات حسابات افتراضية
     */
    private function getDefaultAccountsData()
    {
        return [
            [
                'type' => 'الرصيد الأساسي',
                'amount' => '$22,466.24',
                'gradient' => 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                'holder' => 'محمد عبد المنعم',
                'expiry' => '04/26'
            ],
            [
                'type' => 'الرصيد الثانوي',
                'amount' => '$67,876.32',
                'gradient' => 'linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)',
                'holder' => 'محمد عبد المنعم',
                'expiry' => '04/26'
            ],
            [
                'type' => 'الرصيد الثالث',
                'amount' => '$240.56',
                'gradient' => 'linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%)',
                'holder' => 'محمد عبد المنعم',
                'expiry' => '04/26'
            ],
            [
                'type' => 'الرصيد الرابع',
                'amount' => '$5,786.25',
                'gradient' => 'linear-gradient(135deg, #f59e0b 0%, #d97706 100%)',
                'holder' => 'محمد عبد المنعم',
                'expiry' => '08/26'
            ]
        ];
    }
}
