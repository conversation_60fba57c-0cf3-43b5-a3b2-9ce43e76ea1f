<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use App\Domains\Auth\Models\User;
use Carbon\Carbon;

class NotificationsController extends Controller
{
    /**
     * Display the notifications page.
     */
    public function index()
    {
        return view('backend.notifications.index');
    }

    /**
     * Create a new notification.
     */
    public function create(Request $request)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'type' => 'required|in:info,success,warning,error',
                'recipient_type' => 'required|in:all,role,specific,new',
                'send_time' => 'required|in:now,scheduled',
                'scheduled_at' => 'required_if:send_time,scheduled|nullable|date|after:now'
            ]);

            $notificationData = [
                'title' => $request->input('title'),
                'content' => $request->input('content'),
                'type' => $request->input('type'),
                'recipient_type' => $request->input('recipient_type'),
                'roles' => $request->input('roles', []),
                'users' => $request->input('users', []),
                'action_url' => $request->input('action_url'),
                'action_text' => $request->input('action_text'),
                'email_notification' => $request->boolean('email_notification'),
                'sms_notification' => $request->boolean('sms_notification'),
                'send_time' => $request->input('send_time'),
                'scheduled_at' => $request->input('scheduled_at'),
                'created_by' => auth()->id(),
                'created_at' => now()
            ];

            if ($notificationData['send_time'] === 'now') {
                $this->sendNotification($notificationData);
                $message = 'تم إرسال الإشعار بنجاح';
            } else {
                $this->scheduleNotification($notificationData);
                $message = 'تم جدولة الإشعار بنجاح';
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            Log::error('Notification creation failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء الإشعار: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send notification immediately.
     */
    public function send(Request $request)
    {
        try {
            $notificationId = $request->input('notification_id');
            
            // Get notification data from database
            $notificationData = $this->getNotificationData($notificationId);
            
            $this->sendNotification($notificationData);

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال الإشعار بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('Notification sending failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الإشعار: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Broadcast notification to all users.
     */
    public function broadcast(Request $request)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'type' => 'required|in:info,success,warning,error'
            ]);

            $notificationData = [
                'title' => $request->input('title'),
                'content' => $request->input('content'),
                'type' => $request->input('type'),
                'recipient_type' => 'all',
                'send_time' => 'now',
                'created_by' => auth()->id(),
                'created_at' => now()
            ];

            $this->sendNotification($notificationData);

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال الإشعار الجماعي بنجاح'
            ]);

        } catch (\Exception $e) {
            Log::error('Broadcast notification failed: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الإشعار الجماعي: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * View notification details.
     */
    public function view($notification)
    {
        try {
            $notificationData = $this->getNotificationData($notification);

            return response()->json([
                'success' => true,
                'notification' => $notificationData
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'الإشعار غير موجود'
            ], 404);
        }
    }

    /**
     * Edit notification.
     */
    public function edit(Request $request, $notification)
    {
        try {
            $request->validate([
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'type' => 'required|in:info,success,warning,error'
            ]);

            // Update notification in database
            $this->updateNotificationData($notification, $request->all());

            return response()->json([
                'success' => true,
                'message' => 'تم تحديث الإشعار بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تحديث الإشعار: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete notification.
     */
    public function delete($notification)
    {
        try {
            $this->deleteNotificationData($notification);

            return response()->json([
                'success' => true,
                'message' => 'تم حذف الإشعار بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء حذف الإشعار: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Duplicate notification.
     */
    public function duplicate($notification)
    {
        try {
            $originalData = $this->getNotificationData($notification);
            
            // Create duplicate with modified title
            $duplicateData = $originalData;
            $duplicateData['title'] = 'نسخة من: ' . $originalData['title'];
            $duplicateData['created_at'] = now();
            
            $newNotificationId = $this->createNotificationData($duplicateData);

            return response()->json([
                'success' => true,
                'message' => 'تم إنشاء نسخة من الإشعار بنجاح',
                'notification_id' => $newNotificationId
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء نسخ الإشعار: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Resend notification.
     */
    public function resend($notification)
    {
        try {
            $notificationData = $this->getNotificationData($notification);
            $this->sendNotification($notificationData);

            return response()->json([
                'success' => true,
                'message' => 'تم إعادة إرسال الإشعار بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إعادة إرسال الإشعار: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Bulk action on notifications.
     */
    public function bulkAction(Request $request)
    {
        try {
            $action = $request->input('action');
            $notifications = $request->input('notifications', []);

            if (empty($notifications)) {
                return response()->json([
                    'success' => false,
                    'message' => 'لم يتم تحديد أي إشعارات'
                ], 400);
            }

            switch ($action) {
                case 'delete':
                    $this->bulkDeleteNotifications($notifications);
                    $message = 'تم حذف الإشعارات المحددة بنجاح';
                    break;
                case 'resend':
                    $this->bulkResendNotifications($notifications);
                    $message = 'تم إعادة إرسال الإشعارات المحددة بنجاح';
                    break;
                default:
                    throw new \Exception('إجراء غير مدعوم');
            }

            return response()->json([
                'success' => true,
                'message' => $message
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تنفيذ الإجراء: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get notification templates.
     */
    public function templates()
    {
        $templates = [
            [
                'id' => 'welcome',
                'name' => 'رسالة ترحيب',
                'title' => 'مرحباً بك في النظام',
                'content' => 'نرحب بك في منصتنا ونتمنى لك تجربة ممتعة ومفيدة.',
                'type' => 'success'
            ],
            [
                'id' => 'system_update',
                'name' => 'تحديث النظام',
                'title' => 'تحديث النظام',
                'content' => 'سيتم تحديث النظام في الوقت المحدد. قد تواجه انقطاع مؤقت في الخدمة.',
                'type' => 'info'
            ],
            [
                'id' => 'security_alert',
                'name' => 'تنبيه أمني',
                'title' => 'تنبيه أمني',
                'content' => 'تم اكتشاف نشاط مشبوه. يرجى مراجعة إعدادات الأمان.',
                'type' => 'error'
            ]
        ];

        return response()->json([
            'success' => true,
            'templates' => $templates
        ]);
    }

    /**
     * Get notification statistics.
     */
    public function getStats()
    {
        try {
            $stats = [
                'total_notifications' => $this->getTotalNotifications(),
                'sent_notifications' => $this->getSentNotifications(),
                'pending_notifications' => $this->getPendingNotifications(),
                'read_rate' => $this->getReadRate(),
                'notifications_today' => $this->getNotificationsToday(),
                'failed_notifications' => $this->getFailedNotifications()
            ];

            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإحصائيات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Quick test notification.
     */
    public function quickTestNotification()
    {
        try {
            $notificationData = [
                'title' => 'إشعار تجريبي',
                'content' => 'هذا إشعار تجريبي لاختبار النظام.',
                'type' => 'info',
                'recipient_type' => 'specific',
                'users' => [auth()->id()],
                'send_time' => 'now',
                'created_by' => auth()->id(),
                'created_at' => now()
            ];

            $this->sendNotification($notificationData);

            return response()->json([
                'success' => true,
                'message' => 'تم إرسال الإشعار التجريبي بنجاح'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إرسال الإشعار التجريبي: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Send notification to recipients.
     */
    private function sendNotification($notificationData)
    {
        $recipients = $this->getRecipients($notificationData);

        foreach ($recipients as $recipient) {
            // Send in-app notification
            $this->sendInAppNotification($recipient, $notificationData);

            // Send email if enabled
            if ($notificationData['email_notification'] ?? false) {
                $this->sendEmailNotification($recipient, $notificationData);
            }

            // Send SMS if enabled
            if ($notificationData['sms_notification'] ?? false) {
                $this->sendSmsNotification($recipient, $notificationData);
            }
        }

        // Update notification status
        $this->updateNotificationStatus($notificationData, 'sent');
    }

    /**
     * Schedule notification for later.
     */
    private function scheduleNotification($notificationData)
    {
        // Store notification in database with scheduled status
        // This would typically be handled by a job queue
        $this->createNotificationData($notificationData);
    }

    /**
     * Get recipients based on notification settings.
     */
    private function getRecipients($notificationData)
    {
        switch ($notificationData['recipient_type']) {
            case 'all':
                return User::where('active', true)->get();
            case 'role':
                return User::whereHas('roles', function ($query) use ($notificationData) {
                    $query->whereIn('name', $notificationData['roles']);
                })->get();
            case 'specific':
                return User::whereIn('id', $notificationData['users'])->get();
            case 'new':
                return User::where('created_at', '>=', now()->subDays(7))->get();
            default:
                return collect();
        }
    }

    /**
     * Send in-app notification.
     */
    private function sendInAppNotification($user, $notificationData)
    {
        // Implementation for in-app notifications
        // This could use Laravel's notification system
    }

    /**
     * Send email notification.
     */
    private function sendEmailNotification($user, $notificationData)
    {
        try {
            Mail::raw($notificationData['content'], function ($message) use ($user, $notificationData) {
                $message->to($user->email)
                        ->subject($notificationData['title']);
            });
        } catch (\Exception $e) {
            Log::error('Email notification failed: ' . $e->getMessage());
        }
    }

    /**
     * Send SMS notification.
     */
    private function sendSmsNotification($user, $notificationData)
    {
        // Implementation for SMS notifications
        // This would integrate with SMS service provider
    }

    // Helper methods for database operations
    private function getNotificationData($id) { return []; }
    private function createNotificationData($data) { return rand(1, 1000); }
    private function updateNotificationData($id, $data) { return true; }
    private function deleteNotificationData($id) { return true; }
    private function updateNotificationStatus($data, $status) { return true; }
    private function bulkDeleteNotifications($ids) { return true; }
    private function bulkResendNotifications($ids) { return true; }

    // Statistics methods
    private function getTotalNotifications() { return rand(1000, 2000); }
    private function getSentNotifications() { return rand(800, 1500); }
    private function getPendingNotifications() { return rand(50, 200); }
    private function getReadRate() { return rand(70, 95); }
    private function getNotificationsToday() { return rand(10, 50); }
    private function getFailedNotifications() { return rand(0, 10); }
}
