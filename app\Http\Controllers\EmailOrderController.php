<?php

namespace App\Http\Controllers;

use App\Models\EmailPlan;
use App\Models\EmailProvider;
use App\Models\Order;
use App\Services\EmailProviderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class EmailOrderController extends Controller
{
    /**
     * Display email plans
     */
    public function index()
    {
        $plans = EmailPlan::where('is_active', true)
            ->orderBy('sort_order')
            ->orderBy('monthly_price')
            ->get();

        return view('email.index', compact('plans'));
    }

    /**
     * Show order form for specific email plan
     */
    public function showOrderForm(EmailPlan $plan)
    {
        return view('email.order', compact('plan'));
    }

    /**
     * Process email order
     */
    public function processOrder(Request $request)
    {
        $request->validate([
            'email_plan_id' => 'required|exists:email_plans,id',
            'domain_name' => 'required|string|max:255',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
            'billing_cycle' => 'required|in:monthly,yearly',
            'payment_method' => 'required|in:credit_card,bank_transfer,paypal',
            'email_users_count' => 'required|integer|min:1|max:100',
            'email_users' => 'required|array|min:1',
            'email_users.*.name' => 'required|string|max:255',
            'email_users.*.email' => 'required|string|max:255',
        ]);

        try {
            DB::beginTransaction();

            $plan = EmailPlan::findOrFail($request->email_plan_id);

            // Validate user count against plan limits
            if ($request->email_users_count > $plan->max_users && $plan->max_users > 0) {
                return back()->withErrors(['email_users_count' => 'عدد المستخدمين يتجاوز الحد المسموح للخطة']);
            }

            // Calculate pricing
            $basePrice = $request->billing_cycle === 'yearly' ? $plan->yearly_price : $plan->monthly_price;
            $totalPrice = $basePrice * $request->email_users_count;
            $taxAmount = $totalPrice * 0.15; // 15% VAT
            $finalAmount = $totalPrice + $taxAmount;

            // Create order
            $order = Order::create([
                'order_number' => $this->generateOrderNumber(),
                'customer_name' => $request->customer_name,
                'customer_email' => $request->customer_email,
                'customer_phone' => $request->customer_phone,
                'service_type' => 'email',
                'email_plan_id' => $plan->id,
                'email_users_count' => $request->email_users_count,
                'email_users' => $request->email_users,
                'domain_name' => $this->sanitizeDomain($request->domain_name),
                'order_details' => [
                    'billing_cycle' => $request->billing_cycle,
                    'plan_name' => $plan->name_ar,
                    'provider' => $plan->provider,
                ],
                'subtotal' => $totalPrice,
                'tax_amount' => $taxAmount,
                'total_amount' => $finalAmount,
                'status' => 'pending',
                'payment_status' => 'pending',
                'payment_method' => $request->payment_method,
                'service_start_date' => now(),
                'service_end_date' => $request->billing_cycle === 'yearly' ? now()->addYear() : now()->addMonth(),
            ]);

            // Process payment
            $paymentResult = $this->processPayment($order, $request);

            if ($paymentResult['success']) {
                $order->update([
                    'payment_status' => 'paid',
                    'transaction_id' => $paymentResult['transaction_id'],
                ]);

                // Create email account
                $this->createEmailAccount($order);

                DB::commit();

                return redirect()->route('email.order.success', $order)
                    ->with('success', 'تم إنشاء طلب البريد الإلكتروني بنجاح!');
            } else {
                DB::rollBack();
                return back()->withErrors(['payment' => 'فشل في معالجة الدفع: ' . $paymentResult['message']]);
            }

        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Email order processing failed', [
                'error' => $e->getMessage(),
                'request' => $request->all()
            ]);

            return back()->withErrors(['error' => 'حدث خطأ أثناء معالجة الطلب: ' . $e->getMessage()]);
        }
    }

    /**
     * Show order success page
     */
    public function orderSuccess(Order $order)
    {
        return view('email.success', compact('order'));
    }

    /**
     * Create email account with provider
     */
    private function createEmailAccount(Order $order)
    {
        // Get the email provider for this plan
        $provider = EmailProvider::where('is_active', true)
            ->where('type', $order->emailPlan->provider)
            ->first();

        if (!$provider) {
            // Fallback to manual setup
            $order->update([
                'status' => 'pending',
                'admin_notes' => 'No active provider found - manual setup required',
            ]);
            return;
        }

        $order->update(['email_provider_id' => $provider->id]);

        $providerService = new EmailProviderService($provider);
        $result = $providerService->createEmailAccount($order);

        if ($result['success']) {
            $order->update([
                'status' => 'active',
                'provider_order_id' => $result['account_id'] ?? null,
                'provider_response' => $result,
            ]);

            // Send welcome email with account details
            $this->sendWelcomeEmail($order, $result);
        } else {
            $order->update([
                'status' => 'pending',
                'admin_notes' => 'Email account creation failed: ' . $result['message'],
            ]);
        }
    }

    /**
     * Process payment (integrate with your payment gateway)
     */
    private function processPayment(Order $order, Request $request)
    {
        // This is a placeholder - integrate with your actual payment gateway
        // Examples: PayTabs, Stripe, PayPal, etc.

        switch ($request->payment_method) {
            case 'credit_card':
                return $this->processCreditCardPayment($order, $request);
            case 'bank_transfer':
                return $this->processBankTransfer($order, $request);
            case 'paypal':
                return $this->processPayPalPayment($order, $request);
            default:
                return ['success' => false, 'message' => 'Unsupported payment method'];
        }
    }

    /**
     * Process credit card payment
     */
    private function processCreditCardPayment(Order $order, Request $request)
    {
        // Integrate with PayTabs, Stripe, or other credit card processor
        // For demo purposes, we'll simulate a successful payment

        return [
            'success' => true,
            'transaction_id' => 'EMAIL_TXN_' . Str::random(10),
            'message' => 'Payment processed successfully'
        ];
    }

    /**
     * Process bank transfer
     */
    private function processBankTransfer(Order $order, Request $request)
    {
        // For bank transfer, usually you'd mark as pending and wait for confirmation
        $order->update([
            'payment_status' => 'pending',
            'admin_notes' => 'Waiting for bank transfer confirmation'
        ]);

        return [
            'success' => true,
            'transaction_id' => 'EMAIL_BANK_' . $order->order_number,
            'message' => 'Bank transfer initiated'
        ];
    }

    /**
     * Process PayPal payment
     */
    private function processPayPalPayment(Order $order, Request $request)
    {
        // Integrate with PayPal API
        return [
            'success' => true,
            'transaction_id' => 'EMAIL_PP_' . Str::random(10),
            'message' => 'PayPal payment processed'
        ];
    }

    /**
     * Send welcome email with email account details
     */
    private function sendWelcomeEmail(Order $order, array $emailDetails)
    {
        // Implement email sending logic
        // You can use Laravel's Mail facade or a service like SendGrid

        Log::info('Email welcome email sent', [
            'order_id' => $order->id,
            'customer_email' => $order->customer_email,
            'provider' => $order->emailPlan->provider
        ]);
    }

    /**
     * Generate unique order number
     */
    private function generateOrderNumber()
    {
        return 'EMAIL' . date('Ymd') . Str::random(6);
    }

    /**
     * Sanitize domain name
     */
    private function sanitizeDomain($domain)
    {
        // Remove http/https and www
        $domain = preg_replace('/^https?:\/\//', '', $domain);
        $domain = preg_replace('/^www\./', '', $domain);

        return strtolower(trim($domain));
    }

    /**
     * Admin: View all email orders
     */
    public function adminIndex()
    {
        $orders = Order::with(['emailPlan', 'emailProvider'])
            ->where('service_type', 'email')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('admin.email.orders', compact('orders'));
    }

    /**
     * Admin: Manage specific email order
     */
    public function adminShow(Order $order)
    {
        return view('admin.email.order-details', compact('order'));
    }

    /**
     * Admin: Suspend email account
     */
    public function adminSuspend(Order $order)
    {
        try {
            if ($order->email_provider_id) {
                $provider = EmailProvider::find($order->email_provider_id);
                $providerService = new EmailProviderService($provider);
                $providerService->suspendAccount($order);
            }

            $order->update(['status' => 'suspended']);

            return back()->with('success', 'تم تعليق حساب البريد الإلكتروني بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'فشل في تعليق الحساب: ' . $e->getMessage()]);
        }
    }

    /**
     * Admin: Unsuspend email account
     */
    public function adminUnsuspend(Order $order)
    {
        try {
            if ($order->email_provider_id) {
                $provider = EmailProvider::find($order->email_provider_id);
                $providerService = new EmailProviderService($provider);
                $providerService->unsuspendAccount($order);
            }

            $order->update(['status' => 'active']);

            return back()->with('success', 'تم إلغاء تعليق حساب البريد الإلكتروني بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'فشل في إلغاء تعليق الحساب: ' . $e->getMessage()]);
        }
    }

    /**
     * Admin: Add email users to existing account
     */
    public function adminAddUsers(Order $order, Request $request)
    {
        $request->validate([
            'new_users' => 'required|array|min:1',
            'new_users.*.name' => 'required|string|max:255',
            'new_users.*.email' => 'required|string|max:255',
        ]);

        try {
            $currentUsers = $order->email_users ?? [];
            $newUsers = $request->new_users;
            $allUsers = array_merge($currentUsers, $newUsers);

            // Check plan limits
            if ($order->emailPlan->max_users > 0 && count($allUsers) > $order->emailPlan->max_users) {
                return back()->withErrors(['error' => 'عدد المستخدمين يتجاوز الحد المسموح للخطة']);
            }

            $order->update([
                'email_users' => $allUsers,
                'email_users_count' => count($allUsers),
            ]);

            return back()->with('success', 'تم إضافة المستخدمين بنجاح');
        } catch (\Exception $e) {
            return back()->withErrors(['error' => 'فشل في إضافة المستخدمين: ' . $e->getMessage()]);
        }
    }
}
