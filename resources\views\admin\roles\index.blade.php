@extends('layouts.admin')

@section('title', 'إدارة الأدوار والصلاحيات')

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">إدارة الأدوار والصلاحيات</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item active">الأدوار والصلاحيات</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <!-- Statistics Cards -->
            <div class="row mb-3">
                <div class="col-lg-4 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ number_format($stats['total_roles']) }}</h3>
                            <p>إجمالي الأدوار</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-user-tag"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ number_format($stats['total_permissions']) }}</h3>
                            <p>إجمالي الصلاحيات</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-key"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>{{ number_format($stats['total_admins']) }}</h3>
                            <p>إجمالي المديرين</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Roles Table -->
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة الأدوار</h3>
                    <div class="card-tools">
                        <a href="{{ route('admin.roles.create') }}" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> إضافة دور جديد
                        </a>
                        <a href="{{ route('admin.permissions.index') }}" class="btn btn-success btn-sm">
                            <i class="fas fa-key"></i> إدارة الصلاحيات
                        </a>
                    </div>
                </div>
                <div class="card-body table-responsive p-0">
                    <table class="table table-hover text-nowrap">
                        <thead>
                            <tr>
                                <th>اسم الدور</th>
                                <th>الاسم المعروض</th>
                                <th>الوصف</th>
                                <th>عدد الصلاحيات</th>
                                <th>عدد المستخدمين</th>
                                <th>تاريخ الإنشاء</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            @forelse($roles as $role)
                            <tr>
                                <td>
                                    <strong>{{ $role->name }}</strong>
                                    @if($role->name === 'Super Admin')
                                        <span class="badge badge-danger ml-1">مدير عام</span>
                                    @endif
                                </td>
                                <td>{{ $role->display_name ?? $role->name }}</td>
                                <td>{{ Str::limit($role->description ?? 'لا يوجد وصف', 50) }}</td>
                                <td>
                                    <span class="badge badge-info">{{ $role->permissions_count }}</span>
                                </td>
                                <td>
                                    <span class="badge badge-success">{{ $role->users_count }}</span>
                                </td>
                                <td>{{ $role->created_at->format('Y-m-d') }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{{ route('admin.roles.show', $role) }}" 
                                           class="btn btn-info btn-sm" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('admin.roles.edit', $role) }}" 
                                           class="btn btn-warning btn-sm" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        @if($role->name !== 'Super Admin')
                                        <button type="button" class="btn btn-danger btn-sm" 
                                                onclick="deleteRole({{ $role->id }})" title="حذف">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @empty
                            <tr>
                                <td colspan="7" class="text-center">لا توجد أدوار</td>
                            </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
                @if($roles->hasPages())
                <div class="card-footer">
                    {{ $roles->links() }}
                </div>
                @endif
            </div>

            <!-- Quick Actions -->
            <div class="row">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">إجراءات سريعة</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-6">
                                    <a href="{{ route('admin.roles.create') }}" class="btn btn-primary btn-block">
                                        <i class="fas fa-plus"></i><br>
                                        إضافة دور جديد
                                    </a>
                                </div>
                                <div class="col-6">
                                    <a href="{{ route('admin.permissions.index') }}" class="btn btn-success btn-block">
                                        <i class="fas fa-key"></i><br>
                                        إدارة الصلاحيات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">معلومات مهمة</h3>
                        </div>
                        <div class="card-body">
                            <div class="alert alert-info">
                                <h5><i class="icon fas fa-info"></i> ملاحظة!</h5>
                                <ul class="mb-0">
                                    <li>لا يمكن حذف دور "المدير العام"</li>
                                    <li>لا يمكن حذف دور مرتبط بمستخدمين</li>
                                    <li>تأكد من تعيين الصلاحيات المناسبة لكل دور</li>
                                    <li>يمكن تعديل الأدوار في أي وقت</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تأكيد الحذف</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                هل أنت متأكد من حذف هذا الدور؟ هذا الإجراء لا يمكن التراجع عنه.
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">إلغاء</button>
                <form id="deleteForm" method="POST" style="display: inline;">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn btn-danger">حذف</button>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function deleteRole(roleId) {
    $('#deleteForm').attr('action', `{{ route('admin.roles.index') }}/${roleId}`);
    $('#deleteModal').modal('show');
}
</script>
@endpush
