<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no, viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="apple-mobile-web-app-title" content="ميتاء تك">
    <meta name="msapplication-TileColor" content="#4f46e5">
    <meta name="msapplication-config" content="/browserconfig.xml">
    <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

    <title><?php echo $__env->yieldContent('title', 'ميتاء تك للبرمجة وتصميم المواقع والتطبيقات'); ?></title>
    <meta name="description" content="<?php echo $__env->yieldContent('description', 'ميتاء تك للبرمجة وتصميم المواقع والتطبيقات - شركة رائدة في مجال البرمجة وتطوير المواقع والتطبيقات والاستضافة'); ?>">
    <meta name="keywords" content="<?php echo $__env->yieldContent('keywords', 'ميتاء تك، برمجة، تصميم مواقع، تطبيقات، استضافة، دومين، تطوير ويب، اليمن، المكلا، حضرموت'); ?>">
    <meta name="author" content="ميتاء تك للبرمجة وتصميم المواقع والتطبيقات">
    <meta name="robots" content="<?php echo $__env->yieldContent('robots', 'index, follow'); ?>">
    <meta name="language" content="ar">
    <meta name="geo.region" content="YE-HD">
    <meta name="geo.placename" content="المكلا، حضرموت، اليمن">
    <meta name="geo.position" content="14.5425;49.1242">
    <meta name="ICBM" content="14.5425, 49.1242">

    <!-- Additional SEO Meta Tags -->
    <meta name="rating" content="general">
    <meta name="distribution" content="global">
    <meta name="revisit-after" content="1 days">
    <meta name="expires" content="never">
    <meta name="copyright" content="ميتاء تك للبرمجة وتصميم المواقع والتطبيقات">
    <meta name="reply-to" content="<EMAIL>">
    <meta name="owner" content="ميتاء تك للبرمجة وتصميم المواقع والتطبيقات">
    <meta name="url" content="<?php echo e(url()->current()); ?>">
    <meta name="identifier-URL" content="<?php echo e(url()->current()); ?>">
    <meta name="category" content="Technology, Web Development, Programming, Hosting">
    <meta name="coverage" content="Worldwide">
    <meta name="target" content="all">
    <meta name="HandheldFriendly" content="True">
    <meta name="MobileOptimized" content="320">
    <meta http-equiv="cleartype" content="on">
    <meta name="skype_toolbar" content="skype_toolbar_parser_compatible">
    <meta name="format-detection" content="telephone=yes">
    <meta name="format-detection" content="address=yes">
    <meta name="format-detection" content="email=yes">

    <!-- Canonical URL -->
    <link rel="canonical" href="<?php echo e(url()->current()); ?>">

    <!-- Alternate Languages -->
    <link rel="alternate" hreflang="ar" href="<?php echo e(url()->current()); ?>">
    <link rel="alternate" hreflang="ar-ye" href="<?php echo e(url()->current()); ?>">
    <link rel="alternate" hreflang="x-default" href="<?php echo e(url()->current()); ?>">

    <!-- Open Graph Tags -->
    <?php
        $ogTitle = $__env->yieldContent('og_title') ?: $__env->yieldContent('title') ?: 'ميتاء تك للبرمجة وتصميم المواقع والتطبيقات';
        $ogDescription = $__env->yieldContent('og_description') ?: $__env->yieldContent('description') ?: 'ميتاء تك للبرمجة وتصميم المواقع والتطبيقات - شركة رائدة في مجال البرمجة وتطوير المواقع والتطبيقات والاستضافة';
        $ogImage = $__env->yieldContent('og_image') ?: asset('images/zdeuj.png');
    ?>
    <meta property="og:title" content="<?php echo e($ogTitle); ?>">
    <meta property="og:description" content="<?php echo e($ogDescription); ?>">
    <meta property="og:image" content="<?php echo e($ogImage); ?>">
    <meta property="og:url" content="<?php echo e(url()->current()); ?>">
    <meta property="og:type" content="<?php echo $__env->yieldContent('og_type', 'website'); ?>">
    <meta property="og:site_name" content="ميتاء تك للبرمجة وتصميم المواقع والتطبيقات">
    <meta property="og:locale" content="ar_YE">

    <!-- Twitter Card Tags -->
    <?php
        $twitterTitle = $__env->yieldContent('twitter_title') ?: $ogTitle;
        $twitterDescription = $__env->yieldContent('twitter_description') ?: $ogDescription;
        $twitterImage = $__env->yieldContent('twitter_image') ?: $ogImage;
    ?>
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo e($twitterTitle); ?>">
    <meta name="twitter:description" content="<?php echo e($twitterDescription); ?>">
    <meta name="twitter:image" content="<?php echo e($twitterImage); ?>">

    <!-- Favicon and App Icons -->
    <link rel="icon" type="image/png" sizes="32x32" href="<?php echo e(asset('images/zdeuj.png')); ?>">
    <link rel="icon" type="image/png" sizes="16x16" href="<?php echo e(asset('images/zdeuj.png')); ?>">
    <link rel="apple-touch-icon" sizes="180x180" href="<?php echo e(asset('images/zdeuj.png')); ?>">
    <link rel="manifest" href="<?php echo e(asset('manifest.json')); ?>">
    <meta name="theme-color" content="#4f46e5">

    <!-- Fonts - Optimized Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="<?php echo e(asset('images/zdeuj.png')); ?>" as="image" type="image/png">
    <link rel="dns-prefetch" href="//cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="//unpkg.com">
    <link rel="dns-prefetch" href="//cdn.tailwindcss.com">
    <link rel="dns-prefetch" href="//fonts.googleapis.com">
    <link rel="dns-prefetch" href="//fonts.gstatic.com">

    <!-- Preload Critical CSS -->
    <link rel="preload" href="<?php echo e(asset('css/hostinger-theme.css')); ?>" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="<?php echo e(asset('css/hostinger-theme.css')); ?>"></noscript>

    <!-- Browser Compatibility CSS -->
    <link rel="stylesheet" href="<?php echo e(asset('css/browser-compatibility.css')); ?>">

    <!-- Resource Hints for Performance -->
    <link rel="prefetch" href="/services">
    <link rel="prefetch" href="/hosting">
    <link rel="prefetch" href="/contact">

    <!-- Font Awesome - Deferred Loading -->
    <link rel="preload" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"></noscript>

    <!-- AOS Animation - Deferred Loading -->
    <link rel="preload" href="https://unpkg.com/aos@2.3.1/dist/aos.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="https://unpkg.com/aos@2.3.1/dist/aos.css"></noscript>

    <!-- Tailwind CSS - Optimized -->
    <script>
        // Load Tailwind CSS asynchronously
        const tailwindScript = document.createElement('script');
        tailwindScript.src = 'https://cdn.tailwindcss.com';
        tailwindScript.async = true;
        document.head.appendChild(tailwindScript);
    </script>

    <!-- Hostinger Theme CSS - Already preloaded above -->

    <!-- Critical CSS Inline for Performance -->
    <style>
        /* Critical Above-the-Fold CSS */
        *,*::before,*::after{box-sizing:border-box}
        body{font-family:'Cairo','Tajawal',sans-serif;margin:0;direction:rtl;background:#fff;color:#1f2937}
        .navbar{background:#fff;box-shadow:0 2px 10px rgba(0,0,0,0.1);position:fixed;top:0;width:100%;z-index:1000}
        .hero-section{min-height:100vh;display:flex;align-items:center;background:linear-gradient(135deg,#f8fafc 0%,#e2e8f0 100%);padding-top:70px}
        .btn-primary{background:#8b5cf6;color:#fff;padding:14px 32px;border-radius:8px;font-weight:600;transition:all 0.3s ease}
        .gradient-text{background:linear-gradient(135deg,#8b5cf6 0%,#3b82f6 100%);-webkit-background-clip:text;-webkit-text-fill-color:transparent}
        img.lazy{opacity:0;transition:opacity 0.4s ease}
        img.loaded{opacity:1}
    </style>

    <!-- Custom Styles -->
    <style>
        :root {
            --primary-color: #8b5cf6;
            --primary-dark: #7c3aed;
            --secondary-color: #3b82f6;
            --accent-color: #06b6d4;
            --text-dark: #1f2937;
            --text-light: #6b7280;
            --bg-light: #f8fafc;
            --gradient-primary: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
            --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #6366f1 100%);
            --gradient-accent: linear-gradient(135deg, #06b6d4 0%, #0ea5e9 100%);
            --hostinger-purple: #8b5cf6;
            --hostinger-orange: #f59e0b;
        }

        /* تطبيق الخط الرسمي العربي على جميع العناصر */
        * {
            font-family: 'Cairo', 'Tajawal', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 400;
            line-height: 1.6;
            color: var(--text-dark);
        }

        /* تحسين الخطوط للعناوين */
        h1, h2, h3, h4, h5, h6 {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 600;
            line-height: 1.3;
        }

        /* تحسين الخطوط للنصوص والروابط */
        p, span, div, a, button, input, textarea, select, label {
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        /* تحسين الخطوط للأزرار */
        .btn, button {
            font-family: 'Cairo', 'Tajawal', sans-serif;
            font-weight: 600;
            letter-spacing: 0.025em;
        }

        .gradient-bg {
            background: var(--gradient-primary);
        }

        .hostinger-gradient {
            background: linear-gradient(135deg, var(--hostinger-purple) 0%, var(--hostinger-orange) 100%);
        }

        .gradient-text {
            background: var(--gradient-primary);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .hover-scale {
            transition: transform 0.3s ease;
        }

        .hover-scale:hover {
            transform: scale(1.05);
        }

        .btn-primary {
            background: var(--hostinger-purple);
            color: white;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }

        .btn-primary:hover {
            background: var(--primary-dark);
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(103, 58, 183, 0.3);
        }

        .btn-secondary {
            background: transparent;
            color: var(--hostinger-purple);
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: 2px solid var(--hostinger-purple);
            cursor: pointer;
            font-size: 16px;
        }

        .btn-secondary:hover {
            background: var(--hostinger-purple);
            color: white;
            transform: translateY(-2px);
        }

        .btn-orange {
            background: var(--hostinger-orange);
            color: white;
            padding: 14px 32px;
            border-radius: 8px;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }

        .btn-orange:hover {
            background: #e55a00;
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 105, 0, 0.3);
        }

        .card-hover {
            transition: all 0.3s ease;
            border-radius: 12px;
            overflow: hidden;
            background: white;
            border: 1px solid #e2e8f0;
        }

        .card-hover:hover {
            transform: translateY(-8px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            border-color: var(--hostinger-purple);
        }

        .section-padding {
            padding: 80px 0;
        }

        .navbar {
            background: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .navbar.scrolled {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
        }

        .hero-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: var(--hostinger-purple);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            margin-bottom: 20px;
        }

        .pricing-card {
            background: white;
            border-radius: 16px;
            padding: 40px 30px;
            text-align: center;
            border: 2px solid #e2e8f0;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .pricing-card:hover {
            border-color: var(--hostinger-purple);
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }

        .pricing-card.featured {
            border-color: var(--hostinger-purple);
            transform: scale(1.05);
        }

        .pricing-card.featured::before {
            content: 'الأكثر شعبية';
            position: absolute;
            top: 20px;
            right: -30px;
            background: var(--hostinger-orange);
            color: white;
            padding: 8px 40px;
            transform: rotate(45deg);
            font-size: 12px;
            font-weight: 600;
        }

        @media (max-width: 768px) {
            .section-padding {
                padding: 60px 0;
            }

            .hero-section {
                min-height: 80vh;
            }

            .btn-primary, .btn-secondary, .btn-orange {
                padding: 12px 24px;
                font-size: 14px;
            }
        }

        .animate-blob {
            animation: blob 7s infinite;
        }

        .animation-delay-2000 {
            animation-delay: 2s;
        }

        .animation-delay-4000 {
            animation-delay: 4s;
        }

        @keyframes  blob {
            0% {
                transform: translate(0px, 0px) scale(1);
            }
            33% {
                transform: translate(30px, -50px) scale(1.1);
            }
            66% {
                transform: translate(-20px, 20px) scale(0.9);
            }
            100% {
                transform: translate(0px, 0px) scale(1);
            }
        }

        .animate-float {
            animation: float 6s ease-in-out infinite;
        }

        .animation-delay-1000 {
            animation-delay: 1s;
        }

        .animation-delay-2000 {
            animation-delay: 2s;
        }

        .animation-delay-3000 {
            animation-delay: 3s;
        }

        @keyframes  float {
            0%, 100% {
                transform: translateY(0px);
            }
            50% {
                transform: translateY(-20px);
            }
        }

        .animate-gradient {
            background-size: 400% 400%;
            animation: gradient 15s ease infinite;
        }

        @keyframes  gradient {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        /* Enhanced Navigation Styles */
        .nav-dropdown {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .nav-dropdown::before {
            content: '';
            position: absolute;
            top: -8px;
            right: 20px;
            width: 16px;
            height: 16px;
            background: white;
            border: 1px solid rgba(0, 0, 0, 0.1);
            border-bottom: none;
            border-right: none;
            transform: rotate(45deg);
        }

        .service-card {
            transition: all 0.2s ease;
            border: 1px solid transparent;
        }

        .service-card:hover {
            border-color: rgba(103, 58, 183, 0.2);
            box-shadow: 0 4px 12px rgba(103, 58, 183, 0.1);
            transform: translateY(-1px);
        }

        .price-badge {
            background: linear-gradient(135deg, #4ade80, #22c55e);
            color: white;
            font-weight: 600;
            font-size: 0.75rem;
            padding: 2px 8px;
            border-radius: 12px;
            display: inline-block;
        }

        /* Mobile Menu Enhancements */
        @media (max-width: 1024px) {
            .mobile-menu-item {
                border-bottom: 1px solid rgba(0, 0, 0, 0.05);
                padding: 12px 0;
            }

            .mobile-menu-item:last-child {
                border-bottom: none;
            }
        }

        /* Smooth transitions for dropdowns */
        .group:hover .group-hover\\:opacity-100 {
            opacity: 1 !important;
            visibility: visible !important;
            transform: translateY(0) !important;
        }

        .group .group-hover\\:opacity-100 {
            opacity: 0;
            visibility: hidden;
            transform: translateY(-10px);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Navigation hover effects */
        .nav-item {
            position: relative;
            overflow: hidden;
        }

        .nav-item::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            width: 0;
            height: 2px;
            background: linear-gradient(135deg, var(--hostinger-purple), #2196f3);
            transition: all 0.3s ease;
            transform: translateX(-50%);
        }

        .nav-item:hover::before {
            width: 100%;
        }

        /* Custom Scrollbar for Services Dropdown */
        .services-dropdown {
            scrollbar-width: thin;
            scrollbar-color: #a855f7 #f3f4f6;
        }

        .services-dropdown::-webkit-scrollbar {
            width: 6px;
        }

        .services-dropdown::-webkit-scrollbar-track {
            background: #f3f4f6;
            border-radius: 3px;
        }

        .services-dropdown::-webkit-scrollbar-thumb {
            background: linear-gradient(to bottom, #a855f7, #8b5cf6);
            border-radius: 3px;
        }

        .services-dropdown::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(to bottom, #9333ea, #7c3aed);
        }

        /* Smooth scroll behavior */
        .services-dropdown {
            scroll-behavior: smooth;
        }

        /* Responsive adjustments for dropdown */
        @media (max-width: 768px) {
            .services-dropdown {
                max-height: 60vh;
                width: 90vw;
                right: 5vw;
                -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
            }
        }

        /* Touch scroll improvements */
        .services-dropdown {
            -webkit-overflow-scrolling: touch;
            overscroll-behavior: contain;
        }

        /* Enhanced Lazy loading styles */
        img.lazy {
            opacity: 0;
            transition: opacity 0.4s ease-in-out, transform 0.4s ease-in-out;
            transform: scale(1.05);
            filter: blur(5px);
        }

        img.lazy.loaded, img.loaded {
            opacity: 1;
            transform: scale(1);
            filter: blur(0);
        }

        /* Skeleton loader for images */
        .img-skeleton {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
            border-radius: 8px;
        }

        /* WebP support detection */
        .webp img[data-webp] {
            content: attr(data-webp);
        }

        .no-webp img[data-fallback] {
            content: attr(data-fallback);
        }

        /* Performance optimizations */
        * {
            box-sizing: border-box;
        }

        /* Reduce layout shifts */
        img {
            height: auto;
            max-width: 100%;
        }

        /* Critical CSS for above-the-fold content */
        .hero-section {
            will-change: transform;
        }

        /* Optimize animations */
        @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        }

        /* Core Web Vitals Optimizations */

        /* Largest Contentful Paint (LCP) */
        .hero-section, .main-content {
            contain: layout style paint;
        }

        /* First Input Delay (FID) */
        button, a, input, textarea, select {
            touch-action: manipulation;
        }

        /* Cumulative Layout Shift (CLS) */
        img, video, iframe {
            aspect-ratio: attr(width) / attr(height);
        }

        .skeleton-loader {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: loading 1.5s infinite;
        }

        @keyframes  loading {
            0% { background-position: 200% 0; }
            100% { background-position: -200% 0; }
        }

        /* Resource hints */
        .critical-resource {
            font-display: swap;
        }

        /* Intersection Observer optimizations */
        .lazy-section {
            min-height: 200px;
        }

        /* Performance optimizations */
        .will-change-transform {
            will-change: transform;
        }

        .will-change-opacity {
            will-change: opacity;
        }

        /* Remove will-change after animation */
        .animation-complete {
            will-change: auto;
        }

        /* Accessibility Improvements */

        /* Focus indicators */
        *:focus {
            outline: 2px solid #4f46e5;
            outline-offset: 2px;
        }

        .sr-only {
            position: absolute;
            width: 1px;
            height: 1px;
            padding: 0;
            margin: -1px;
            overflow: hidden;
            clip: rect(0, 0, 0, 0);
            white-space: nowrap;
            border: 0;
        }

        /* High contrast mode support */
        @media (prefers-contrast: high) {
            .btn-primary, .btn-secondary {
                border: 2px solid;
            }

            .text-gray-600 {
                color: #000 !important;
            }
        }

        /* Reduced motion support */
        @media (prefers-reduced-motion: reduce) {
            .animate-blob, .animate-pulse, .animate-bounce {
                animation: none;
            }
        }

        /* Color contrast improvements */
        .text-purple-600 {
            color: #6d28d9; /* Darker purple for better contrast */
        }

        .text-blue-600 {
            color: #1d4ed8; /* Darker blue for better contrast */
        }

        /* Skip link */
        .skip-link {
            position: absolute;
            top: -40px;
            left: 6px;
            background: #000;
            color: #fff;
            padding: 8px;
            text-decoration: none;
            z-index: 1000;
            border-radius: 4px;
        }

        .skip-link:focus {
            top: 6px;
        }

        /* Keyboard navigation improvements */
        .keyboard-nav button:focus,
        .keyboard-nav a:focus {
            box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.5);
        }
    </style>



    <!-- Google Analytics -->
    <?php if(config('app.env') === 'production'): ?>
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag(){dataLayer.push(arguments);}
        gtag('js', new Date());
        gtag('config', 'GA_MEASUREMENT_ID');
    </script>

    <!-- Google Search Console Verification -->
    <meta name="google-site-verification" content="GOOGLE_SEARCH_CONSOLE_VERIFICATION_CODE">
    <?php endif; ?>

    <!-- Advanced Schema Markup -->
    <?php echo $__env->make('partials.schema-markup', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Vite Assets -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
</head>
<body class="font-sans antialiased bg-white keyboard-nav" dir="rtl" lang="ar">
    <!-- Skip Link for Accessibility -->
    <a href="#main-content" class="skip-link">تخطي إلى المحتوى الرئيسي</a>
    <!-- Navigation -->
    <?php echo $__env->make('layouts.navigation', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Main Content -->
    <main id="main-content" role="main" tabindex="-1">
        <?php echo $__env->yieldContent('content'); ?>
        <?php echo e($slot ?? ''); ?>

    </main>

    <!-- Footer -->
    <?php echo $__env->make('layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <!-- Scripts - Optimized Loading -->
    <script>
        // Load jQuery asynchronously
        function loadScript(src, callback) {
            const script = document.createElement('script');
            script.src = src;
            script.async = true;
            script.onload = callback;
            document.head.appendChild(script);
        }

        // Load scripts in order
        loadScript('https://code.jquery.com/jquery-3.6.0.min.js', function() {
            loadScript('https://unpkg.com/aos@2.3.1/dist/aos.js', function() {
                // Initialize AOS after loading
                if (typeof AOS !== 'undefined') {
                    AOS.init({
                        duration: 1000,
                        once: true,
                        offset: 100
                    });
                }
            });
        });
    </script>

    <script>
        // Performance optimizations
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize AOS
            AOS.init({
                duration: 1000,
                once: true,
                offset: 100
            });

            // Enhanced Lazy loading for images
            const lazyImages = document.querySelectorAll('img[data-src], img[loading="lazy"]');

            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;

                            // Handle data-src lazy loading
                            if (img.dataset.src) {
                                img.src = img.dataset.src;
                                img.removeAttribute('data-src');
                            }

                            // Handle srcset for responsive images
                            if (img.dataset.srcset) {
                                img.srcset = img.dataset.srcset;
                                img.removeAttribute('data-srcset');
                            }

                            img.classList.remove('lazy');
                            img.classList.add('loaded');
                            imageObserver.unobserve(img);
                        }
                    });
                }, {
                    rootMargin: '50px 0px',
                    threshold: 0.01
                });

                lazyImages.forEach(img => {
                    img.classList.add('lazy');
                    imageObserver.observe(img);
                });
            } else {
                // Fallback for browsers without IntersectionObserver
                lazyImages.forEach(img => {
                    if (img.dataset.src) {
                        img.src = img.dataset.src;
                        img.removeAttribute('data-src');
                    }
                    if (img.dataset.srcset) {
                        img.srcset = img.dataset.srcset;
                        img.removeAttribute('data-srcset');
                    }
                });
            }

            // Smooth scrolling
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Optimized navbar scroll effect
            let ticking = false;
            function updateNavbar() {
                const navbar = document.querySelector('.navbar');
                if (navbar) {
                    if (window.scrollY > 50) {
                        navbar.classList.add('scrolled');
                    } else {
                        navbar.classList.remove('scrolled');
                    }
                }
                ticking = false;
            }

            window.addEventListener('scroll', function() {
                if (!ticking) {
                    requestAnimationFrame(updateNavbar);
                    ticking = true;
                }
            });

            // Preload critical pages
            const criticalPages = ['/services', '/hosting', '/contact'];
            criticalPages.forEach(page => {
                const link = document.createElement('link');
                link.rel = 'prefetch';
                link.href = page;
                document.head.appendChild(link);
            });

            // Register Service Worker for PWA
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                    navigator.serviceWorker.register('/sw.js')
                        .then(function(registration) {
                            console.log('ServiceWorker registration successful');

                            // Check for updates
                            registration.addEventListener('updatefound', function() {
                                const newWorker = registration.installing;
                                newWorker.addEventListener('statechange', function() {
                                    if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                                        // New content available, show update notification
                                        showUpdateNotification();
                                    }
                                });
                            });
                        })
                        .catch(function(error) {
                            console.log('ServiceWorker registration failed: ', error);
                        });
                });
            }

            // PWA Install prompt
            let deferredPrompt;
            window.addEventListener('beforeinstallprompt', function(e) {
                e.preventDefault();
                deferredPrompt = e;
                showInstallButton();
            });

            function showInstallButton() {
                const installButton = document.createElement('button');
                installButton.innerHTML = '<i class="fas fa-download ml-2"></i>تثبيت التطبيق';
                installButton.className = 'fixed bottom-4 right-4 bg-purple-600 text-white px-4 py-2 rounded-lg shadow-lg hover:bg-purple-700 transition-colors z-50';
                installButton.addEventListener('click', function() {
                    if (deferredPrompt) {
                        deferredPrompt.prompt();
                        deferredPrompt.userChoice.then(function(choiceResult) {
                            if (choiceResult.outcome === 'accepted') {
                                console.log('User accepted the install prompt');
                            }
                            deferredPrompt = null;
                            installButton.remove();
                        });
                    }
                });
                document.body.appendChild(installButton);
            }

            function showUpdateNotification() {
                const notification = document.createElement('div');
                notification.innerHTML = `
                    <div class="fixed top-4 right-4 bg-blue-600 text-white p-4 rounded-lg shadow-lg z-50">
                        <p class="mb-2">تحديث جديد متاح!</p>
                        <button onclick="updateApp()" class="bg-white text-blue-600 px-3 py-1 rounded text-sm">تحديث الآن</button>
                        <button onclick="this.parentElement.parentElement.remove()" class="mr-2 text-blue-200 hover:text-white">×</button>
                    </div>
                `;
                document.body.appendChild(notification);
            }

            window.updateApp = function() {
                if ('serviceWorker' in navigator) {
                    navigator.serviceWorker.getRegistration().then(function(registration) {
                        if (registration && registration.waiting) {
                            registration.waiting.postMessage({type: 'SKIP_WAITING'});
                            window.location.reload();
                        }
                    });
                }
            };

            // Core Web Vitals Optimizations

            // Largest Contentful Paint (LCP) optimization
            const lcpElements = document.querySelectorAll('img, video, .hero-section');
            lcpElements.forEach(element => {
                if (element.tagName === 'IMG') {
                    element.loading = 'eager';
                    element.decoding = 'sync';
                }
            });

            // First Input Delay (FID) optimization
            const interactiveElements = document.querySelectorAll('button, a, input, textarea, select');
            interactiveElements.forEach(element => {
                element.addEventListener('touchstart', function() {}, {passive: true});
            });

            // Cumulative Layout Shift (CLS) optimization
            const images = document.querySelectorAll('img');
            images.forEach(img => {
                if (!img.hasAttribute('width') || !img.hasAttribute('height')) {
                    img.addEventListener('load', function() {
                        // Remove skeleton loader if present
                        const skeleton = this.parentElement.querySelector('.skeleton-loader');
                        if (skeleton) {
                            skeleton.remove();
                        }
                    });
                }
            });

            // Intersection Observer for performance
            const observerOptions = {
                root: null,
                rootMargin: '50px',
                threshold: 0.1
            };

            const performanceObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const element = entry.target;

                        // Add will-change for animations
                        if (element.hasAttribute('data-aos')) {
                            element.classList.add('will-change-transform');

                            // Remove will-change after animation
                            setTimeout(() => {
                                element.classList.remove('will-change-transform');
                                element.classList.add('animation-complete');
                            }, 1000);
                        }

                        performanceObserver.unobserve(element);
                    }
                });
            }, observerOptions);

            // Observe elements with animations
            document.querySelectorAll('[data-aos]').forEach(el => {
                performanceObserver.observe(el);
            });

            // Web Vitals measurement (if library is available)
            if (typeof webVitals !== 'undefined') {
                webVitals.getCLS(console.log);
                webVitals.getFID(console.log);
                webVitals.getFCP(console.log);
                webVitals.getLCP(console.log);
                webVitals.getTTFB(console.log);
            }

            // Resource hints for critical resources
            const criticalResources = [
                'https://fonts.googleapis.com',
                'https://cdnjs.cloudflare.com',
                'https://unpkg.com'
            ];

            criticalResources.forEach(url => {
                const link = document.createElement('link');
                link.rel = 'dns-prefetch';
                link.href = url;
                document.head.appendChild(link);
            });

            // Advanced Performance Optimizations

            // Preload critical resources
            const criticalImages = document.querySelectorAll('img[data-critical]');
            criticalImages.forEach(img => {
                const link = document.createElement('link');
                link.rel = 'preload';
                link.as = 'image';
                link.href = img.src || img.dataset.src;
                document.head.appendChild(link);
            });

            // Defer non-critical CSS
            const nonCriticalCSS = document.querySelectorAll('link[rel="stylesheet"][data-defer]');
            nonCriticalCSS.forEach(link => {
                link.media = 'print';
                link.onload = function() {
                    this.media = 'all';
                };
            });

            // Image optimization with WebP support
            function supportsWebP() {
                return new Promise(resolve => {
                    const webP = new Image();
                    webP.onload = webP.onerror = () => resolve(webP.height === 2);
                    webP.src = 'data:image/webp;base64,UklGRjoAAABXRUJQVlA4IC4AAACyAgCdASoCAAIALmk0mk0iIiIiIgBoSygABc6WWgAA/veff/0PP8bA//LwYAAA';
                });
            }

            supportsWebP().then(supported => {
                if (supported) {
                    document.documentElement.classList.add('webp');
                } else {
                    document.documentElement.classList.add('no-webp');
                }
            });

            // Connection-aware loading
            if ('connection' in navigator) {
                const connection = navigator.connection;
                if (connection.effectiveType === 'slow-2g' || connection.effectiveType === '2g') {
                    document.documentElement.classList.add('slow-connection');
                    // Disable animations and heavy features
                    document.querySelectorAll('[data-aos]').forEach(el => {
                        el.removeAttribute('data-aos');
                    });
                }
            }

            // Battery-aware optimizations
            if ('getBattery' in navigator) {
                navigator.getBattery().then(battery => {
                    if (battery.level < 0.2 || !battery.charging) {
                        document.documentElement.classList.add('low-battery');
                        // Reduce animations and effects
                        document.querySelectorAll('.animate-blob').forEach(el => {
                            el.style.animation = 'none';
                        });
                    }
                });
            }

            // Memory-aware optimizations
            if ('memory' in performance) {
                if (performance.memory.usedJSHeapSize / performance.memory.jsHeapSizeLimit > 0.8) {
                    document.documentElement.classList.add('low-memory');
                    // Reduce memory usage
                    console.log('Low memory detected, optimizing...');
                }
            }

            // Adaptive loading based on device capabilities
            const isHighEndDevice = () => {
                return navigator.hardwareConcurrency >= 4 &&
                       'memory' in performance &&
                       performance.memory.jsHeapSizeLimit >= 1073741824; // 1GB
            };

            if (!isHighEndDevice()) {
                document.documentElement.classList.add('low-end-device');
                // Simplify UI for low-end devices
                document.querySelectorAll('.backdrop-blur').forEach(el => {
                    el.classList.remove('backdrop-blur');
                });
            }

            // Prefetch next likely pages
            const prefetchPages = ['/services', '/hosting', '/contact'];
            let prefetchTimer;

            function startPrefetching() {
                clearTimeout(prefetchTimer);
                prefetchTimer = setTimeout(() => {
                    prefetchPages.forEach(page => {
                        const link = document.createElement('link');
                        link.rel = 'prefetch';
                        link.href = page;
                        document.head.appendChild(link);
                    });
                }, 2000);
            }

            // Start prefetching after user interaction
            ['mouseenter', 'touchstart', 'focus'].forEach(event => {
                document.addEventListener(event, startPrefetching, {once: true, passive: true});
            });

            // Performance monitoring
            if ('PerformanceObserver' in window) {
                const observer = new PerformanceObserver((list) => {
                    for (const entry of list.getEntries()) {
                        if (entry.entryType === 'largest-contentful-paint') {
                            console.log('LCP:', entry.startTime);
                        }
                        if (entry.entryType === 'first-input') {
                            console.log('FID:', entry.processingStart - entry.startTime);
                        }
                        if (entry.entryType === 'layout-shift') {
                            if (!entry.hadRecentInput) {
                                console.log('CLS:', entry.value);
                            }
                        }
                    }
                });

                try {
                    observer.observe({entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift']});
                } catch (e) {
                    console.log('Performance Observer not fully supported');
                }
            }
        });
    </script>

    <!-- Browser Compatibility Script -->
    <script src="<?php echo e(asset('js/browser-compatibility.js')); ?>"></script>

    <!-- Performance & SEO Optimizers -->
    <script src="<?php echo e(asset('js/performance-optimizer.js')); ?>" defer></script>
    <script src="<?php echo e(asset('js/seo-optimizer.js')); ?>" defer></script>

    <!-- Image Optimizer -->
    <script src="<?php echo e(asset('js/image-optimizer.js')); ?>" defer></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Services Dropdown Scroll Enhancement -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const servicesDropdown = document.querySelector('.services-dropdown');
            const bottomScrollIndicator = servicesDropdown?.querySelector('.absolute.bottom-0');
            const topScrollIndicator = document.getElementById('top-scroll-indicator');

            if (servicesDropdown && bottomScrollIndicator && topScrollIndicator) {
                // Function to update scroll indicators visibility
                function updateScrollIndicators() {
                    const isScrollable = servicesDropdown.scrollHeight > servicesDropdown.clientHeight;
                    const isAtTop = servicesDropdown.scrollTop <= 5;
                    const isAtBottom = servicesDropdown.scrollTop + servicesDropdown.clientHeight >= servicesDropdown.scrollHeight - 5;

                    // Bottom indicator (shows when there's more content below)
                    if (!isScrollable || isAtBottom) {
                        bottomScrollIndicator.style.opacity = '0';
                    } else {
                        bottomScrollIndicator.style.opacity = '1';
                    }

                    // Top indicator (shows when user has scrolled down)
                    if (!isScrollable || isAtTop) {
                        topScrollIndicator.style.opacity = '0';
                    } else {
                        topScrollIndicator.style.opacity = '1';
                    }
                }

                // Add scroll event listener
                servicesDropdown.addEventListener('scroll', updateScrollIndicators);

                // Initial check
                setTimeout(updateScrollIndicators, 100);

                // Add smooth scroll to dropdown items
                const dropdownItems = servicesDropdown.querySelectorAll('a');
                dropdownItems.forEach(item => {
                    item.addEventListener('focus', function() {
                        this.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                    });
                });

                // Add keyboard navigation support
                servicesDropdown.addEventListener('keydown', function(e) {
                    if (e.key === 'ArrowDown' || e.key === 'ArrowUp') {
                        e.preventDefault();
                        const focusedElement = document.activeElement;
                        const items = Array.from(dropdownItems);
                        const currentIndex = items.indexOf(focusedElement);

                        if (e.key === 'ArrowDown' && currentIndex < items.length - 1) {
                            items[currentIndex + 1].focus();
                        } else if (e.key === 'ArrowUp' && currentIndex > 0) {
                            items[currentIndex - 1].focus();
                        }
                    }
                });
            }
        });
    </script>

    <!-- Meta Tech Custom Animations -->
    <script src="<?php echo e(asset('js/animations.js')); ?>" defer></script>
</body>
</html>
<?php /**PATH C:\xampp2\htdocs\‏‏Meta\resources\views/layouts/app.blade.php ENDPATH**/ ?>