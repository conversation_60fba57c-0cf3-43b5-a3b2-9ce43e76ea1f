/* Additional styles for the new admin dashboard */

/* Smooth animations */
* {
    transition: all 0.3s ease;
}

/* Custom scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 3px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Hover effects for cards */
.stat-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

.balance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

/* Loading states */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e2e8f0;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Button hover effects */
.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.notification-btn:hover {
    transform: scale(1.05);
}

/* Chart containers */
.chart-container canvas {
    max-height: 300px !important;
}

/* Responsive improvements */
@media (max-width: 1200px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .header-right {
        flex-direction: column;
        gap: 12px;
    }
    
    .search-input {
        width: 100%;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .dashboard-container {
        background: #1a202c;
        color: #e2e8f0;
    }
    
    .sidebar {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .main-content {
        background: #1a202c;
    }
    
    .top-header {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .stat-card,
    .chart-card,
    .balance-card {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}

/* Print styles */
@media print {
    .sidebar,
    .top-header {
        display: none !important;
    }
    
    .main-content {
        margin-right: 0 !important;
    }
    
    .dashboard-content {
        padding: 0 !important;
    }
    
    .stat-card,
    .chart-card {
        break-inside: avoid;
        margin-bottom: 20px;
    }
}

/* Accessibility improvements */
.nav-link:focus,
.notification-btn:focus,
.filter-btn:focus {
    outline: 2px solid #667eea;
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    .stat-card,
    .chart-card,
    .balance-card {
        border: 2px solid #000;
    }
    
    .nav-link:hover {
        background: #000;
        color: #fff;
    }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
    * {
        transition: none !important;
        animation: none !important;
    }
}

/* Custom utilities */
.text-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.shadow-soft {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.shadow-medium {
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.shadow-strong {
    box-shadow: 0 12px 25px rgba(0, 0, 0, 0.15);
}

/* Animation classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

.slide-up {
    animation: slideUp 0.5s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from { 
        opacity: 0;
        transform: translateY(20px);
    }
    to { 
        opacity: 1;
        transform: translateY(0);
    }
}

/* Status indicators */
.status-online {
    color: #10b981;
}

.status-offline {
    color: #ef4444;
}

.status-pending {
    color: #f59e0b;
}

/* Notification badges */
.notification-badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Custom form elements */
.form-control-modern {
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 12px 16px;
    font-size: 14px;
    transition: all 0.3s ease;
}

.form-control-modern:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    outline: none;
}

/* Modern buttons */
.btn-modern {
    border: none;
    border-radius: 12px;
    padding: 12px 24px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-modern:hover {
    transform: translateY(-2px);
}

.btn-primary-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-secondary-modern {
    background: #f8fafc;
    color: #64748b;
    border: 1px solid #e2e8f0;
}

.btn-secondary-modern:hover {
    background: #f1f5f9;
    border-color: #cbd5e1;
}
