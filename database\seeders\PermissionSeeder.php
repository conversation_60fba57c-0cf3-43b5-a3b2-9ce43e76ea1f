<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;
use App\Models\Admin;

class PermissionSeeder extends Seeder
{
    /**
     * Run the database seeder.
     */
    public function run(): void
    {
        // Reset cached roles and permissions
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // Create permissions
        $permissions = [
            // User Management
            'manage-users' => 'إدارة المستخدمين',
            'view-users' => 'عرض المستخدمين',
            'create-users' => 'إنشاء المستخدمين',
            'edit-users' => 'تعديل المستخدمين',
            'delete-users' => 'حذف المستخدمين',
            'verify-users' => 'تفعيل المستخدمين',

            // Admin Management
            'manage-admins' => 'إدارة المديرين',
            'view-admins' => 'عرض المديرين',
            'create-admins' => 'إنشاء المديرين',
            'edit-admins' => 'تعديل المديرين',
            'delete-admins' => 'حذف المديرين',

            // Role & Permission Management
            'manage-roles' => 'إدارة الأدوار',
            'view-roles' => 'عرض الأدوار',
            'create-roles' => 'إنشاء الأدوار',
            'edit-roles' => 'تعديل الأدوار',
            'delete-roles' => 'حذف الأدوار',
            'assign-roles' => 'تعيين الأدوار',
            'manage-permissions' => 'إدارة الصلاحيات',

            // Order Management
            'manage-orders' => 'إدارة الطلبات',
            'view-orders' => 'عرض الطلبات',
            'edit-orders' => 'تعديل الطلبات',
            'delete-orders' => 'حذف الطلبات',
            'approve-orders' => 'الموافقة على الطلبات',

            // Domain Management
            'manage-domains' => 'إدارة الدومينات',
            'view-domains' => 'عرض الدومينات',
            'edit-domains' => 'تعديل الدومينات',
            'delete-domains' => 'حذف الدومينات',
            'renew-domains' => 'تجديد الدومينات',

            // Hosting Management
            'manage-hosting' => 'إدارة الاستضافة',
            'view-hosting' => 'عرض خدمات الاستضافة',
            'edit-hosting' => 'تعديل خدمات الاستضافة',
            'delete-hosting' => 'حذف خدمات الاستضافة',

            // Email Management
            'manage-emails' => 'إدارة البريد الإلكتروني',
            'view-emails' => 'عرض خدمات البريد',
            'edit-emails' => 'تعديل خدمات البريد',
            'delete-emails' => 'حذف خدمات البريد',

            // Project Management
            'manage-projects' => 'إدارة المشاريع',
            'view-projects' => 'عرض المشاريع',
            'create-projects' => 'إنشاء المشاريع',
            'edit-projects' => 'تعديل المشاريع',
            'delete-projects' => 'حذف المشاريع',

            // Portfolio Management
            'manage-portfolios' => 'إدارة الأعمال',
            'view-portfolios' => 'عرض الأعمال',
            'create-portfolios' => 'إنشاء الأعمال',
            'edit-portfolios' => 'تعديل الأعمال',
            'delete-portfolios' => 'حذف الأعمال',

            // Contact Management
            'manage-contacts' => 'إدارة الرسائل',
            'view-contacts' => 'عرض الرسائل',
            'reply-contacts' => 'الرد على الرسائل',
            'delete-contacts' => 'حذف الرسائل',

            // Blog Management
            'manage-blog' => 'إدارة المدونة',
            'view-blog' => 'عرض المدونة',
            'create-blog' => 'إنشاء مقالات',
            'edit-blog' => 'تعديل المقالات',
            'delete-blog' => 'حذف المقالات',

            // Page Management
            'manage-pages' => 'إدارة الصفحات',
            'view-pages' => 'عرض الصفحات',
            'create-pages' => 'إنشاء الصفحات',
            'edit-pages' => 'تعديل الصفحات',
            'delete-pages' => 'حذف الصفحات',

            // Service Management
            'manage-services' => 'إدارة الخدمات',
            'view-services' => 'عرض الخدمات',
            'create-services' => 'إنشاء الخدمات',
            'edit-services' => 'تعديل الخدمات',
            'delete-services' => 'حذف الخدمات',

            // Reports & Analytics
            'view-reports' => 'عرض التقارير',
            'export-reports' => 'تصدير التقارير',
            'view-analytics' => 'عرض الإحصائيات',

            // System Settings
            'manage-settings' => 'إدارة إعدادات النظام',
            'view-settings' => 'عرض الإعدادات',
            'edit-settings' => 'تعديل الإعدادات',
            'backup-system' => 'نسخ احتياطي للنظام',

            // Dashboard
            'view-dashboard' => 'عرض لوحة التحكم',
            'view-statistics' => 'عرض الإحصائيات',
        ];

        foreach ($permissions as $name => $displayName) {
            Permission::firstOrCreate([
                'name' => $name,
                'guard_name' => 'admin'
            ], [
                'display_name' => $displayName
            ]);
        }

        // Create roles
        $superAdminRole = Role::firstOrCreate(['name' => 'Super Admin', 'guard_name' => 'admin']);
        $adminRole = Role::firstOrCreate(['name' => 'Admin', 'guard_name' => 'admin']);
        $moderatorRole = Role::firstOrCreate(['name' => 'Moderator', 'guard_name' => 'admin']);

        // Assign all permissions to Super Admin
        $superAdminRole->givePermissionTo(Permission::where('guard_name', 'admin')->get());

        // Assign specific permissions to Admin
        $adminRole->givePermissionTo([
            'manage-users',
            'manage-services',
            'manage-projects',
            'manage-blog',
            'manage-contacts',
            'manage-hosting',
            'manage-domains',
            'manage-emails',
            'manage-orders',
            'manage-pages',
            'view-dashboard',
        ]);

        // Assign limited permissions to Moderator
        $moderatorRole->givePermissionTo([
            'manage-contacts',
            'manage-blog',
            'view-dashboard',
        ]);

        // Assign Super Admin role to the first admin (if exists)
        $firstAdmin = Admin::first();
        if ($firstAdmin) {
            $firstAdmin->assignRole($superAdminRole);
        }
    }
}
