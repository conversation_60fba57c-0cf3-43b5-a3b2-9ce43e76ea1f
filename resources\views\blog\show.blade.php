@extends('layouts.app')

@section('title', 'مقال - ميتاء تك')

@push('styles')
<style>
/* Article Styles */
.article-content {
    font-size: 1.125rem;
    line-height: 1.8;
    color: #374151;
}

.article-content h2 {
    font-size: 1.875rem;
    font-weight: 700;
    color: #1f2937;
    margin: 2rem 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e5e7eb;
}

.article-content h3 {
    font-size: 1.5rem;
    font-weight: 600;
    color: #374151;
    margin: 1.5rem 0 1rem 0;
}

.article-content p {
    margin-bottom: 1.5rem;
}

.article-content ul, .article-content ol {
    margin: 1.5rem 0;
    padding-right: 2rem;
}

.article-content li {
    margin-bottom: 0.5rem;
}

.article-content blockquote {
    background: #f8fafc;
    border-right: 4px solid #3b82f6;
    padding: 1rem 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #4b5563;
}

.article-content code {
    background: #f1f5f9;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-family: 'Courier New', monospace;
    color: #dc2626;
}

.article-content pre {
    background: #1e293b;
    color: #e2e8f0;
    padding: 1.5rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 2rem 0;
}

.article-content pre code {
    background: none;
    color: inherit;
    padding: 0;
}

.share-button {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    transition: all 0.3s ease;
    margin: 0.25rem;
}

.share-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
    color: white;
}

.related-article {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.related-article:hover {
    transform: translateY(-4px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
}

.author-card {
    background: linear-gradient(135deg, #f8fafc, #e2e8f0);
    border-radius: 1rem;
    padding: 2rem;
    margin: 3rem 0;
}

.progress-bar {
    position: fixed;
    top: 0;
    left: 0;
    width: 0%;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
    z-index: 1000;
    transition: width 0.3s ease;
}

.floating-toc {
    position: sticky;
    top: 2rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.toc-link {
    color: #6b7280;
    text-decoration: none;
    display: block;
    padding: 0.5rem 0;
    border-right: 2px solid transparent;
    transition: all 0.3s ease;
}

.toc-link:hover,
.toc-link.active {
    color: #3b82f6;
    border-right-color: #3b82f6;
    padding-right: 1rem;
}
</style>
@endpush

@section('content')
<!-- Progress Bar -->
<div class="progress-bar" id="progressBar"></div>

<!-- Breadcrumbs -->
<x-breadcrumbs :items="[
    ['title' => 'المدونة', 'url' => route('blog.index')],
    ['title' => 'المقال', 'url' => '#']
]" />

@php
// بيانات المقالات (في التطبيق الحقيقي ستأتي من قاعدة البيانات)
$articles = [
    'latest-web-development-technologies-2024' => [
        'title' => 'أحدث تقنيات تطوير المواقع في 2024',
        'category' => 'التقنية',
        'date' => '15 يوليو 2024',
        'author' => 'فريق ميتاء تك',
        'read_time' => '10 دقائق',
        'image' => 'https://images.unsplash.com/photo-1555066931-4365d14bab8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'اكتشف أحدث التقنيات والأدوات المستخدمة في تطوير المواقع الحديثة، من React و Vue.js إلى Laravel و Node.js.',
        'content' => [
            'intro' => 'في عالم التطوير السريع التطور، تظهر تقنيات جديدة باستمرار تهدف إلى تحسين تجربة المطورين وتسريع عملية التطوير. في هذا المقال، سنستكشف أحدث التقنيات والأدوات التي تشكل مستقبل تطوير المواقع في عام 2024.',
            'sections' => [
                'التقنيات الأساسية' => [
                    'content' => 'تشهد صناعة تطوير المواقع تطوراً مستمراً، ومن أهم التقنيات التي برزت في الآونة الأخيرة:',
                    'list' => [
                        'React 18: مع ميزات جديدة مثل Concurrent Features و Suspense المحسن',
                        'Next.js 14: إطار عمل React مع تحسينات في الأداء والـ SEO',
                        'Laravel 11: أحدث إصدار من إطار العمل PHP الأكثر شعبية',
                        'Vue.js 3: مع Composition API والأداء المحسن',
                        'TypeScript: لكتابة JavaScript أكثر أماناً وقابلية للصيانة'
                    ]
                ],
                'أدوات التطوير الحديثة' => [
                    'content' => 'تلعب أدوات التطوير دوراً مهماً في تحسين إنتاجية المطورين. من أهم هذه الأدوات: Vite للبناء السريع، Docker للحاويات، وGitHub Actions للتكامل المستمر.',
                    'quote' => 'الأدوات الجيدة لا تجعل المطور الجيد، لكنها تساعده على أن يكون أكثر إنتاجية وفعالية.'
                ]
            ],
            'conclusion' => 'تطوير المواقع في 2024 يتطلب مواكبة التقنيات الحديثة والأدوات المتطورة. من المهم للمطورين الاستمرار في التعلم والتطوير لمواكبة هذا التطور السريع في المجال.'
        ]
    ],
    'laravel-11-guide' => [
        'title' => 'دليل شامل لتعلم Laravel 11',
        'category' => 'البرمجة',
        'date' => '12 يوليو 2024',
        'author' => 'أحمد محمد',
        'read_time' => '15 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'تعلم أساسيات Laravel 11 وكيفية بناء تطبيقات ويب قوية ومرنة باستخدام أحدث إصدار من إطار العمل الأكثر شعبية في PHP.',
        'content' => [
            'intro' => 'Laravel 11 يأتي بميزات جديدة ومحسنة تجعل تطوير تطبيقات الويب أسرع وأكثر فعالية. في هذا الدليل الشامل، سنتعلم كيفية البدء مع Laravel 11 وبناء تطبيقات قوية.',
            'sections' => [
                'ما الجديد في Laravel 11' => [
                    'content' => 'Laravel 11 يقدم تحسينات كبيرة في الأداء والأمان:',
                    'list' => [
                        'تحسينات في Eloquent ORM للاستعلامات الأسرع',
                        'نظام Cache محسن مع دعم أفضل للتوزيع',
                        'ميزات أمان جديدة لحماية التطبيقات',
                        'واجهة برمجة تطبيقات محسنة للتعامل مع قواعد البيانات',
                        'دعم أفضل لـ PHP 8.3 والميزات الحديثة'
                    ]
                ],
                'البدء مع Laravel 11' => [
                    'content' => 'لبدء مشروع جديد مع Laravel 11، تحتاج إلى تثبيت Composer أولاً، ثم تشغيل الأوامر التالية لإنشاء مشروع جديد وتكوين قاعدة البيانات.',
                    'code' => 'composer create-project laravel/laravel my-project\ncd my-project\nphp artisan serve'
                ]
            ],
            'conclusion' => 'Laravel 11 يوفر أدوات قوية لبناء تطبيقات ويب حديثة. مع الممارسة والتعلم المستمر، ستتمكن من إتقان هذا الإطار الرائع.'
        ]
    ],
    'react-interactive-apps' => [
        'title' => 'بناء تطبيقات تفاعلية مع React',
        'category' => 'البرمجة',
        'date' => '6 يوليو 2024',
        'author' => 'سارة أحمد',
        'read_time' => '12 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1627398242454-45a1465c2479?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'اكتشف قوة React في بناء واجهات المستخدم التفاعلية وتعلم أفضل الممارسات لتطوير تطبيقات ويب حديثة وسريعة.',
        'content' => [
            'intro' => 'React هو مكتبة JavaScript قوية لبناء واجهات المستخدم التفاعلية. في هذا المقال، سنتعلم كيفية استخدام React لإنشاء تطبيقات ويب حديثة وسريعة الاستجابة.',
            'sections' => [
                'مفاهيم React الأساسية' => [
                    'content' => 'React يعتمد على مفاهيم أساسية مهمة:',
                    'list' => [
                        'Components: وحدات البناء الأساسية للتطبيق',
                        'JSX: صيغة لكتابة HTML داخل JavaScript',
                        'Props: لتمرير البيانات بين المكونات',
                        'State: لإدارة حالة المكونات',
                        'Hooks: لإضافة وظائف للمكونات الوظيفية'
                    ]
                ],
                'مثال عملي' => [
                    'content' => 'إليك مثال بسيط على مكون React تفاعلي:',
                    'code' => 'import React, { useState } from \'react\';\n\nfunction Counter() {\n  const [count, setCount] = useState(0);\n\n  return (\n    <div>\n      <p>العدد: {count}</p>\n      <button onClick={() => setCount(count + 1)}>\n        زيادة\n      </button>\n    </div>\n  );\n}'
                ]
            ],
            'conclusion' => 'React يوفر طريقة فعالة لبناء واجهات مستخدم تفاعلية. مع فهم المفاهيم الأساسية والممارسة، ستتمكن من إنشاء تطبيقات رائعة.'
        ]
    ],
    'ui-ux-design-basics' => [
        'title' => 'أساسيات تصميم واجهات المستخدم',
        'category' => 'التصميم',
        'date' => '10 يوليو 2024',
        'author' => 'مريم سالم',
        'read_time' => '8 دقائق',
        'image' => 'https://images.unsplash.com/photo-1558655146-9f40138edfeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'اكتشف المبادئ الأساسية لتصميم واجهات المستخدم الفعالة وكيفية إنشاء تجربة مستخدم مميزة.',
        'content' => [
            'intro' => 'تصميم واجهات المستخدم (UI) وتجربة المستخدم (UX) هما عنصران أساسيان في نجاح أي منتج رقمي. في هذا المقال، سنتعلم المبادئ الأساسية لتصميم واجهات فعالة.',
            'sections' => [
                'مبادئ التصميم الأساسية' => [
                    'content' => 'هناك مبادئ أساسية يجب اتباعها في تصميم الواجهات:',
                    'list' => [
                        'البساطة: تجنب التعقيد غير الضروري',
                        'الوضوح: جعل الواجهة سهلة الفهم',
                        'التناسق: استخدام نفس الأنماط في كامل التطبيق',
                        'إمكانية الوصول: جعل التصميم متاحاً للجميع',
                        'التغذية الراجعة: إعطاء المستخدم ردود فعل واضحة'
                    ]
                ],
                'أدوات التصميم' => [
                    'content' => 'هناك العديد من الأدوات المفيدة لمصممي الواجهات مثل Figma وAdobe XD وSketch، والتي تساعد في إنشاء نماذج أولية وتصاميم تفاعلية.',
                    'quote' => 'التصميم الجيد هو التصميم الذي لا يلاحظه المستخدم - يعمل ببساطة وسلاسة.'
                ]
            ],
            'conclusion' => 'تصميم واجهات المستخدم الفعالة يتطلب فهماً عميقاً لاحتياجات المستخدمين وتطبيق المبادئ الأساسية للتصميم.'
        ]
    ],
    'seo-optimization' => [
        'title' => 'تحسين محركات البحث للمواقع',
        'category' => 'التسويق',
        'date' => '8 يوليو 2024',
        'author' => 'خالد العمري',
        'read_time' => '12 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1563206767-5b18f218e8de?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'تعلم أفضل الممارسات لتحسين موقعك لمحركات البحث وزيادة ظهوره في النتائج الأولى.',
        'content' => [
            'intro' => 'تحسين محركات البحث (SEO) هو عملية تحسين موقعك الإلكتروني ليظهر في النتائج الأولى لمحركات البحث. في هذا الدليل الشامل، سنتعلم أهم استراتيجيات SEO الفعالة.',
            'sections' => [
                'أساسيات SEO' => [
                    'content' => 'تحسين محركات البحث يتطلب فهم العوامل الأساسية التي تؤثر على ترتيب موقعك:',
                    'list' => [
                        'الكلمات المفتاحية: اختيار الكلمات المناسبة لجمهورك',
                        'المحتوى عالي الجودة: إنشاء محتوى مفيد وأصلي',
                        'البنية التقنية: تحسين سرعة الموقع وسهولة التنقل',
                        'الروابط الخارجية: بناء شبكة روابط قوية',
                        'تجربة المستخدم: تحسين معدل الارتداد ووقت البقاء'
                    ]
                ],
                'استراتيجيات متقدمة' => [
                    'content' => 'للحصول على نتائج أفضل، يمكن تطبيق استراتيجيات متقدمة مثل تحسين البحث المحلي، واستخدام البيانات المنظمة، وتحسين الموقع للأجهزة المحمولة.',
                    'quote' => 'SEO ليس مجرد تقنية، بل استراتيجية طويلة المدى لبناء حضور قوي على الإنترنت.'
                ]
            ],
            'conclusion' => 'تحسين محركات البحث عملية مستمرة تتطلب صبراً ومثابرة. مع تطبيق الاستراتيجيات الصحيحة، ستحقق نتائج ممتازة في ترتيب موقعك.'
        ]
    ],
    'website-security' => [
        'title' => 'أمان المواقع والحماية من الهجمات',
        'category' => 'الأمان',
        'date' => '4 يوليو 2024',
        'author' => 'عمر التقني',
        'read_time' => '15 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'تعرف على أهم التهديدات الأمنية وكيفية حماية موقعك من الهجمات الإلكترونية والثغرات.',
        'content' => [
            'intro' => 'أمان المواقع الإلكترونية أصبح أولوية قصوى في عصر التهديدات السيبرانية المتزايدة. في هذا المقال، سنتعلم كيفية حماية مواقعنا من أشهر أنواع الهجمات.',
            'sections' => [
                'أنواع التهديدات الأمنية' => [
                    'content' => 'هناك عدة أنواع من التهديدات التي تواجه المواقع الإلكترونية:',
                    'list' => [
                        'هجمات SQL Injection: استغلال ثغرات قواعد البيانات',
                        'هجمات XSS: حقن أكواد ضارة في المتصفح',
                        'هجمات CSRF: تنفيذ عمليات غير مصرح بها',
                        'هجمات DDoS: إغراق الخادم بطلبات وهمية',
                        'البرمجيات الخبيثة: رفع ملفات ضارة للخادم'
                    ]
                ],
                'استراتيجيات الحماية' => [
                    'content' => 'لحماية موقعك بشكل فعال، يجب تطبيق طبقات متعددة من الأمان تشمل التشفير، والمصادقة القوية، والمراقبة المستمرة.',
                    'quote' => 'الأمان ليس منتجاً يُشترى، بل عملية مستمرة تتطلب يقظة دائمة.',
                    'code' => '// مثال على تنظيف البيانات المدخلة\n$clean_input = htmlspecialchars(strip_tags($_POST[\'user_input\']), ENT_QUOTES, \'UTF-8\');\n\n// استخدام Prepared Statements\n$stmt = $pdo->prepare("SELECT * FROM users WHERE email = ?");\n$stmt->execute([$email]);'
                ]
            ],
            'conclusion' => 'أمان المواقع مسؤولية مشتركة تتطلب تطبيق أفضل الممارسات والتحديث المستمر للحماية من التهديدات الجديدة.'
        ]
    ],
    'google-analytics-guide' => [
        'title' => 'تحليل بيانات الموقع باستخدام Google Analytics',
        'category' => 'تحليلات',
        'date' => '2 يوليو 2024',
        'author' => 'نورا المحللة',
        'read_time' => '10 دقائق',
        'image' => 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'تعلم كيفية استخدام Google Analytics لفهم سلوك زوار موقعك وتحسين الأداء.',
        'content' => [
            'intro' => 'Google Analytics هو أداة قوية لتحليل بيانات موقعك وفهم سلوك الزوار. في هذا الدليل، سنتعلم كيفية استخدامه لاتخاذ قرارات مدروسة لتحسين موقعك.',
            'sections' => [
                'إعداد Google Analytics' => [
                    'content' => 'البدء مع Google Analytics يتطلب خطوات أساسية:',
                    'list' => [
                        'إنشاء حساب Google Analytics: التسجيل وإعداد الخاصية',
                        'تثبيت كود التتبع: إضافة الكود لجميع صفحات الموقع',
                        'تكوين الأهداف: تحديد الإجراءات المهمة للتتبع',
                        'ربط Google Search Console: للحصول على بيانات البحث',
                        'إعداد التقارير المخصصة: لمراقبة المؤشرات المهمة'
                    ]
                ],
                'قراءة وتحليل البيانات' => [
                    'content' => 'فهم التقارير والمؤشرات الأساسية يساعدك في اتخاذ قرارات صحيحة لتحسين أداء موقعك وزيادة التحويلات.',
                    'quote' => 'البيانات بدون تحليل مجرد أرقام، والتحليل بدون عمل مجرد معرفة.',
                    'code' => '<!-- كود Google Analytics 4 -->\n<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>\n<script>\n  window.dataLayer = window.dataLayer || [];\n  function gtag(){dataLayer.push(arguments);}\n  gtag(\'js\', new Date());\n  gtag(\'config\', \'GA_MEASUREMENT_ID\');\n</script>'
                ]
            ],
            'conclusion' => 'Google Analytics أداة لا غنى عنها لأي صاحب موقع يريد فهم جمهوره وتحسين أداء موقعه بناءً على بيانات حقيقية.'
        ]
    ],
    'database-design-basics' => [
        'title' => 'تحسين محركات البحث للمواقع',
        'category' => 'قواعد البيانات',
        'date' => '30 يونيو 2024',
        'author' => 'أحمد قواعد البيانات',
        'read_time' => '14 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1544383835-bda2bc66a55d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'تعلم أساسيات تصميم قواعد البيانات وكيفية إنشاء هياكل بيانات محسنة للأداء.',
        'content' => [
            'intro' => 'تصميم قواعد البيانات الجيد هو أساس أي تطبيق ناجح. في هذا المقال، سنتعلم المبادئ الأساسية لتصميم قواعد بيانات فعالة ومحسنة للأداء.',
            'sections' => [
                'مبادئ التصميم الأساسية' => [
                    'content' => 'تصميم قاعدة البيانات يتطلب فهم المبادئ التالية:',
                    'list' => [
                        'التطبيع (Normalization): تقليل التكرار وضمان سلامة البيانات',
                        'العلاقات: تحديد الروابط بين الجداول بشكل صحيح',
                        'الفهرسة: تحسين سرعة الاستعلامات',
                        'أنواع البيانات: اختيار النوع المناسب لكل حقل',
                        'القيود: ضمان سلامة وصحة البيانات'
                    ]
                ],
                'تحسين الأداء' => [
                    'content' => 'لضمان أداء ممتاز لقاعدة البيانات، يجب تطبيق تقنيات التحسين المختلفة مثل الفهرسة الذكية، وتحسين الاستعلامات، واستخدام التخزين المؤقت.',
                    'quote' => 'قاعدة البيانات الجيدة هي التي تنمو مع تطبيقك دون أن تفقد كفاءتها.',
                    'code' => '-- مثال على إنشاء فهرس محسن\nCREATE INDEX idx_user_email ON users(email);\n\n-- استعلام محسن\nSELECT u.name, p.title \nFROM users u \nINNER JOIN posts p ON u.id = p.user_id \nWHERE u.active = 1 \nORDER BY p.created_at DESC \nLIMIT 10;'
                ]
            ],
            'conclusion' => 'تصميم قواعد البيانات الجيد استثمار طويل المدى يوفر الوقت والجهد ويضمن قابلية التوسع والصيانة.'
        ]
    ],
    'mobile-app-ui-design' => [
        'title' => 'أساسيات تصميم واجهات المستخدم',
        'category' => 'تصميم الجوال',
        'date' => '28 يونيو 2024',
        'author' => 'ليلى المصممة',
        'read_time' => '11 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1512941937669-90a1b58e7e9c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'اكتشف أفضل الممارسات في تصميم واجهات تطبيقات الجوال وكيفية إنشاء تجربة مستخدم سلسة.',
        'content' => [
            'intro' => 'تصميم واجهات تطبيقات الجوال يتطلب فهماً عميقاً لسلوك المستخدمين على الأجهزة المحمولة. في هذا المقال، سنستكشف أفضل الممارسات لتصميم واجهات جوال فعالة.',
            'sections' => [
                'مبادئ التصميم للجوال' => [
                    'content' => 'تصميم الجوال له خصائص فريدة يجب مراعاتها:',
                    'list' => [
                        'التصميم المتجاوب: التكيف مع أحجام الشاشات المختلفة',
                        'اللمس أولاً: تصميم للتفاعل باللمس',
                        'البساطة: واجهات نظيفة وسهلة الاستخدام',
                        'السرعة: تحميل سريع وانتقالات سلسة',
                        'إمكانية الوصول: دعم جميع المستخدمين'
                    ]
                ],
                'أدوات وتقنيات التصميم' => [
                    'content' => 'استخدام الأدوات المناسبة يسهل عملية التصميم ويحسن النتائج النهائية. من أهم هذه الأدوات Figma وSketch وAdobe XD.',
                    'quote' => 'التصميم الجيد للجوال هو الذي يجعل المستخدم ينسى أنه يستخدم جهازاً صغيراً.',
                    'code' => '/* CSS للتصميم المتجاوب */\n@media (max-width: 768px) {\n  .container {\n    padding: 1rem;\n    font-size: 14px;\n  }\n  \n  .button {\n    min-height: 44px; /* حد أدنى للمس */\n    width: 100%;\n  }\n}'
                ]
            ],
            'conclusion' => 'تصميم واجهات الجوال الناجحة يتطلب فهم احتياجات المستخدمين وتطبيق أفضل الممارسات لضمان تجربة مستخدم ممتازة.'
        ]
    ],
    'cloud-computing-guide' => [
        'title' => 'أمان المواقع والحماية من الهجمات',
        'category' => 'الحوسبة السحابية',
        'date' => '26 يونيو 2024',
        'author' => 'محمد السحابي',
        'read_time' => '13 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1451187580459-43490279c0fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'استكشف عالم الحوسبة السحابية وكيف تغير طريقة تطوير ونشر التطبيقات.',
        'content' => [
            'intro' => 'الحوسبة السحابية ثورة حقيقية في عالم التكنولوجيا، حيث غيرت طريقة تطوير ونشر وإدارة التطبيقات. في هذا المقال، سنستكشف أساسيات الحوسبة السحابية وفوائدها.',
            'sections' => [
                'أنواع الخدمات السحابية' => [
                    'content' => 'هناك ثلاثة أنواع رئيسية من الخدمات السحابية:',
                    'list' => [
                        'IaaS: البنية التحتية كخدمة (مثل AWS EC2)',
                        'PaaS: المنصة كخدمة (مثل Google App Engine)',
                        'SaaS: البرمجيات كخدمة (مثل Office 365)',
                        'FaaS: الوظائف كخدمة (مثل AWS Lambda)',
                        'DBaaS: قواعد البيانات كخدمة (مثل Amazon RDS)'
                    ]
                ],
                'مقارنة بين مقدمي الخدمات' => [
                    'content' => 'أشهر مقدمي الخدمات السحابية هم Amazon AWS وMicrosoft Azure وGoogle Cloud، ولكل منهم مميزات وخدمات فريدة.',
                    'quote' => 'السحابة ليست مجرد تقنية، بل طريقة جديدة للتفكير في البنية التحتية والتطوير.',
                    'code' => '# مثال على نشر تطبيق على AWS\naws configure set region us-east-1\naws s3 cp ./build s3://my-app-bucket --recursive\naws cloudfront create-invalidation --distribution-id E123456 --paths "/*"'
                ]
            ],
            'conclusion' => 'الحوسبة السحابية أصبحت ضرورة لا غنى عنها للشركات الحديثة، حيث توفر المرونة والقابلية للتوسع والكفاءة في التكلفة.'
        ]
    ],
    'laravel-api-development' => [
        'title' => 'بناء تطبيقات تفاعلية مع React',
        'category' => 'تطوير API',
        'date' => '24 يونيو 2024',
        'author' => 'عبدالله المطور',
        'read_time' => '16 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'دليل شامل لبناء واجهات برمجة التطبيقات RESTful باستخدام Laravel مع أفضل الممارسات.',
        'content' => [
            'intro' => 'بناء واجهات برمجة التطبيقات (APIs) أصبح جزءاً أساسياً من تطوير التطبيقات الحديثة. في هذا الدليل، سنتعلم كيفية بناء APIs قوية ومرنة باستخدام Laravel.',
            'sections' => [
                'أساسيات REST API' => [
                    'content' => 'REST API يتبع مبادئ محددة لتصميم واجهات برمجة فعالة:',
                    'list' => [
                        'HTTP Methods: استخدام GET, POST, PUT, DELETE بشكل صحيح',
                        'Status Codes: إرجاع رموز الحالة المناسبة',
                        'JSON Format: تنسيق البيانات بشكل متسق',
                        'Authentication: حماية الـ API بالتوثيق المناسب',
                        'Rate Limiting: تحديد معدل الطلبات لمنع الإساءة'
                    ]
                ],
                'تطبيق عملي مع Laravel' => [
                    'content' => 'Laravel يوفر أدوات قوية لبناء APIs مثل API Resources وMiddleware والتوثيق باستخدام Sanctum أو Passport.',
                    'quote' => 'API الجيد هو الذي يجعل حياة المطور أسهل، وليس أصعب.',
                    'code' => '<?php\n// مثال على API Controller\nclass PostController extends Controller\n{\n    public function index()\n    {\n        $posts = Post::with(\'user\')->paginate(15);\n        return PostResource::collection($posts);\n    }\n    \n    public function store(Request $request)\n    {\n        $validated = $request->validate([\n            \'title\' => \'required|max:255\',\n            \'content\' => \'required\'\n        ]);\n        \n        $post = auth()->user()->posts()->create($validated);\n        return new PostResource($post);\n    }\n}'
                ]
            ],
            'conclusion' => 'بناء APIs جيدة يتطلب فهم المبادئ الأساسية وتطبيق أفضل الممارسات لضمان الأمان والأداء والقابلية للصيانة.'
        ]
    ],
    'ecommerce-design-guide' => [
        'title' => 'تصميم المتاجر الإلكترونية عالية التحويل',
        'category' => 'التجارة الإلكترونية',
        'date' => '22 يونيو 2024',
        'author' => 'فاطمة التجارية',
        'read_time' => '12 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'تعلم أساسيات تصميم المتاجر الإلكترونية التي تحقق مبيعات عالية من تجربة المستخدم إلى عملية الدفع.',
        'content' => [
            'intro' => 'تصميم المتاجر الإلكترونية يتطلب فهماً عميقاً لسلوك المتسوقين عبر الإنترنت. في هذا المقال، سنتعلم كيفية تصميم متجر إلكتروني يحقق أعلى معدلات التحويل.',
            'sections' => [
                'عناصر التصميم الأساسية' => [
                    'content' => 'المتجر الإلكتروني الناجح يحتاج إلى عناصر أساسية:',
                    'list' => [
                        'صفحة رئيسية جذابة: عرض المنتجات الأساسية بوضوح',
                        'تصنيف المنتجات: تنظيم منطقي وسهل التصفح',
                        'صفحات المنتجات: صور عالية الجودة ووصف مفصل',
                        'عربة التسوق: عملية إضافة وإزالة سهلة',
                        'عملية الدفع: خطوات قليلة وواضحة'
                    ]
                ],
                'تحسين معدل التحويل' => [
                    'content' => 'لزيادة المبيعات، يجب التركيز على تحسين تجربة المستخدم وبناء الثقة من خلال شهادات العملاء وضمانات الأمان.',
                    'quote' => 'المتجر الإلكتروني الناجح هو الذي يجعل عملية الشراء أسهل من الذهاب للمتجر الفعلي.',
                    'code' => '<!-- مثال على زر إضافة للسلة محسن -->\n<button class="add-to-cart-btn" \n        data-product-id="123"\n        data-price="99.99">\n  <i class="fas fa-shopping-cart"></i>\n  أضف للسلة - 99.99 ريال\n</button>\n\n<script>\n// تحسين تجربة إضافة المنتج\n$(\'.add-to-cart-btn\').click(function() {\n  // إضافة تأثير بصري\n  $(this).addClass(\'loading\');\n  // إرسال طلب AJAX\n  // عرض رسالة نجاح\n});\n</script>'
                ]
            ],
            'conclusion' => 'تصميم المتاجر الإلكترونية الناجحة يتطلب التوازن بين الجمال والوظائف، مع التركيز على تجربة المستخدم وسهولة الاستخدام.'
        ]
    ],
    'devops-fundamentals' => [
        'title' => 'مقدمة في DevOps وأتمتة التطوير',
        'category' => 'DevOps',
        'date' => '20 يونيو 2024',
        'author' => 'سامي العمليات',
        'read_time' => '14 دقيقة',
        'image' => 'https://images.unsplash.com/photo-1518432031352-d6fc5c10da5a?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80',
        'excerpt' => 'اكتشف عالم DevOps وكيفية تطبيق مبادئ التطوير والعمليات لتحسين دورة حياة التطوير.',
        'content' => [
            'intro' => 'DevOps هو منهجية تجمع بين التطوير (Development) والعمليات (Operations) لتحسين سرعة وجودة تسليم البرمجيات. في هذا المقال، سنستكشف أساسيات DevOps وفوائده.',
            'sections' => [
                'مبادئ DevOps الأساسية' => [
                    'content' => 'DevOps يقوم على مبادئ أساسية تهدف لتحسين التعاون والكفاءة:',
                    'list' => [
                        'التكامل المستمر (CI): دمج التغييرات بشكل مستمر',
                        'النشر المستمر (CD): نشر التطبيقات بشكل آلي',
                        'المراقبة والتسجيل: مراقبة الأداء والأخطاء',
                        'البنية كرمز (IaC): إدارة البنية التحتية بالكود',
                        'الثقافة التعاونية: تحسين التواصل بين الفرق'
                    ]
                ],
                'أدوات DevOps الشائعة' => [
                    'content' => 'هناك العديد من الأدوات التي تساعد في تطبيق DevOps مثل Docker للحاويات، وJenkins للتكامل المستمر، وKubernetes لإدارة الحاويات.',
                    'quote' => 'DevOps ليس مجرد أدوات، بل ثقافة وطريقة تفكير جديدة في التطوير.',
                    'code' => '# مثال على Dockerfile\nFROM node:16-alpine\nWORKDIR /app\nCOPY package*.json ./\nRUN npm ci --only=production\nCOPY . .\nEXPOSE 3000\nCMD ["npm", "start"]\n\n# مثال على pipeline CI/CD\nstages:\n  - build\n  - test\n  - deploy\n\nbuild:\n  script:\n    - docker build -t myapp .\n    - docker push registry/myapp'
                ]
            ],
            'conclusion' => 'DevOps يحسن من سرعة التطوير وجودة المنتج النهائي، ويقلل من الأخطاء ويزيد من رضا العملاء والمطورين.'
        ]
    ]
];

// الحصول على المقال الحالي أو استخدام مقال افتراضي
$currentArticle = $articles[$slug] ?? $articles['latest-web-development-technologies-2024'];
@endphp

<!-- Article Header -->
<section class="py-16 bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center" data-aos="fade-up">
            <!-- Category Badge -->
            <span class="inline-block px-4 py-2 bg-blue-600 text-white text-sm font-semibold rounded-full mb-6">
                {{ $currentArticle['category'] }}
            </span>
            
            <!-- Title -->
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight">
                {{ $currentArticle['title'] }}
            </h1>
            
            <!-- Meta Info -->
            <div class="flex flex-wrap justify-center items-center gap-6 text-gray-600 mb-8">
                <div class="flex items-center">
                    <i class="fas fa-calendar-alt mr-2 text-blue-600"></i>
                    <span>{{ $currentArticle['date'] }}</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-user mr-2 text-blue-600"></i>
                    <span>{{ $currentArticle['author'] }}</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-clock mr-2 text-blue-600"></i>
                    <span>{{ $currentArticle['read_time'] }} قراءة</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-eye mr-2 text-blue-600"></i>
                    <span>2,547 مشاهدة</span>
                </div>
            </div>
            
            <!-- Excerpt -->
            <p class="text-xl text-gray-600 leading-relaxed max-w-3xl mx-auto">
                {{ $currentArticle['excerpt'] }}
            </p>
        </div>
    </div>
</section>

<!-- Article Content -->
<section class="py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid lg:grid-cols-4 gap-12">
            <!-- Main Content -->
            <div class="lg:col-span-3">
                <!-- Featured Image -->
                <div class="mb-12" data-aos="fade-up">
                    <img src="{{ $currentArticle['image'] }}" 
                         alt="{{ $currentArticle['title'] }}" 
                         class="w-full h-96 object-cover rounded-2xl shadow-lg">
                </div>

                <!-- Article Body -->
                <div class="article-content" data-aos="fade-up" data-aos-delay="200">
                    <h2 id="introduction">مقدمة</h2>
                    <p>{{ $currentArticle['content']['intro'] }}</p>

                    @foreach($currentArticle['content']['sections'] as $sectionTitle => $sectionData)
                        <h2 id="{{ Str::slug($sectionTitle) }}">{{ $sectionTitle }}</h2>
                        <p>{{ $sectionData['content'] }}</p>

                        @if(isset($sectionData['list']))
                            <ul>
                                @foreach($sectionData['list'] as $listItem)
                                    @php
                                        $parts = explode(':', $listItem, 2);
                                        $title = $parts[0];
                                        $description = isset($parts[1]) ? $parts[1] : '';
                                    @endphp
                                    <li><strong>{{ $title }}:</strong>{{ $description }}</li>
                                @endforeach
                            </ul>
                        @endif

                        @if(isset($sectionData['quote']))
                            <blockquote>
                                "{{ $sectionData['quote'] }}"
                            </blockquote>
                        @endif

                        @if(isset($sectionData['code']))
                            <h3>مثال عملي</h3>
                            <p>إليك مثال بسيط:</p>
                            <pre><code>{{ $sectionData['code'] }}</code></pre>
                        @endif
                    @endforeach

                    <h2 id="conclusion">الخلاصة</h2>
                    <p>{{ $currentArticle['content']['conclusion'] }}</p>

                    <p>
                        نأمل أن يكون هذا المقال مفيداً لك في رحلتك التطويرية. لا تتردد في مشاركة أفكارك وتجاربك
                        في التعليقات أدناه.
                    </p>
                </div>

                <!-- Share Buttons -->
                <div class="mt-12 pt-8 border-t border-gray-200" data-aos="fade-up">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">شارك المقال</h3>
                    <div class="flex flex-wrap gap-2">
                        <a href="#" class="share-button">
                            <i class="fab fa-facebook-f mr-2"></i>
                            فيسبوك
                        </a>
                        <a href="#" class="share-button">
                            <i class="fab fa-twitter mr-2"></i>
                            تويتر
                        </a>
                        <a href="#" class="share-button">
                            <i class="fab fa-linkedin-in mr-2"></i>
                            لينكد إن
                        </a>
                        <a href="#" class="share-button">
                            <i class="fab fa-whatsapp mr-2"></i>
                            واتساب
                        </a>
                        <a href="#" class="share-button">
                            <i class="fas fa-link mr-2"></i>
                            نسخ الرابط
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Table of Contents -->
                <div class="floating-toc" data-aos="fade-left">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">محتويات المقال</h3>
                    <nav>
                        <a href="#introduction" class="toc-link">مقدمة</a>
                        @foreach($currentArticle['content']['sections'] as $sectionTitle => $sectionData)
                            <a href="#{{ Str::slug($sectionTitle) }}" class="toc-link">{{ $sectionTitle }}</a>
                        @endforeach
                        <a href="#conclusion" class="toc-link">الخلاصة</a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Author Card -->
<section class="py-8 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="author-card" data-aos="fade-up">
            <div class="flex flex-col md:flex-row items-center gap-6">
                <div class="flex-shrink-0">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&q=80"
                         alt="{{ $currentArticle['author'] }}"
                         class="w-24 h-24 rounded-full object-cover border-4 border-white shadow-lg">
                </div>
                <div class="text-center md:text-right flex-1">
                    <h3 class="text-xl font-bold text-gray-900 mb-2">{{ $currentArticle['author'] }}</h3>
                    <p class="text-gray-600 mb-4">
                        مطور ويب متخصص في Laravel و React مع أكثر من 8 سنوات من الخبرة في تطوير التطبيقات الحديثة.
                        شارك في تطوير أكثر من 100 مشروع ناجح ويحب مشاركة المعرفة مع المجتمع التقني.
                    </p>
                    <div class="flex justify-center md:justify-end gap-3">
                        <a href="#" class="text-blue-600 hover:text-blue-800 transition-colors">
                            <i class="fab fa-twitter text-xl"></i>
                        </a>
                        <a href="#" class="text-blue-600 hover:text-blue-800 transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                        <a href="#" class="text-blue-600 hover:text-blue-800 transition-colors">
                            <i class="fab fa-github text-xl"></i>
                        </a>
                        <a href="#" class="text-blue-600 hover:text-blue-800 transition-colors">
                            <i class="fas fa-globe text-xl"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Articles -->
<section class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12" data-aos="fade-up">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">مقالات ذات صلة</h2>
            <p class="text-gray-600">اكتشف المزيد من المحتوى المفيد</p>
        </div>

        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Related Article 1 -->
            <article class="related-article rounded-xl overflow-hidden" data-aos="fade-up" data-aos-delay="100">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1558655146-9f40138edfeb?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                         alt="UI/UX Design" class="w-full h-48 object-cover">
                    <div class="absolute top-4 right-4">
                        <span class="bg-purple-600 text-white px-3 py-1 rounded-full text-sm font-semibold">التصميم</span>
                    </div>
                </div>

                <div class="p-6">
                    <div class="text-sm text-gray-500 mb-3">
                        <span><i class="fas fa-calendar-alt mr-2"></i>10 يوليو 2024</span>
                        <span class="mx-2">•</span>
                        <span><i class="fas fa-clock mr-2"></i>4 دقائق</span>
                    </div>

                    <h3 class="text-xl font-bold text-gray-900 mb-3">
                        أساسيات تصميم واجهات المستخدم
                    </h3>

                    <p class="text-gray-600 mb-4">
                        اكتشف المبادئ الأساسية لتصميم واجهات المستخدم الفعالة وكيفية إنشاء تجربة مستخدم مميزة.
                    </p>

                    <a href="{{ route('blog.show', 'ui-ux-design-basics') }}" class="text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        اقرأ المزيد <i class="fas fa-arrow-left mr-2"></i>
                    </a>
                </div>
            </article>

            <!-- Related Article 2 -->
            <article class="related-article rounded-xl overflow-hidden" data-aos="fade-up" data-aos-delay="200">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1563206767-5b18f218e8de?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                         alt="SEO" class="w-full h-48 object-cover">
                    <div class="absolute top-4 right-4">
                        <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm font-semibold">نصائح</span>
                    </div>
                </div>

                <div class="p-6">
                    <div class="text-sm text-gray-500 mb-3">
                        <span><i class="fas fa-calendar-alt mr-2"></i>8 يوليو 2024</span>
                        <span class="mx-2">•</span>
                        <span><i class="fas fa-clock mr-2"></i>6 دقائق</span>
                    </div>

                    <h3 class="text-xl font-bold text-gray-900 mb-3">
                        تحسين محركات البحث للمواقع
                    </h3>

                    <p class="text-gray-600 mb-4">
                        تعلم أفضل الممارسات لتحسين موقعك لمحركات البحث وزيادة ظهوره في النتائج الأولى.
                    </p>

                    <a href="{{ route('blog.show', 'seo-optimization') }}" class="text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        اقرأ المزيد <i class="fas fa-arrow-left mr-2"></i>
                    </a>
                </div>
            </article>

            <!-- Related Article 3 -->
            <article class="related-article rounded-xl overflow-hidden" data-aos="fade-up" data-aos-delay="300">
                <div class="relative">
                    <img src="https://images.unsplash.com/photo-1550751827-4bd374c3f58b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                         alt="Security" class="w-full h-48 object-cover">
                    <div class="absolute top-4 right-4">
                        <span class="bg-red-600 text-white px-3 py-1 rounded-full text-sm font-semibold">التقنية</span>
                    </div>
                </div>

                <div class="p-6">
                    <div class="text-sm text-gray-500 mb-3">
                        <span><i class="fas fa-calendar-alt mr-2"></i>4 يوليو 2024</span>
                        <span class="mx-2">•</span>
                        <span><i class="fas fa-clock mr-2"></i>7 دقائق</span>
                    </div>

                    <h3 class="text-xl font-bold text-gray-900 mb-3">
                        أمان المواقع والحماية من الهجمات
                    </h3>

                    <p class="text-gray-600 mb-4">
                        تعرف على أهم التهديدات الأمنية وكيفية حماية موقعك من الهجمات الإلكترونية والثغرات.
                    </p>

                    <a href="{{ route('blog.show', 'website-security') }}" class="text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                        اقرأ المزيد <i class="fas fa-arrow-left mr-2"></i>
                    </a>
                </div>
            </article>
        </div>
    </div>
</section>

<!-- Comments Section -->
<section class="py-16 bg-gray-50">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="bg-white rounded-2xl p-8 shadow-lg" data-aos="fade-up">
            <h3 class="text-2xl font-bold text-gray-900 mb-8">التعليقات (12)</h3>

            <!-- Comment Form -->
            <div class="mb-8 p-6 bg-gray-50 rounded-xl">
                <h4 class="text-lg font-semibold text-gray-900 mb-4">اترك تعليقاً</h4>
                <form class="space-y-4">
                    <div class="grid md:grid-cols-2 gap-4">
                        <input type="text" placeholder="الاسم"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <input type="email" placeholder="البريد الإلكتروني"
                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <textarea placeholder="تعليقك..." rows="4"
                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"></textarea>
                    <button type="submit"
                            class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-all duration-300">
                        <i class="fas fa-paper-plane mr-2"></i>
                        إرسال التعليق
                    </button>
                </form>
            </div>

            <!-- Comments List -->
            <div class="space-y-6">
                <!-- Comment 1 -->
                <div class="flex gap-4 p-4 bg-gray-50 rounded-xl">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80"
                         alt="User" class="w-12 h-12 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-2">
                            <h5 class="font-semibold text-gray-900">محمد علي</h5>
                            <span class="text-sm text-gray-500">منذ يومين</span>
                        </div>
                        <p class="text-gray-700 mb-2">
                            مقال رائع ومفيد جداً! شكراً لك على المعلومات القيمة. أتطلع لقراءة المزيد من مقالاتك.
                        </p>
                        <button class="text-blue-600 text-sm hover:text-blue-800 transition-colors">
                            <i class="fas fa-reply mr-1"></i>
                            رد
                        </button>
                    </div>
                </div>

                <!-- Comment 2 -->
                <div class="flex gap-4 p-4 bg-gray-50 rounded-xl">
                    <img src="https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80"
                         alt="User" class="w-12 h-12 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-2">
                            <h5 class="font-semibold text-gray-900">فاطمة أحمد</h5>
                            <span class="text-sm text-gray-500">منذ 3 أيام</span>
                        </div>
                        <p class="text-gray-700 mb-2">
                            هل يمكنك إضافة المزيد من الأمثلة العملية؟ أعتقد أن ذلك سيساعد المبتدئين أكثر.
                        </p>
                        <button class="text-blue-600 text-sm hover:text-blue-800 transition-colors">
                            <i class="fas fa-reply mr-1"></i>
                            رد
                        </button>
                    </div>
                </div>

                <!-- Comment 3 -->
                <div class="flex gap-4 p-4 bg-gray-50 rounded-xl">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&q=80"
                         alt="User" class="w-12 h-12 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-2">
                            <h5 class="font-semibold text-gray-900">أحمد محمد</h5>
                            <span class="text-sm text-gray-500 bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">الكاتب</span>
                            <span class="text-sm text-gray-500">منذ يوم</span>
                        </div>
                        <p class="text-gray-700 mb-2">
                            @فاطمة أحمد شكراً لك على الاقتراح! سأعمل على إضافة المزيد من الأمثلة العملية في المقالات القادمة.
                        </p>
                        <button class="text-blue-600 text-sm hover:text-blue-800 transition-colors">
                            <i class="fas fa-reply mr-1"></i>
                            رد
                        </button>
                    </div>
                </div>
            </div>

            <!-- Load More Comments -->
            <div class="text-center mt-8">
                <button class="text-blue-600 font-semibold hover:text-blue-800 transition-colors">
                    <i class="fas fa-chevron-down mr-2"></i>
                    عرض المزيد من التعليقات
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Newsletter CTA -->
<section class="py-16 bg-gradient-to-r from-blue-600 to-purple-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div class="text-white" data-aos="fade-up">
            <h2 class="text-3xl md:text-4xl font-bold mb-4">
                لا تفوت أحدث المقالات
            </h2>
            <p class="text-xl text-blue-100 mb-8">
                اشترك في النشرة الإخبارية واحصل على أحدث المقالات التقنية مباشرة في بريدك
            </p>

            <div class="flex flex-col md:flex-row gap-4 justify-center max-w-md mx-auto">
                <input type="email" placeholder="بريدك الإلكتروني"
                       class="flex-1 px-6 py-3 rounded-full text-gray-900 focus:outline-none focus:ring-2 focus:ring-white">
                <button class="bg-white text-blue-600 px-8 py-3 rounded-full font-semibold hover:bg-gray-100 transition-colors">
                    اشترك الآن
                </button>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
// Progress Bar
window.addEventListener('scroll', function() {
    const winScroll = document.body.scrollTop || document.documentElement.scrollTop;
    const height = document.documentElement.scrollHeight - document.documentElement.clientHeight;
    const scrolled = (winScroll / height) * 100;
    document.getElementById('progressBar').style.width = scrolled + '%';
});

// Table of Contents Active Link
const tocLinks = document.querySelectorAll('.toc-link');
const sections = document.querySelectorAll('h2, h3');

window.addEventListener('scroll', function() {
    let current = '';
    sections.forEach(section => {
        const sectionTop = section.offsetTop;
        const sectionHeight = section.clientHeight;
        if (scrollY >= (sectionTop - 200)) {
            current = section.textContent;
        }
    });

    tocLinks.forEach(link => {
        link.classList.remove('active');
        if (link.textContent === current) {
            link.classList.add('active');
        }
    });
});

// Copy Link Functionality
document.querySelector('.share-button:last-child').addEventListener('click', function(e) {
    e.preventDefault();
    navigator.clipboard.writeText(window.location.href).then(function() {
        alert('تم نسخ الرابط بنجاح!');
    });
});
</script>
@endpush
