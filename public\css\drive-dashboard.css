/* Google Drive Inspired Dashboard Styles */

/* Global Reset */
html, body {
    width: 100%;
    overflow-x: hidden !important;
    margin: 0;
    padding: 0;
    height: 100%;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: var(--drive-gray-50);
    color: var(--drive-gray-800);
    line-height: 1.6;
    direction: rtl;
    text-align: right;
}

/* منع التمرير الأفقي في جميع العناصر */
* {
    max-width: 100%;
    box-sizing: border-box;
}

.main-wrapper,
.dashboard-container,
.drive-main-content {
    width: 100vw !important;
    max-width: 100vw !important;
    overflow-x: hidden !important;
    margin: 0 auto !important;
    padding: 0 !important;
    display: flex;
}

.container, .wrapper {
    max-width: 100% !important;
    width: 100%;
    overflow-x: hidden;
}

/* إضافة أنماط لضمان العرض الكامل */
.w-100 {
    width: 100% !important;
    max-width: 100% !important;
}

/* تأكد من عدم وجود قيود على العرض */
.row, .col, [class*="col-"] {
    max-width: 100% !important;
    overflow-x: hidden;
}

/* إزالة أي margins أو paddings قد تسبب مشاكل */
.no-gutters {
    margin-right: 0 !important;
    margin-left: 0 !important;
}

.no-gutters > .col,
.no-gutters > [class*="col-"] {
    padding-right: 0 !important;
    padding-left: 0 !important;
}

/* تأكد من عدم وجود قيود على العرض */
.row, .col, [class*="col-"] {
    max-width: 100% !important;
    overflow-x: hidden;
}

/* إضافة أنماط لضمان العرض الكامل */
.w-100 {
    width: 100% !important;
    max-width: 100% !important;
}

/* تأكد من عدم وجود قيود على العرض */
.row, .col, [class*="col-"] {
    max-width: 100% !important;
    overflow-x: hidden;
}

/* إضافة أنماط لضمان العرض الكامل */
.w-100 {
    width: 100% !important;
    max-width: 100% !important;
}

/* تأكد من عدم وجود قيود على العرض */
.row, .col, [class*="col-"] {
    max-width: 100% !important;
    overflow-x: hidden;
}

*, *::before, *::after {
    box-sizing: border-box;
}

:root {
    --drive-primary: #8b5cf6;
    --drive-primary-hover: #7c3aed;
    --drive-secondary: #3b82f6;
    --drive-warning: #f59e0b;
    --drive-danger: #ef4444;
    --drive-gray-50: #f8f9fa;
    --drive-gray-100: #f1f3f4;
    --drive-gray-200: #e8eaed;
    --drive-gray-300: #dadce0;
    --drive-gray-400: #bdc1c6;
    --drive-gray-500: #9aa0a6;
    --drive-gray-600: #80868b;
    --drive-gray-700: #5f6368;
    --drive-gray-800: #3c4043;
    --drive-gray-900: #202124;
    --drive-white: #ffffff;
    --drive-border-radius: 8px;
    --drive-shadow: 0 1px 2px 0 rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);
    --drive-shadow-hover: 0 1px 3px 0 rgba(60, 64, 67, 0.3), 0 4px 8px 3px rgba(60, 64, 67, 0.15);
}

/* إخفاء navbar الافتراضي من AdminLTE */
.main-header.navbar {
    display: none !important;
}

/* إظهار القائمة الجانبية من AdminLTE */
.main-sidebar {
    display: block !important;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
}

.content-header {
    display: none !important;
}

/* تعديل المحتوى ليبدأ من الأعلى ويترك مساحة للقائمة الجانبية */
.content-wrapper {
    margin-top: 0 !important;
    margin-right: 250px !important;
    margin-left: 0 !important;
    transition: margin-right 0.3s ease;
}

/* إعادة تعيين wrapper AdminLTE */
.wrapper {
    min-height: 100vh !important;
}

/* عندما تكون القائمة الجانبية مطوية */
.sidebar-collapse .content-wrapper {
    margin-right: 60px !important;
}

.sidebar-collapse .drive-dashboard-container {
    width: calc(100vw - 60px) !important;
    max-width: calc(100vw - 60px) !important;
    margin-right: 60px;
}

/* Dashboard Container */
.drive-dashboard-container {
    background: var(--drive-gray-50);
    height: 100vh;
    font-family: 'Google Sans', 'Roboto', Arial, sans-serif;
    overflow: hidden;
    width: calc(100vw - 250px) !important;
    max-width: calc(100vw - 250px) !important;
    margin: 0;
    margin-right: 250px;
    padding: 0;
    position: relative;
    display: flex;
    flex-direction: column;
    transition: width 0.3s ease, margin-right 0.3s ease;
}

/* Dashboard Header */
.drive-dashboard-header {
    background: linear-gradient(135deg, var(--drive-white) 0%, #f8fafc 100%);
    border-bottom: 1px solid var(--drive-gray-200);
    padding: 12px 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 72px;
    position: sticky;
    top: 0;
    z-index: 100;
    width: 100%;
    max-width: 100%;
    overflow-x: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.dashboard-title-wrapper {
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: nowrap;
}

.dashboard-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--drive-primary) 0%, var(--drive-secondary) 100%);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 18px;
    box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
    flex-shrink: 0;
}

.dashboard-title-text {
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.header-left .dashboard-title {
    font-size: 22px;
    font-weight: 600;
    color: var(--drive-gray-800);
    margin: 0;
    line-height: 1.2;
    white-space: nowrap;
}

.dashboard-subtitle {
    font-size: 11px;
    color: var(--drive-gray-500);
    font-weight: 400;
    line-height: 1;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 20px;
}

/* Quick Actions */
.quick-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.quick-action-btn {
    position: relative;
    width: 40px;
    height: 40px;
    border: none;
    background: var(--drive-gray-100);
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--drive-gray-600);
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.quick-action-btn:hover {
    background: var(--drive-primary);
    color: white;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(26, 115, 232, 0.3);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: #ff5733;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    min-width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Search Container */
.search-container {
    flex: 1;
    max-width: 720px;
    margin: 0 32px;
}

.search-box {
    position: relative;
    background: var(--drive-gray-100);
    border-radius: 24px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    transition: all 0.2s ease;
}

.search-box:hover {
    background: var(--drive-gray-200);
    box-shadow: var(--drive-shadow);
}

.search-box i {
    color: var(--drive-gray-600);
    font-size: 16px;
}

.search-box input {
    border: none;
    background: transparent;
    outline: none;
    flex: 1;
    font-size: 16px;
    color: var(--drive-gray-800);
}

.search-box input::placeholder {
    color: var(--drive-gray-500);
}

/* Header Actions */
.header-actions {
    display: flex;
    align-items: center;
    gap: 8px;
}

.view-toggle {
    display: flex;
    background: var(--drive-gray-100);
    border-radius: 20px;
    padding: 4px;
}

.view-btn {
    background: transparent;
    border: none;
    padding: 8px 12px;
    border-radius: 16px;
    color: var(--drive-gray-600);
    cursor: pointer;
    transition: all 0.2s ease;
}

.view-btn.active {
    background: var(--drive-white);
    color: var(--drive-primary);
    box-shadow: var(--drive-shadow);
}

.upload-btn {
    background: var(--drive-primary);
    color: var(--drive-white);
    border: none;
    padding: 10px 24px;
    border-radius: 24px;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.upload-btn:hover {
    background: var(--drive-primary-hover);
    box-shadow: var(--drive-shadow-hover);
}

/* User Menu */
.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    border: none;
    background: var(--drive-primary);
    cursor: pointer;
    overflow: hidden;
    position: relative;
}

.user-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-fallback {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--drive-white);
    font-weight: 500;
    font-size: 14px;
}

/* Main Content */
.drive-main-content {
    display: flex;
    flex-wrap: nowrap;
    gap: 0;
    padding: 0;
    width: 100%;
    max-width: 100%;
    margin: 0;
    height: calc(100vh - 72px);
    overflow-y: auto;
    overflow-x: hidden;
}

.content-area {
    flex: 1;
    min-width: 0; /* مهم لمنع التمدد الزائد */
    overflow-x: hidden;
    width: 100%;
    max-width: 100% !important;
    padding: 24px;
    background: var(--drive-gray-50);
}

/* Quick Stats Bar */
.quick-stats-bar {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
    width: 100%;
}

.quick-stat-item {
    background: var(--drive-white);
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 16px;
    border: 1px solid var(--drive-gray-200);
    transition: all 0.2s ease;
}

.quick-stat-item:hover {
    transform: translateY(-2px);
    box-shadow: var(--drive-shadow-hover);
    border-color: var(--drive-primary);
}

.quick-stat-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    color: white;
    flex-shrink: 0;
}

.quick-stat-icon.projects {
    background: linear-gradient(135deg, var(--drive-primary) 0%, var(--drive-secondary) 100%);
}

.quick-stat-icon.clients {
    background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
}

.quick-stat-icon.revenue {
    background: linear-gradient(135deg, var(--drive-secondary) 0%, #2e7d32 100%);
}

.quick-stat-icon.tasks {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.quick-stat-info {
    display: flex;
    flex-direction: column;
}

.quick-stat-number {
    font-size: 24px;
    font-weight: 700;
    color: var(--drive-gray-800);
    line-height: 1.2;
}

.quick-stat-label {
    font-size: 12px;
    color: var(--drive-gray-600);
    font-weight: 500;
}

/* Welcome Hero Section */
.welcome-hero-section {
    background: var(--drive-white);
    border-radius: var(--drive-border-radius);
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: var(--drive-shadow);
    position: relative;
    overflow: hidden;
}

.welcome-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 32px;
}

.welcome-text {
    flex: 1;
}

.welcome-title {
    font-size: 24px;
    font-weight: 400;
    color: var(--drive-gray-800);
    margin: 0 0 8px 0;
}

.welcome-description {
    color: var(--drive-gray-600);
    font-size: 14px;
    line-height: 1.5;
    margin: 0 0 24px 0;
}

.btn-upgrade {
    background: var(--drive-primary);
    color: var(--drive-white);
    border: none;
    padding: 10px 24px;
    border-radius: 20px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-upgrade:hover {
    background: var(--drive-primary-hover);
    box-shadow: var(--drive-shadow-hover);
}

/* Welcome Illustration */
.welcome-illustration {
    flex-shrink: 0;
    width: 200px;
    height: 150px;
    position: relative;
}

.illustration-container {
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cloud-icon {
    position: absolute;
    top: 20px;
    right: 40px;
    width: 60px;
    height: 40px;
    background: var(--drive-primary);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--drive-white);
    font-size: 24px;
    box-shadow: var(--drive-shadow);
}

.cloud-icon::before {
    content: '';
    position: absolute;
    top: -8px;
    left: 15px;
    width: 20px;
    height: 20px;
    background: var(--drive-primary);
    border-radius: 50%;
}

.cloud-icon::after {
    content: '';
    position: absolute;
    top: -5px;
    right: 10px;
    width: 15px;
    height: 15px;
    background: var(--drive-primary);
    border-radius: 50%;
}

.character-illustration {
    position: relative;
    width: 80px;
    height: 120px;
}

.character-body {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 60px;
    background: var(--drive-secondary);
    border-radius: 20px 20px 8px 8px;
}

.character-head {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 30px;
    height: 30px;
    background: #fdbcb4;
    border-radius: 50%;
}

.character-arms {
    position: absolute;
    top: 35px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 20px;
    background: var(--drive-secondary);
    border-radius: 10px;
}

/* Section Headers */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.section-header h4 {
    font-size: 16px;
    font-weight: 500;
    color: var(--drive-gray-800);
    margin: 0;
}

.view-all-link {
    color: var(--drive-primary);
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: color 0.2s ease;
}

.view-all-link:hover {
    color: var(--drive-primary-hover);
    text-decoration: none;
}

/* Folders Section */
.drive-folders-section {
    margin-bottom: 32px;
}

.drive-folders-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 24px;
    width: 100%;
    max-width: 100%;
    margin-bottom: 24px;
    margin-top: 20px;
    overflow-x: hidden;
}

.drive-folder-card {
    background: var(--drive-white);
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    cursor: pointer;
    border: 1px solid rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.drive-folder-card:hover {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    transform: translateY(-4px) scale(1.02);
    border-color: rgba(26, 115, 232, 0.2);
}

.drive-folder-card.red {
    border-left: 4px solid var(--drive-danger);
}

.drive-folder-card.blue {
    border-left: 4px solid var(--drive-primary);
}

.drive-folder-card.orange {
    border-left: 4px solid var(--drive-warning);
}

.drive-folder-actions {
    position: absolute;
    top: 16px;
    right: 16px;
}

.drive-action-btn {
    background: transparent;
    border: none;
    color: var(--drive-gray-500);
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0;
}

.drive-folder-card:hover .drive-action-btn {
    opacity: 1;
}

.drive-action-btn:hover {
    background: var(--drive-gray-100);
    color: var(--drive-gray-700);
}

.drive-folder-icon {
    width: 40px;
    height: 40px;
    background: var(--drive-gray-100);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    font-size: 18px;
    color: var(--drive-gray-600);
}

.drive-folder-card.red .drive-folder-icon {
    background: rgba(234, 67, 53, 0.1);
    color: var(--drive-danger);
}

.drive-folder-card.blue .drive-folder-icon {
    background: rgba(26, 115, 232, 0.1);
    color: var(--drive-primary);
}

.drive-folder-card.orange .drive-folder-icon {
    background: rgba(251, 188, 4, 0.1);
    color: var(--drive-warning);
}

.drive-folder-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--drive-gray-800);
    margin-bottom: 12px;
}

.drive-folder-meta {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.drive-folder-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.drive-folder-info span {
    font-size: 12px;
    color: var(--drive-gray-600);
    display: flex;
    align-items: center;
    gap: 4px;
}

.drive-folder-avatars {
    display: flex;
    margin-right: -8px;
}

.drive-folder-avatar {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: var(--drive-primary);
    color: var(--drive-white);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: 500;
    margin-right: -8px;
    border: 2px solid var(--drive-white);
}

.drive-folder-avatar:nth-child(2) {
    background: var(--drive-secondary);
}

.drive-folder-avatar:nth-child(3) {
    background: var(--drive-warning);
}

/* Recent Files Section */
.recent-files {
    background: var(--drive-white);
    border-radius: var(--drive-border-radius);
    box-shadow: var(--drive-shadow);
    overflow: hidden;
}

.file-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    border-bottom: 1px solid var(--drive-gray-200);
    transition: background-color 0.2s ease;
}

.file-item:last-child {
    border-bottom: none;
}

.file-item:hover {
    background: var(--drive-gray-50);
}

.file-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 16px;
    font-size: 16px;
    color: var(--drive-white);
}

.file-icon.pdf {
    background: var(--drive-danger);
}

.file-icon.sketch {
    background: var(--drive-warning);
}

.file-icon.after-effects {
    background: var(--drive-primary);
}

.file-info {
    flex: 1;
}

.file-info h6 {
    font-size: 14px;
    font-weight: 500;
    color: var(--drive-gray-800);
    margin: 0 0 4px 0;
}

.file-meta {
    font-size: 12px;
    color: var(--drive-gray-600);
}

.file-actions {
    display: flex;
    gap: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;
}

.file-item:hover .file-actions {
    opacity: 1;
}

.btn-action {
    background: transparent;
    border: none;
    color: var(--drive-gray-500);
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.btn-action:hover {
    background: var(--drive-gray-100);
    color: var(--drive-gray-700);
}

/* Sidebar */
.drive-sidebar {
    width: 280px;
    flex-shrink: 0;
    background: var(--drive-white);
    border-left: 1px solid var(--drive-gray-200);
    padding: 24px;
    overflow-y: auto;
    overflow-x: hidden;
    height: calc(100vh - 72px);
    max-width: 280px;
}

.sidebar-widget {
    background: var(--drive-white);
    border-radius: var(--drive-border-radius);
    box-shadow: var(--drive-shadow);
    margin-bottom: 16px;
    overflow: hidden;
}

.widget-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid var(--drive-gray-200);
}

.widget-header h5 {
    font-size: 16px;
    font-weight: 500;
    color: var(--drive-gray-800);
    margin: 0;
}

.widget-expand {
    background: transparent;
    border: none;
    color: var(--drive-gray-500);
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.widget-expand:hover {
    background: var(--drive-gray-100);
    color: var(--drive-gray-700);
}

/* Calendar Widget */
.calendar-container {
    padding: 16px 20px;
}

.calendar-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.calendar-nav-btn {
    background: transparent;
    border: none;
    color: var(--drive-gray-600);
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-nav-btn:hover {
    background: var(--drive-gray-100);
    color: var(--drive-gray-800);
}

.calendar-month-year {
    display: flex;
    align-items: center;
    gap: 8px;
}

.month-selector,
.year-selector {
    font-size: 14px;
    font-weight: 500;
    color: var(--drive-gray-800);
}

.calendar-view-toggle {
    display: flex;
    background: var(--drive-gray-100);
    border-radius: 20px;
    padding: 2px;
    margin-bottom: 16px;
}

.calendar-view-btn {
    background: transparent;
    border: none;
    padding: 6px 12px;
    border-radius: 18px;
    font-size: 12px;
    color: var(--drive-gray-600);
    cursor: pointer;
    transition: all 0.2s ease;
}

.calendar-view-btn.active {
    background: var(--drive-white);
    color: var(--drive-primary);
    box-shadow: var(--drive-shadow);
}

.calendar-grid {
    display: flex;
    justify-content: center;
}

.calendar-today {
    text-align: center;
    padding: 16px;
    background: var(--drive-gray-50);
    border-radius: 8px;
    border: 2px solid var(--drive-primary);
}

.today-date {
    font-size: 24px;
    font-weight: 500;
    color: var(--drive-primary);
    margin-bottom: 4px;
}

.today-month {
    font-size: 12px;
    color: var(--drive-gray-600);
    text-transform: uppercase;
}

/* Tasks Widget */
.task-list {
    padding: 16px 20px;
}

.task-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px 0;
    border-bottom: 1px solid var(--drive-gray-200);
}

.task-item:last-child {
    border-bottom: none;
}

.task-checkbox {
    position: relative;
    margin-top: 2px;
}

.task-checkbox input[type="checkbox"] {
    appearance: none;
    width: 16px;
    height: 16px;
    border: 2px solid var(--drive-gray-400);
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.task-checkbox input[type="checkbox"]:checked {
    background: var(--drive-primary);
    border-color: var(--drive-primary);
}

.task-checkbox input[type="checkbox"]:checked::after {
    content: '✓';
    position: absolute;
    top: -1px;
    left: 2px;
    color: var(--drive-white);
    font-size: 12px;
    font-weight: bold;
}

.task-content {
    flex: 1;
}

.task-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--drive-gray-800);
    margin: 0 0 4px 0;
}

.task-time {
    font-size: 12px;
    color: var(--drive-gray-600);
}

.task-menu-btn {
    background: transparent;
    border: none;
    color: var(--drive-gray-500);
    padding: 4px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    opacity: 0;
}

.task-item:hover .task-menu-btn {
    opacity: 1;
}

.task-menu-btn:hover {
    background: var(--drive-gray-100);
    color: var(--drive-gray-700);
}

/* Storage Widget */
.storage-overview {
    padding: 16px 20px;
}

.storage-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.storage-amount {
    display: flex;
    flex-direction: column;
}

.available-space {
    font-size: 12px;
    color: var(--drive-gray-600);
    margin-bottom: 4px;
}

.space-value {
    font-size: 16px;
    font-weight: 500;
    color: var(--drive-gray-800);
}

.storage-selector select {
    background: var(--drive-gray-100);
    border: none;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    color: var(--drive-gray-700);
    cursor: pointer;
    outline: none;
}

.storage-chart {
    margin-bottom: 16px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.storage-visual {
    width: 100%;
}

.storage-bar {
    width: 100%;
    height: 8px;
    background: var(--drive-gray-200);
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.storage-used {
    height: 100%;
    background: linear-gradient(90deg, var(--drive-primary) 0%, var(--drive-secondary) 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
}

.storage-percentage {
    font-size: 12px;
    color: var(--drive-gray-600);
    text-align: center;
}

.storage-legend {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 12px;
    color: var(--drive-gray-600);
}

.legend-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.legend-dot.uploads {
    background: var(--drive-danger);
}

.legend-dot.received {
    background: var(--drive-primary);
}

.legend-dot.space {
    background: var(--drive-secondary);
}

/* Responsive Design */
@media (max-width: 1024px) {
    .drive-main-content {
        flex-direction: column;
        padding: 16px;
        width: 100%;
        max-width: 100%;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .quick-services-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    }

    .drive-sidebar {
        width: 100%;
        max-width: 100%;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
        overflow-x: hidden;
    }

    .sidebar-widget {
        margin-bottom: 0;
    }
}

@media (max-width: 768px) {
    .drive-dashboard-header {
        padding: 8px 16px;
        flex-wrap: wrap;
        height: auto;
        gap: 8px;
    }

    .drive-dashboard-container {
        width: 100vw !important;
        max-width: 100vw !important;
        margin-right: 0 !important;
    }

    .content-wrapper {
        margin-right: 0 !important;
    }

    .main-sidebar {
        transform: translateX(100%);
        transition: transform 0.3s ease;
    }

    .sidebar-open .main-sidebar {
        transform: translateX(0);
    }
}

    .header-left {
        order: 1;
        width: 100%;
    }

    .search-container {
        order: 2;
        width: 100%;
        margin: 8px 0;
    }

    .header-right {
        order: 3;
        width: 100%;
        justify-content: space-between;
    }

    .welcome-content {
        flex-direction: column;
        text-align: center;
        gap: 24px;
    }

    .welcome-illustration {
        width: 150px;
        height: 120px;
    }

    .drive-folders-grid {
        grid-template-columns: 1fr;
    }

    .drive-sidebar {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .drive-main-content {
        padding: 12px;
    }

    .welcome-hero-section {
        padding: 20px;
    }

    .drive-folder-card {
        padding: 16px;
    }

    .file-item {
        padding: 12px 16px;
    }

    .widget-header,
    .calendar-container,
    .task-list,
    .storage-overview {
        padding: 12px 16px;
    }
}

/* Animation and Transitions */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.drive-folder-card,
.file-item,
.sidebar-widget {
    animation: fadeInUp 0.3s ease-out;
}

.drive-folder-card:nth-child(1) { animation-delay: 0.1s; }
.drive-folder-card:nth-child(2) { animation-delay: 0.2s; }
.drive-folder-card:nth-child(3) { animation-delay: 0.3s; }

/* Focus States for Accessibility */
.view-btn:focus,
.upload-btn:focus,
.user-avatar:focus,
.drive-action-btn:focus,
.btn-action:focus,
.calendar-nav-btn:focus,
.task-checkbox input:focus {
    outline: 2px solid var(--drive-primary);
    outline-offset: 2px;
}

/* Brand Logo Styles */
.brand-logo-container {
    display: flex;
    align-items: center;
    gap: 12px;
}

.brand-logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--drive-primary) 0%, var(--drive-secondary) 100%);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--drive-white);
    font-size: 18px;
    box-shadow: 0 2px 8px rgba(26, 115, 232, 0.3);
}

.brand-text {
    color: var(--drive-gray-800) !important;
    font-weight: 500 !important;
    font-size: 16px !important;
}

/* Welcome Section Updates */
.welcome-actions {
    display: flex;
    gap: 12px;
    margin-top: 20px;
}

.btn-primary, .btn-secondary {
    padding: 12px 20px;
    border-radius: 8px;
    border: none;
    font-weight: 500;
    font-size: 14px;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.2s ease;
}

.btn-primary {
    background: var(--drive-primary);
    color: white;
}

.btn-primary:hover {
    background: var(--drive-primary-hover);
    transform: translateY(-1px);
}

.btn-secondary {
    background: var(--drive-gray-100);
    color: var(--drive-gray-700);
}

.btn-secondary:hover {
    background: var(--drive-gray-200);
    transform: translateY(-1px);
}

.code-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--drive-primary) 0%, var(--drive-secondary) 100%);
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 32px;
    margin-bottom: 16px;
    box-shadow: 0 8px 24px rgba(26, 115, 232, 0.3);
}

.tech-icons {
    display: flex;
    gap: 8px;
    justify-content: center;
}

.tech-icon {
    width: 32px;
    height: 32px;
    background: var(--drive-gray-100);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--drive-gray-600);
    font-size: 16px;
}

/* Quick Services Section */
.quick-services-section {
    margin-bottom: 32px;
}

.quick-services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
    margin-top: 16px;
    width: 100%;
    max-width: 100%;
}

.quick-service-card {
    background: var(--drive-white);
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    border: 1px solid var(--drive-gray-200);
    transition: all 0.2s ease;
    cursor: pointer;
}

.quick-service-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--drive-shadow-hover);
    border-color: var(--drive-primary);
}

.service-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 12px;
    font-size: 20px;
    color: white;
}

.service-icon.new-project {
    background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
}

.service-icon.quote {
    background: linear-gradient(135deg, #ea4335 0%, #fbbc04 100%);
}

.service-icon.client {
    background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
}

.service-icon.report {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.quick-service-card h6 {
    font-size: 14px;
    font-weight: 600;
    color: var(--drive-gray-800);
    margin: 0 0 8px 0;
}

.quick-service-card p {
    font-size: 12px;
    color: var(--drive-gray-600);
    margin: 0;
    line-height: 1.4;
}

/* Statistics Widget */
.stats-widget {
    margin-bottom: 24px;
}

.stats-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 12px;
    margin-top: 16px;
}

.stat-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    background: var(--drive-gray-50);
    border-radius: 8px;
    border: 1px solid var(--drive-gray-200);
}

.stat-icon {
    width: 32px;
    height: 32px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    color: white;
    flex-shrink: 0;
}

.stat-icon.active-projects {
    background: linear-gradient(135deg, var(--drive-primary) 0%, var(--drive-secondary) 100%);
}

.stat-icon.completed-projects {
    background: linear-gradient(135deg, var(--drive-secondary) 0%, #2e7d32 100%);
}

.stat-icon.total-clients {
    background: linear-gradient(135deg, #9c27b0 0%, #673ab7 100%);
}

.stat-icon.monthly-revenue {
    background: linear-gradient(135deg, var(--drive-warning) 0%, #f57c00 100%);
}

.stat-info {
    display: flex;
    flex-direction: column;
    min-width: 0;
}

.stat-number {
    font-size: 16px;
    font-weight: 600;
    color: var(--drive-gray-800);
    line-height: 1.2;
}

.stat-label {
    font-size: 11px;
    color: var(--drive-gray-600);
    line-height: 1.2;
}

/* Recent Projects Section */
.recent-projects-section {
    margin-bottom: 32px;
}

.recent-projects-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 16px;
    margin-top: 16px;
    width: 100%;
    max-width: 100%;
}

.project-timeline-card {
    background: var(--drive-white);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid var(--drive-gray-200);
    transition: all 0.2s ease;
    display: flex;
    gap: 16px;
}

.project-timeline-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--drive-shadow-hover);
    border-color: var(--drive-primary);
}

.project-status {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    color: white;
    flex-shrink: 0;
}

.project-status.in-progress {
    background: linear-gradient(135deg, var(--drive-primary) 0%, var(--drive-secondary) 100%);
}

.project-status.completed {
    background: linear-gradient(135deg, var(--drive-secondary) 0%, #2e7d32 100%);
}

.project-status.pending {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
}

.project-details {
    flex: 1;
    min-width: 0;
}

.project-details h6 {
    font-size: 14px;
    font-weight: 600;
    color: var(--drive-gray-800);
    margin: 0 0 4px 0;
    line-height: 1.3;
}

.project-details p {
    font-size: 12px;
    color: var(--drive-gray-600);
    margin: 0 0 8px 0;
}

.project-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    gap: 8px;
}

.project-tech {
    font-size: 11px;
    background: var(--drive-gray-100);
    color: var(--drive-gray-700);
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: 500;
}

.project-deadline {
    font-size: 11px;
    color: var(--drive-gray-600);
    font-weight: 500;
}

.project-progress {
    display: flex;
    align-items: center;
    gap: 8px;
}

.project-progress .progress-bar {
    flex: 1;
    height: 6px;
    background: var(--drive-gray-200);
    border-radius: 3px;
    overflow: hidden;
}

.project-progress .progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--drive-primary) 0%, var(--drive-secondary) 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
}

.project-progress .progress-fill.completed {
    background: linear-gradient(90deg, var(--drive-secondary) 0%, #2e7d32 100%);
}

.project-progress .progress-text {
    font-size: 11px;
    color: var(--drive-gray-600);
    font-weight: 600;
    min-width: 30px;
    text-align: right;
}

/* Print Styles */
@media print {
    .drive-dashboard-header,
    .header-actions,
    .drive-action-btn,
    .file-actions,
    .task-menu-btn,
    .widget-expand {
        display: none !important;
    }

    .drive-main-content {
        flex-direction: column;
        padding: 0;
    }

    .drive-sidebar {
        width: 100%;
    }

    .welcome-hero-section,
    .drive-folder-card,
    .sidebar-widget {
        box-shadow: none !important;
        border: 1px solid var(--drive-gray-300) !important;
    }
}

/* Calendar Styles */
.calendar-container {
    padding: 0;
}

.calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 0 4px;
}

.calendar-nav {
    background: none;
    border: none;
    color: var(--drive-gray-600);
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.calendar-nav:hover {
    background: var(--drive-gray-100);
    color: var(--drive-primary);
}

.calendar-month {
    font-size: 14px;
    font-weight: 600;
    color: var(--drive-gray-800);
    margin: 0;
}

.calendar-grid {
    width: 100%;
}

.calendar-weekdays {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
    margin-bottom: 8px;
}

.calendar-weekdays span {
    text-align: center;
    font-size: 11px;
    font-weight: 600;
    color: var(--drive-gray-600);
    padding: 4px 0;
}

.calendar-days {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: var(--drive-gray-700);
    cursor: pointer;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.calendar-day:hover {
    background: var(--drive-gray-100);
}

.calendar-day.today {
    background: var(--drive-primary);
    color: var(--drive-white);
    font-weight: 600;
}

.calendar-day.other-month {
    color: var(--drive-gray-400);
    opacity: 0.5;
}

.calendar-events {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid var(--drive-gray-200);
}

.event-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    padding: 4px 0;
}

.event-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--drive-primary);
    flex-shrink: 0;
}

.event-dot.secondary {
    background: var(--drive-secondary);
}

.event-text {
    font-size: 12px;
    color: var(--drive-gray-700);
    line-height: 1.4;
}
