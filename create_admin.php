<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

use App\Domains\Auth\Models\User;
use Illuminate\Support\Facades\Hash;

try {
    $user = new User();
    $user->name = 'المدير العام';
    $user->email = '<EMAIL>';
    $user->password = Hash::make('admin123');
    $user->email_verified_at = now();
    $user->active = 1;
    $user->save();
    
    echo "Admin user created successfully!\n";
    echo "Email: <EMAIL>\n";
    echo "Password: admin123\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
