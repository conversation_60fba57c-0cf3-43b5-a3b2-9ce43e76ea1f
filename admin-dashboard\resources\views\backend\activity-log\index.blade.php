@extends('backend.layouts.app')

@section('title', 'سجل النشاط')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-history me-2 text-primary"></i>
                        سجل النشاط
                    </h1>
                    <p class="text-muted mb-0">تتبع جميع الأنشطة والعمليات في النظام</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-danger" onclick="clearOldLogs()">
                        <i class="fas fa-trash me-2"></i>
                        مسح السجلات القديمة
                    </button>
                    <button class="btn btn-outline-primary" onclick="exportLogs()">
                        <i class="fas fa-download me-2"></i>
                        تصدير السجل
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row align-items-end">
                        <div class="col-md-2">
                            <label class="form-label">من تاريخ</label>
                            <input type="date" class="form-control" id="startDate" value="{{ now()->subDays(7)->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">إلى تاريخ</label>
                            <input type="date" class="form-control" id="endDate" value="{{ now()->format('Y-m-d') }}">
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">نوع النشاط</label>
                            <select class="form-select" id="activityType">
                                <option value="">جميع الأنشطة</option>
                                <option value="login">تسجيل دخول</option>
                                <option value="logout">تسجيل خروج</option>
                                <option value="create">إنشاء</option>
                                <option value="update">تحديث</option>
                                <option value="delete">حذف</option>
                                <option value="view">عرض</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">المستخدم</label>
                            <select class="form-select" id="userId">
                                <option value="">جميع المستخدمين</option>
                                <option value="1">أحمد محمد</option>
                                <option value="2">فاطمة علي</option>
                                <option value="3">محمد حسن</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label">مستوى الأهمية</label>
                            <select class="form-select" id="logLevel">
                                <option value="">جميع المستويات</option>
                                <option value="info">معلومات</option>
                                <option value="warning">تحذير</option>
                                <option value="error">خطأ</option>
                                <option value="critical">حرج</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button class="btn btn-primary w-100" onclick="filterLogs()">
                                <i class="fas fa-filter me-2"></i>
                                تطبيق الفلتر
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">إجمالي الأنشطة</h6>
                            <h3 class="mb-0">15,420</h3>
                        </div>
                        <div class="activity-icon">
                            <i class="fas fa-chart-line fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">أنشطة اليوم</h6>
                            <h3 class="mb-0">247</h3>
                        </div>
                        <div class="activity-icon">
                            <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">تحذيرات</h6>
                            <h3 class="mb-0">12</h3>
                        </div>
                        <div class="activity-icon">
                            <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-danger text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">أخطاء</h6>
                            <h3 class="mb-0">3</h3>
                        </div>
                        <div class="activity-icon">
                            <i class="fas fa-times-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Activity Timeline -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-stream text-primary me-2"></i>
                            سجل الأنشطة
                        </h5>
                        <div class="d-flex gap-2">
                            <div class="input-group input-group-sm" style="width: 250px;">
                                <input type="text" class="form-control" placeholder="البحث في السجل..." id="logSearch">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshLogs()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="activity-timeline-container" style="max-height: 600px; overflow-y: auto;">
                        <div class="activity-timeline p-4">
                            <!-- Activity Item -->
                            <div class="activity-item" data-level="info">
                                <div class="activity-marker bg-success">
                                    <i class="fas fa-sign-in-alt"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-header">
                                        <h6 class="activity-title">تسجيل دخول ناجح</h6>
                                        <span class="activity-time">منذ 5 دقائق</span>
                                    </div>
                                    <div class="activity-description">
                                        <p>قام المستخدم <strong>أحمد محمد</strong> بتسجيل الدخول بنجاح</p>
                                        <div class="activity-details">
                                            <span class="badge bg-light text-dark me-2">IP: *************</span>
                                            <span class="badge bg-light text-dark me-2">المتصفح: Chrome</span>
                                            <span class="badge bg-light text-dark">الجهاز: Desktop</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Item -->
                            <div class="activity-item" data-level="info">
                                <div class="activity-marker bg-primary">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-header">
                                        <h6 class="activity-title">إنشاء مستخدم جديد</h6>
                                        <span class="activity-time">منذ 15 دقيقة</span>
                                    </div>
                                    <div class="activity-description">
                                        <p>تم إنشاء مستخدم جديد بواسطة <strong>المدير العام</strong></p>
                                        <div class="activity-details">
                                            <span class="badge bg-light text-dark me-2">المستخدم: فاطمة علي</span>
                                            <span class="badge bg-light text-dark me-2">البريد: <EMAIL></span>
                                            <span class="badge bg-light text-dark">الدور: مستخدم</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Item -->
                            <div class="activity-item" data-level="warning">
                                <div class="activity-marker bg-warning">
                                    <i class="fas fa-exclamation-triangle"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-header">
                                        <h6 class="activity-title">محاولة دخول فاشلة</h6>
                                        <span class="activity-time">منذ 30 دقيقة</span>
                                    </div>
                                    <div class="activity-description">
                                        <p>محاولة تسجيل دخول فاشلة للمستخدم <strong><EMAIL></strong></p>
                                        <div class="activity-details">
                                            <span class="badge bg-light text-dark me-2">IP: ************</span>
                                            <span class="badge bg-light text-dark me-2">المحاولة: 3/5</span>
                                            <span class="badge bg-warning text-dark">السبب: كلمة مرور خاطئة</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Item -->
                            <div class="activity-item" data-level="info">
                                <div class="activity-marker bg-info">
                                    <i class="fas fa-edit"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-header">
                                        <h6 class="activity-title">تحديث الإعدادات</h6>
                                        <span class="activity-time">منذ ساعة</span>
                                    </div>
                                    <div class="activity-description">
                                        <p>تم تحديث إعدادات النظام بواسطة <strong>المدير التقني</strong></p>
                                        <div class="activity-details">
                                            <span class="badge bg-light text-dark me-2">القسم: الأمان</span>
                                            <span class="badge bg-light text-dark me-2">التغيير: تفعيل 2FA</span>
                                            <span class="badge bg-light text-dark">الحالة: مطبق</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Item -->
                            <div class="activity-item" data-level="error">
                                <div class="activity-marker bg-danger">
                                    <i class="fas fa-times-circle"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-header">
                                        <h6 class="activity-title">خطأ في النظام</h6>
                                        <span class="activity-time">منذ ساعتين</span>
                                    </div>
                                    <div class="activity-description">
                                        <p>حدث خطأ في الاتصال بقاعدة البيانات</p>
                                        <div class="activity-details">
                                            <span class="badge bg-danger text-white me-2">الكود: DB_CONNECTION_ERROR</span>
                                            <span class="badge bg-light text-dark me-2">الخادم: db-server-01</span>
                                            <span class="badge bg-light text-dark">المدة: 2 ثانية</span>
                                        </div>
                                        <div class="activity-actions mt-2">
                                            <button class="btn btn-sm btn-outline-primary" onclick="viewErrorDetails()">
                                                عرض التفاصيل
                                            </button>
                                            <button class="btn btn-sm btn-outline-success" onclick="markAsResolved()">
                                                تم الحل
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Activity Item -->
                            <div class="activity-item" data-level="info">
                                <div class="activity-marker bg-secondary">
                                    <i class="fas fa-sign-out-alt"></i>
                                </div>
                                <div class="activity-content">
                                    <div class="activity-header">
                                        <h6 class="activity-title">تسجيل خروج</h6>
                                        <span class="activity-time">منذ 3 ساعات</span>
                                    </div>
                                    <div class="activity-description">
                                        <p>قام المستخدم <strong>محمد حسن</strong> بتسجيل الخروج</p>
                                        <div class="activity-details">
                                            <span class="badge bg-light text-dark me-2">مدة الجلسة: 2 ساعة 15 دقيقة</span>
                                            <span class="badge bg-light text-dark me-2">الصفحات: 45</span>
                                            <span class="badge bg-light text-dark">النوع: تلقائي</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Load More Button -->
                            <div class="text-center mt-4">
                                <button class="btn btn-outline-primary" onclick="loadMoreLogs()">
                                    <i class="fas fa-chevron-down me-2"></i>
                                    تحميل المزيد
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Error Details Modal -->
<x-backend.advanced-modal 
    id="errorDetailsModal"
    title="تفاصيل الخطأ"
    size="modal-lg"
    :centered="true">
    
    <div id="errorDetailsContent">
        <!-- Error details will be loaded here -->
    </div>
    
    <x-slot name="footerLeft">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
    </x-slot>
    
    <button type="button" class="btn btn-primary" onclick="downloadErrorLog()">
        <i class="fas fa-download me-2"></i>
        تحميل سجل الخطأ
    </button>
</x-backend.advanced-modal>
@endsection

@push('after-styles')
<style>
.activity-icon {
    opacity: 0.7;
}

.activity-timeline {
    position: relative;
    padding-right: 30px;
}

.activity-timeline::before {
    content: '';
    position: absolute;
    right: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: linear-gradient(to bottom, #e9ecef 0%, #e9ecef 100%);
}

.activity-item {
    position: relative;
    margin-bottom: 30px;
    display: flex;
    align-items: flex-start;
    transition: all 0.3s ease;
}

.activity-item:hover {
    transform: translateX(-5px);
}

.activity-marker {
    position: absolute;
    right: -22px;
    top: 5px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    z-index: 2;
}

.activity-content {
    background: #fff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    width: 100%;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.activity-item:hover .activity-content {
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
    border-color: #667eea;
}

.activity-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 0.75rem;
}

.activity-title {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    flex: 1;
}

.activity-time {
    font-size: 0.75rem;
    color: #6b7280;
    background: #f3f4f6;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    white-space: nowrap;
}

.activity-description {
    color: #6b7280;
    font-size: 0.875rem;
    line-height: 1.5;
}

.activity-description p {
    margin-bottom: 0.75rem;
}

.activity-details {
    margin-bottom: 0.75rem;
}

.activity-details .badge {
    font-size: 0.7rem;
    padding: 0.35em 0.65em;
    margin-bottom: 0.25rem;
}

.activity-actions {
    border-top: 1px solid #f1f3f4;
    padding-top: 0.75rem;
}

.activity-actions .btn {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
    margin-left: 0.5rem;
}

/* Level-based styling */
.activity-item[data-level="info"] .activity-content {
    border-right: 4px solid #3b82f6;
}

.activity-item[data-level="warning"] .activity-content {
    border-right: 4px solid #f59e0b;
}

.activity-item[data-level="error"] .activity-content {
    border-right: 4px solid #ef4444;
}

.activity-item[data-level="critical"] .activity-content {
    border-right: 4px solid #dc2626;
    background: rgba(239, 68, 68, 0.02);
}

/* Filter animations */
.activity-item.filtered-out {
    opacity: 0.3;
    transform: scale(0.95);
    pointer-events: none;
}

.activity-item.highlighted {
    animation: highlight 2s ease-in-out;
}

@keyframes highlight {
    0%, 100% { background: transparent; }
    50% { background: rgba(102, 126, 234, 0.1); }
}

/* Loading states */
.activity-timeline-container.loading {
    position: relative;
    overflow: hidden;
}

.activity-timeline-container.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.8), transparent);
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Responsive design */
@media (max-width: 768px) {
    .activity-timeline {
        padding-right: 20px;
    }

    .activity-timeline::before {
        right: 10px;
    }

    .activity-marker {
        right: -15px;
        width: 30px;
        height: 30px;
        font-size: 0.75rem;
    }

    .activity-content {
        padding: 1rem;
    }

    .activity-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }

    .activity-time {
        align-self: flex-end;
    }

    .activity-details .badge {
        display: block;
        margin-bottom: 0.25rem;
        width: fit-content;
    }

    .activity-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }

    .activity-actions .btn {
        flex: 1;
        min-width: 120px;
        margin: 0;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .activity-content {
        background: #1f2937;
        border-color: #374151;
        color: #e5e7eb;
    }

    .activity-title {
        color: #f9fafb;
    }

    .activity-description {
        color: #d1d5db;
    }

    .activity-time {
        background: #374151;
        color: #d1d5db;
    }

    .activity-timeline::before {
        background: #374151;
    }
}

/* Print styles */
@media print {
    .activity-timeline-container {
        max-height: none !important;
        overflow: visible !important;
    }

    .activity-marker {
        box-shadow: none;
        border: 2px solid #000;
    }

    .activity-content {
        box-shadow: none;
        border: 1px solid #000;
    }

    .activity-actions {
        display: none;
    }

    .btn {
        display: none;
    }
}
</style>
@endpush

@push('after-scripts')
<script>
let currentPage = 1;
let isLoading = false;
let filters = {};

document.addEventListener('DOMContentLoaded', function() {
    initializeActivityLog();
    setupEventListeners();
    setupRealTimeUpdates();
});

function initializeActivityLog() {
    // Setup search functionality
    setupSearch();

    // Setup infinite scroll
    setupInfiniteScroll();

    // Setup keyboard shortcuts
    setupKeyboardShortcuts();
}

function setupEventListeners() {
    // Filter change events
    ['startDate', 'endDate', 'activityType', 'userId', 'logLevel'].forEach(id => {
        document.getElementById(id).addEventListener('change', updateFilters);
    });

    // Search input
    document.getElementById('logSearch').addEventListener('input', debounce(handleSearch, 300));
}

function setupSearch() {
    const searchInput = document.getElementById('logSearch');

    searchInput.addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const activityItems = document.querySelectorAll('.activity-item');

        activityItems.forEach(item => {
            const content = item.textContent.toLowerCase();
            if (content.includes(searchTerm)) {
                item.classList.remove('filtered-out');
                if (searchTerm && searchTerm.length > 2) {
                    item.classList.add('highlighted');
                    setTimeout(() => item.classList.remove('highlighted'), 2000);
                }
            } else {
                item.classList.add('filtered-out');
            }
        });

        // Update results count
        const visibleItems = document.querySelectorAll('.activity-item:not(.filtered-out)').length;
        updateResultsCount(visibleItems);
    });
}

function setupInfiniteScroll() {
    const container = document.querySelector('.activity-timeline-container');

    container.addEventListener('scroll', function() {
        if (this.scrollTop + this.clientHeight >= this.scrollHeight - 100) {
            loadMoreLogs();
        }
    });
}

function setupKeyboardShortcuts() {
    document.addEventListener('keydown', function(e) {
        if (e.ctrlKey || e.metaKey) {
            switch(e.key) {
                case 'f':
                    e.preventDefault();
                    document.getElementById('logSearch').focus();
                    break;
                case 'r':
                    e.preventDefault();
                    refreshLogs();
                    break;
                case 'e':
                    e.preventDefault();
                    exportLogs();
                    break;
            }
        }

        // ESC to clear search
        if (e.key === 'Escape') {
            document.getElementById('logSearch').value = '';
            handleSearch();
        }
    });
}

function setupRealTimeUpdates() {
    // Check for new activities every 30 seconds
    setInterval(() => {
        checkForNewActivities();
    }, 30000);
}

function updateFilters() {
    filters = {
        startDate: document.getElementById('startDate').value,
        endDate: document.getElementById('endDate').value,
        activityType: document.getElementById('activityType').value,
        userId: document.getElementById('userId').value,
        logLevel: document.getElementById('logLevel').value
    };
}

function filterLogs() {
    updateFilters();

    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التطبيق...';
    btn.disabled = true;

    // Show loading state
    const container = document.querySelector('.activity-timeline-container');
    container.classList.add('loading');

    // Simulate API call
    setTimeout(() => {
        // Apply filters to existing items
        applyFiltersToItems();

        // Reset page and load new data
        currentPage = 1;
        loadFilteredLogs();

        container.classList.remove('loading');
        btn.innerHTML = originalText;
        btn.disabled = false;

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم تطبيق الفلاتر بنجاح');
        }
    }, 1500);
}

function applyFiltersToItems() {
    const activityItems = document.querySelectorAll('.activity-item');

    activityItems.forEach(item => {
        let shouldShow = true;

        // Apply level filter
        if (filters.logLevel) {
            const itemLevel = item.dataset.level;
            if (itemLevel !== filters.logLevel) {
                shouldShow = false;
            }
        }

        // Apply other filters...
        // (In a real implementation, you would check against actual data)

        if (shouldShow) {
            item.classList.remove('filtered-out');
        } else {
            item.classList.add('filtered-out');
        }
    });
}

function loadFilteredLogs() {
    // Simulate loading filtered data
    console.log('Loading filtered logs with filters:', filters);
}

function handleSearch() {
    const searchTerm = document.getElementById('logSearch').value.toLowerCase();
    const activityItems = document.querySelectorAll('.activity-item');
    let visibleCount = 0;

    activityItems.forEach(item => {
        const content = item.textContent.toLowerCase();
        if (!searchTerm || content.includes(searchTerm)) {
            item.classList.remove('filtered-out');
            visibleCount++;
        } else {
            item.classList.add('filtered-out');
        }
    });

    updateResultsCount(visibleCount);
}

function updateResultsCount(count) {
    // Update results count display
    let countDisplay = document.getElementById('resultsCount');
    if (!countDisplay) {
        countDisplay = document.createElement('div');
        countDisplay.id = 'resultsCount';
        countDisplay.className = 'text-muted small mt-2';
        document.querySelector('.card-header').appendChild(countDisplay);
    }

    countDisplay.textContent = `عرض ${count} نتيجة`;
}

function refreshLogs() {
    const btn = event.target;
    const originalClass = btn.className;

    btn.className = 'btn btn-sm btn-outline-primary';
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
    btn.disabled = true;

    // Simulate refresh
    setTimeout(() => {
        // Reset timeline
        currentPage = 1;
        loadNewLogs();

        btn.className = originalClass;
        btn.innerHTML = '<i class="fas fa-sync-alt"></i>';
        btn.disabled = false;

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم تحديث السجل بنجاح');
        }
    }, 1500);
}

function loadMoreLogs() {
    if (isLoading) return;

    isLoading = true;
    currentPage++;

    // Show loading indicator
    const loadMoreBtn = document.querySelector('[onclick="loadMoreLogs()"]');
    if (loadMoreBtn) {
        loadMoreBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التحميل...';
        loadMoreBtn.disabled = true;
    }

    // Simulate API call
    setTimeout(() => {
        // Add new activity items
        addNewActivityItems();

        if (loadMoreBtn) {
            loadMoreBtn.innerHTML = '<i class="fas fa-chevron-down me-2"></i>تحميل المزيد';
            loadMoreBtn.disabled = false;
        }

        isLoading = false;
    }, 1000);
}

function addNewActivityItems() {
    const timeline = document.querySelector('.activity-timeline');
    const loadMoreBtn = timeline.querySelector('[onclick="loadMoreLogs()"]').parentElement;

    // Sample new activities
    const newActivities = [
        {
            type: 'info',
            icon: 'fas fa-file-upload',
            title: 'رفع ملف جديد',
            description: 'تم رفع ملف "تقرير_شهري.pdf" بواسطة <strong>سارة أحمد</strong>',
            time: 'منذ 4 ساعات',
            details: ['الحجم: 2.4 MB', 'النوع: PDF', 'المجلد: التقارير']
        },
        {
            type: 'warning',
            icon: 'fas fa-shield-alt',
            title: 'تحديث كلمة المرور',
            description: 'قام المستخدم <strong>خالد محمد</strong> بتغيير كلمة المرور',
            time: 'منذ 5 ساعات',
            details: ['الطريقة: تلقائي', 'القوة: قوية', 'آخر تغيير: منذ 90 يوم']
        }
    ];

    newActivities.forEach(activity => {
        const activityHTML = createActivityHTML(activity);
        loadMoreBtn.insertAdjacentHTML('beforebegin', activityHTML);
    });

    // Animate new items
    const newItems = timeline.querySelectorAll('.activity-item:not(.animated)');
    newItems.forEach((item, index) => {
        item.classList.add('animated');
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';

        setTimeout(() => {
            item.style.transition = 'all 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

function createActivityHTML(activity) {
    const levelColors = {
        'info': 'bg-primary',
        'warning': 'bg-warning',
        'error': 'bg-danger',
        'success': 'bg-success'
    };

    const detailsBadges = activity.details.map(detail =>
        `<span class="badge bg-light text-dark me-2">${detail}</span>`
    ).join('');

    return `
        <div class="activity-item" data-level="${activity.type}">
            <div class="activity-marker ${levelColors[activity.type]}">
                <i class="${activity.icon}"></i>
            </div>
            <div class="activity-content">
                <div class="activity-header">
                    <h6 class="activity-title">${activity.title}</h6>
                    <span class="activity-time">${activity.time}</span>
                </div>
                <div class="activity-description">
                    <p>${activity.description}</p>
                    <div class="activity-details">
                        ${detailsBadges}
                    </div>
                </div>
            </div>
        </div>
    `;
}

function loadNewLogs() {
    // Simulate loading new logs
    console.log('Loading new logs...');
}

function checkForNewActivities() {
    // Check for new activities in the background
    fetch('/admin/activity-log/check-new')
        .then(response => response.json())
        .then(data => {
            if (data.hasNew) {
                showNewActivityNotification(data.count);
            }
        })
        .catch(error => {
            console.error('Error checking for new activities:', error);
        });
}

function showNewActivityNotification(count) {
    const notification = document.createElement('div');
    notification.className = 'alert alert-info alert-dismissible fade show position-fixed';
    notification.style.cssText = 'top: 20px; left: 50%; transform: translateX(-50%); z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        <i class="fas fa-bell me-2"></i>
        يوجد ${count} نشاط جديد
        <button class="btn btn-sm btn-outline-primary ms-2" onclick="refreshLogs()">تحديث</button>
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove after 10 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 10000);
}

function exportLogs() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...';
    btn.disabled = true;

    // Simulate export
    setTimeout(() => {
        // Create and download file
        const logData = generateLogExport();
        downloadLogFile(logData);

        btn.innerHTML = originalText;
        btn.disabled = false;

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم تصدير السجل بنجاح');
        }
    }, 2000);
}

function generateLogExport() {
    const activities = document.querySelectorAll('.activity-item:not(.filtered-out)');
    const exportData = [];

    activities.forEach(activity => {
        const title = activity.querySelector('.activity-title').textContent;
        const time = activity.querySelector('.activity-time').textContent;
        const description = activity.querySelector('.activity-description p').textContent;
        const level = activity.dataset.level;

        exportData.push({
            timestamp: time,
            level: level,
            title: title,
            description: description
        });
    });

    return exportData;
}

function downloadLogFile(data) {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `activity_log_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function clearOldLogs() {
    if (confirm('هل أنت متأكد من حذف السجلات القديمة؟ هذا الإجراء لا يمكن التراجع عنه.')) {
        const btn = event.target;
        const originalText = btn.innerHTML;

        btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الحذف...';
        btn.disabled = true;

        // Simulate deletion
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.disabled = false;

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم حذف السجلات القديمة بنجاح');
            }

            // Refresh the log
            refreshLogs();
        }, 3000);
    }
}

function viewErrorDetails() {
    const modal = new bootstrap.Modal(document.getElementById('errorDetailsModal'));
    const content = document.getElementById('errorDetailsContent');

    // Show loading
    content.innerHTML = '<div class="text-center"><div class="loading-spinner"></div><p>جاري تحميل التفاصيل...</p></div>';

    modal.show();

    // Simulate loading error details
    setTimeout(() => {
        content.innerHTML = `
            <div class="error-details">
                <h6 class="text-danger mb-3">تفاصيل الخطأ</h6>
                <div class="row">
                    <div class="col-md-6">
                        <strong>نوع الخطأ:</strong> Database Connection Error
                    </div>
                    <div class="col-md-6">
                        <strong>الكود:</strong> DB_CONNECTION_ERROR
                    </div>
                    <div class="col-md-6">
                        <strong>الوقت:</strong> 2024-01-15 14:30:25
                    </div>
                    <div class="col-md-6">
                        <strong>المدة:</strong> 2.3 ثانية
                    </div>
                </div>
                <hr>
                <h6>Stack Trace:</h6>
                <pre class="bg-light p-3 rounded"><code>
PDOException: SQLSTATE[HY000] [2002] Connection refused
    at /var/www/html/vendor/laravel/framework/src/Database/Connectors/Connector.php:70
    at PDO->__construct()
    at Illuminate\\Database\\Connectors\\Connector->createConnection()
    at Illuminate\\Database\\Connectors\\MySqlConnector->connect()
                </code></pre>
            </div>
        `;
    }, 1000);
}

function downloadErrorLog() {
    // Simulate downloading error log
    const errorData = {
        error: 'Database Connection Error',
        code: 'DB_CONNECTION_ERROR',
        timestamp: '2024-01-15 14:30:25',
        stack_trace: 'PDOException: SQLSTATE[HY000] [2002] Connection refused...'
    };

    const blob = new Blob([JSON.stringify(errorData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `error_log_${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

function markAsResolved() {
    if (confirm('هل تريد تمييز هذا الخطأ كمحلول؟')) {
        const btn = event.target;
        btn.innerHTML = '<i class="fas fa-check me-1"></i>تم الحل';
        btn.className = 'btn btn-sm btn-success';
        btn.disabled = true;

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم تمييز الخطأ كمحلول');
        }
    }
}

// Utility function for debouncing
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
</script>
@endpush
