<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class EmailProvider extends Model
{
    protected $fillable = [
        'name',
        'type',
        'api_endpoint',
        'api_key',
        'api_secret',
        'api_config',
        'white_label_url',
        'commission_rate',
        'is_active',
        'supported_features',
        'notes',
    ];

    protected $casts = [
        'api_config' => 'array',
        'supported_features' => 'array',
        'commission_rate' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }
}
