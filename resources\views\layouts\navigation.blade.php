<!-- Navigation -->
<nav class="navbar bg-white/95 backdrop-blur-md shadow-lg fixed w-full top-0 z-50 border-b border-gray-100" x-data="{ mobileMenuOpen: false }" @click.away="mobileMenuOpen = false">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
            <!-- Logo -->
            <div class="flex-shrink-0">
                <a href="{{ route('home') }}" class="flex items-center group">
                    <div class="relative">
                        <img src="{{ asset('images/awre.jpg') }}" alt="ميتاء تك للحلول البرمجة وتصميم المواقع والتطبيقات" class="h-10 w-10 ml-3 transition-transform group-hover:scale-110">
                        <div class="absolute inset-0 bg-gradient-to-r from-purple-600 to-blue-600 rounded-full opacity-0 group-hover:opacity-20 transition-opacity"></div>
                    </div>
                    <div class="flex flex-col">
                        <span class="text-lg font-bold bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent leading-tight">ميتاء تك</span>
                        <span class="text-xs text-gray-500 leading-tight">للحلول البرمجة وتصميم المواقع والتطبيقات</span>
                    </div>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden lg:flex items-center space-x-4 space-x-reverse">
                <!-- Home -->
                <a href="{{ route('home') }}" class="text-gray-800 hover:text-purple-600 font-medium py-2 px-4 transition-all duration-300">
                    الرئيسية
                </a>

                <!-- Services -->
                <a href="{{ route('services') }}" class="text-gray-800 hover:text-purple-600 font-medium py-2 px-4 transition-all duration-300">
                    الخدمات
                </a>






                <!-- Explore Dropdown -->
                <div class="relative group">
                    <button class="flex items-center gap-1 text-gray-800 hover:text-purple-600 font-medium py-2 px-4">
                        <svg class="w-4 h-4 transform rotate-90 group-hover:rotate-0 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                        استكشف
                    </button>

                    <!-- القائمة المنسدلة -->
                    <div class="absolute right-0 mt-2 w-52 bg-white shadow-lg rounded-lg z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 text-right">
                        <a href="{{ route('portfolio') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">معرض الأعمال</a>
                        <a href="{{ route('about') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">من نحن</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">شهادات العملاء</a>
                        <a href="{{ route('blog.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">المدونة</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">الشراكات</a>
                    </div>
                </div>

                <!-- Hosting Link -->
                <a href="{{ route('hosting.index') }}" class="text-gray-800 hover:text-purple-600 font-medium py-2 px-4 transition-all duration-300">
                    الاستضافة
                </a>

                <!-- Domains Dropdown -->
                <div class="relative group">
                    <button class="flex items-center gap-1 text-gray-800 hover:text-purple-600 font-medium py-2 px-4">
                        <svg class="w-4 h-4 transform rotate-90 group-hover:rotate-0 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                        الدومينات
                    </button>

                    <!-- القائمة المنسدلة -->
                    <div class="absolute right-0 mt-2 w-56 bg-white shadow-lg rounded-lg z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 text-right">
                        <a href="{{ route('domains.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                            <i class="fas fa-plus-circle ml-2 text-sm"></i>احصل على دومين جديد
                        </a>
                        <a href="{{ route('domains.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                            <i class="fas fa-globe ml-2 text-sm"></i>البحث عن دومين
                        </a>
                        <!-- Transfer Link Updated -->
                        <a href="#" onclick="alert('خدمة نقل الدومين ستكون متاحة قريباً')" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                            <i class="fas fa-exchange-alt ml-2 text-sm"></i>التحويلات
                        </a>
                        <a href="{{ route('domains.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                            <i class="fas fa-cog ml-2 text-sm"></i>إدارة DNS
                        </a>
                        <a href="{{ route('domains.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                            <i class="fas fa-shield-alt ml-2 text-sm"></i>حماية الخصوصية
                        </a>
                        <a href="{{ route('domains.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">
                            <i class="fas fa-tag ml-2 text-sm"></i>أسعار الدومينات
                        </a>
                    </div>
                </div>

                <!-- Email Dropdown -->
                <div class="relative group">
                    <button class="flex items-center gap-1 text-gray-800 hover:text-purple-600 font-medium py-2 px-4">
                        <svg class="w-4 h-4 transform rotate-90 group-hover:rotate-0 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                        البريد
                    </button>

                    <!-- القائمة المنسدلة -->
                    <div class="absolute right-0 mt-2 w-56 bg-white shadow-lg rounded-lg z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 text-right">
                        <a href="{{ route('email.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">البريد الاحترافي</a>
                        <a href="{{ route('email.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">Microsoft 365</a>
                        <a href="{{ route('email.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">Google Workspace</a>
                        <a href="{{ route('email.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">حماية البريد الإلكتروني</a>
                        <a href="{{ route('email.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">أرشفة الرسائل</a>
                        <a href="{{ route('email.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">جميع خدمات البريد</a>
                    </div>
                </div>

                <!-- Pricing -->
                <a href="{{ route('hosting.index') }}" class="text-gray-800 hover:text-purple-600 font-medium py-2 px-4 transition-all duration-300">
                    الأسعار
                </a>

                <!-- Support Dropdown -->
                <div class="relative group">
                    <button class="flex items-center gap-1 text-gray-800 hover:text-purple-600 font-medium py-2 px-4">
                        <svg class="w-4 h-4 transform rotate-90 group-hover:rotate-0 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                        </svg>
                        الدعم
                    </button>

                    <!-- القائمة المنسدلة -->
                    <div class="absolute right-0 mt-2 w-48 bg-white shadow-lg rounded-lg z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 text-right">
                        <a href="{{ route('contact') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">اتصل بنا</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">الأسئلة الشائعة</a>
                        <a href="https://wa.me/967737662752" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">واتساب</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600">مركز المساعدة</a>
                    </div>
                </div>
            </div>

            <!-- User Authentication -->
            <div class="hidden lg:flex items-center">
                @auth
                    <!-- User Dropdown -->
                    <div class="relative group">
                        <button class="flex items-center gap-2 text-gray-700 hover:text-purple-600 px-4 py-2 text-sm font-medium transition-all duration-300 border border-gray-300 hover:border-purple-600 rounded-lg hover:bg-purple-50">
                            <i class="fas fa-user-circle text-lg"></i>
                            <span>{{ Auth::user()->name }}</span>
                            <svg class="w-4 h-4 transform group-hover:rotate-180 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                        </button>

                        <!-- User Dropdown Menu -->
                        <div class="absolute left-0 mt-2 w-56 bg-white shadow-lg rounded-lg z-50 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 text-right">
                            <div class="px-4 py-3 border-b border-gray-100">
                                <p class="text-sm font-medium text-gray-900">{{ Auth::user()->name }}</p>
                                <p class="text-sm text-gray-500">{{ Auth::user()->email }}</p>
                            </div>

                            <a href="{{ route('dashboard') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                                <i class="fas fa-tachometer-alt ml-2 text-sm"></i>
                                لوحة التحكم
                            </a>

                            <a href="{{ route('user.orders.index') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                                <i class="fas fa-list-alt ml-2 text-sm"></i>
                                طلباتي
                            </a>

                            <a href="{{ route('profile.edit') }}" class="block px-4 py-2 text-gray-700 hover:bg-purple-50 hover:text-purple-600 transition-colors">
                                <i class="fas fa-user-cog ml-2 text-sm"></i>
                                إعدادات الحساب
                            </a>

                            <div class="border-t border-gray-100">
                                <form method="POST" action="{{ route('logout') }}" class="block">
                                    @csrf
                                    <button type="submit" class="w-full text-right px-4 py-2 text-red-600 hover:bg-red-50 hover:text-red-700 transition-colors">
                                        <i class="fas fa-sign-out-alt ml-2 text-sm"></i>
                                        تسجيل الخروج
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <!-- Login Button -->
                    <a href="{{ route('login') }}" class="text-gray-700 hover:text-purple-600 px-4 py-2 text-sm font-medium transition-all duration-300 flex items-center gap-2 border border-gray-300 hover:border-purple-600 rounded-lg hover:bg-purple-50">
                        <i class="fas fa-sign-in-alt text-sm"></i>
                        <span>تسجيل الدخول</span>
                    </a>
                @endauth
            </div>

            <!-- Mobile Menu Button -->
            <div class="lg:hidden">
                <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-700 hover:text-purple-600 focus:outline-none focus:text-purple-600 transition-colors">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                </button>
            </div>
        </div>
    <!-- Mobile Navigation Menu -->
    <div x-show="mobileMenuOpen" x-transition class="lg:hidden bg-white/95 backdrop-blur-lg border-t border-gray-200">
        <div class="px-4 py-6 space-y-4">
            <a href="{{ route('home') }}" class="block text-gray-800 hover:text-purple-600 py-2 text-lg font-medium transition-colors">
                الرئيسية
            </a>

            <!-- Services -->
            <a href="{{ route('services') }}" class="block text-gray-800 hover:text-purple-600 py-2 text-lg font-medium transition-colors">
                الخدمات
            </a>

            <!-- Hosting -->
            <a href="{{ route('hosting.index') }}" class="block text-gray-800 hover:text-purple-600 py-2 text-lg font-medium transition-colors">
                الاستضافة
            </a>

            <!-- Domains -->
            <a href="{{ route('domains.index') }}" class="block text-gray-800 hover:text-purple-600 py-2 text-lg font-medium transition-colors">
                الدومينات
            </a>

            <!-- Email -->
            <a href="{{ route('email.index') }}" class="block text-gray-800 hover:text-purple-600 py-2 text-lg font-medium transition-colors">
                البريد
            </a>

            <!-- Pricing -->
            <a href="{{ route('hosting.index') }}" class="block text-gray-800 hover:text-purple-600 py-2 text-lg font-medium transition-colors">
                الأسعار
            </a>

            <!-- Support -->
            <div class="space-y-2">
                <div class="text-gray-800 py-2 text-lg font-medium">الدعم</div>
                <div class="pr-4 space-y-2">
                    <a href="{{ route('contact') }}" class="block text-gray-600 hover:text-purple-600 py-1 transition-colors">
                        <i class="fas fa-headset ml-2 text-xs"></i>اتصل بنا
                    </a>
                    <a href="#" class="block text-gray-600 hover:text-purple-600 py-1 transition-colors">
                        <i class="fas fa-question-circle ml-2 text-xs"></i>الأسئلة الشائعة
                    </a>
                    <a href="https://wa.me/967737662752" class="block text-gray-600 hover:text-purple-600 py-1 transition-colors">
                        <i class="fab fa-whatsapp ml-2 text-xs"></i>واتساب
                    </a>
                </div>
            </div>

            <!-- User Authentication Section -->
            <div class="pt-4 border-t border-gray-200 mt-4 space-y-3">
                @auth
                    <!-- User Info -->
                    <div class="bg-purple-50 rounded-lg p-3 text-center">
                        <div class="flex items-center justify-center mb-2">
                            <i class="fas fa-user-circle text-purple-600 text-2xl ml-2"></i>
                            <div>
                                <p class="text-sm font-medium text-gray-900">{{ Auth::user()->name }}</p>
                                <p class="text-xs text-gray-500">{{ Auth::user()->email }}</p>
                            </div>
                        </div>
                    </div>

                    <!-- User Dashboard Link -->
                    <a href="{{ route('dashboard') }}" class="block w-full text-center bg-purple-600 hover:bg-purple-700 text-white py-3 rounded-lg font-medium transition-all duration-300">
                        <i class="fas fa-tachometer-alt ml-2"></i>
                        لوحة التحكم
                    </a>

                    <!-- User Menu -->
                    <div class="space-y-2">
                        <a href="{{ route('user.orders.index') }}" class="block text-gray-700 hover:text-purple-600 py-2 transition-colors">
                            <i class="fas fa-list-alt ml-2"></i>
                            طلباتي
                        </a>
                        <a href="{{ route('profile.edit') }}" class="block text-gray-700 hover:text-purple-600 py-2 transition-colors">
                            <i class="fas fa-user-cog ml-2"></i>
                            إعدادات الحساب
                        </a>
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="block w-full text-right text-red-600 hover:text-red-700 py-2 transition-colors">
                                <i class="fas fa-sign-out-alt ml-2"></i>
                                تسجيل الخروج
                            </button>
                        </form>
                    </div>
                @else
                    <!-- Login Button -->
                    <a href="{{ route('login') }}" class="block w-full text-center border border-gray-300 text-gray-700 hover:border-purple-600 hover:text-purple-600 hover:bg-purple-50 py-3 rounded-lg font-medium transition-all duration-300">
                        <i class="fas fa-sign-in-alt ml-2"></i>
                        تسجيل الدخول
                    </a>
                @endauth

                <!-- Quick Contact Options -->
                <div class="grid grid-cols-2 gap-2">
                    <a href="https://wa.me/967737662752" class="flex items-center justify-center bg-green-500 hover:bg-green-600 text-white py-2.5 rounded-lg text-sm font-medium transition-colors">
                        <i class="fab fa-whatsapp ml-1 text-sm"></i>
                        واتساب
                    </a>
                    <a href="tel:+967782871439" class="flex items-center justify-center bg-blue-500 hover:bg-blue-600 text-white py-2.5 rounded-lg text-sm font-medium transition-colors">
                        <i class="fas fa-phone ml-1 text-sm"></i>
                        اتصال
                    </a>
                </div>
            </div>

            <!-- Support -->
            <div class="space-y-2">
                <div class="text-gray-700 py-2 text-lg font-medium">الدعم</div>
                <div class="pr-4 space-y-2">
                    <a href="{{ route('contact') }}" class="block text-gray-600 hover:text-purple-600 py-1 transition-colors">
                        <i class="fas fa-headset ml-2 text-xs"></i>اتصل بنا
                    </a>
                    <a href="#" class="block text-gray-600 hover:text-purple-600 py-1 transition-colors">
                        <i class="fas fa-question-circle ml-2 text-xs"></i>الأسئلة الشائعة
                    </a>
                    <a href="https://wa.me/967737662752" class="block text-gray-600 hover:text-purple-600 py-1 transition-colors">
                        <i class="fab fa-whatsapp ml-2 text-xs"></i>واتساب
                    </a>
                </div>
            </div>
        </div>
    </div>
</nav>

