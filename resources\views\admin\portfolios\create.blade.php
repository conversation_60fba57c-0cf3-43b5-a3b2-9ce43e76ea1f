@extends('layouts.admin')

@section('title', 'إضافة عمل جديد')

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">إضافة عمل جديد</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.portfolios.index') }}">الأعمال</a></li>
                        <li class="breadcrumb-item active">إضافة عمل جديد</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">معلومات العمل</h3>
                        </div>
                        <form action="#" method="POST" enctype="multipart/form-data">
                            @csrf
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-8">
                                        <div class="form-group">
                                            <label for="title">عنوان العمل <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="title" name="title" required>
                                        </div>

                                        <div class="form-group">
                                            <label for="description">وصف العمل</label>
                                            <textarea class="form-control" id="description" name="description" rows="4"></textarea>
                                        </div>

                                        <div class="form-group">
                                            <label for="technologies">التقنيات المستخدمة</label>
                                            <input type="text" class="form-control" id="technologies" name="technologies" placeholder="مثال: Laravel, Vue.js, MySQL">
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="client_name">اسم العميل</label>
                                                    <input type="text" class="form-control" id="client_name" name="client_name">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="project_url">رابط المشروع</label>
                                                    <input type="url" class="form-control" id="project_url" name="project_url">
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="start_date">تاريخ البداية</label>
                                                    <input type="date" class="form-control" id="start_date" name="start_date">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label for="end_date">تاريخ الانتهاء</label>
                                                    <input type="date" class="form-control" id="end_date" name="end_date">
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="category">الفئة</label>
                                            <select class="form-control" id="category" name="category">
                                                <option value="">اختر الفئة</option>
                                                <option value="web-development">تطوير مواقع</option>
                                                <option value="mobile-app">تطبيقات الجوال</option>
                                                <option value="desktop-app">تطبيقات سطح المكتب</option>
                                                <option value="ui-ux">تصميم UI/UX</option>
                                                <option value="graphic-design">تصميم جرافيك</option>
                                                <option value="other">أخرى</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="status">الحالة</label>
                                            <select class="form-control" id="status" name="status">
                                                <option value="draft">مسودة</option>
                                                <option value="published">منشور</option>
                                            </select>
                                        </div>

                                        <div class="form-group">
                                            <label for="featured">عمل مميز</label>
                                            <div class="custom-control custom-switch">
                                                <input type="checkbox" class="custom-control-input" id="featured" name="featured" value="1">
                                                <label class="custom-control-label" for="featured">نعم</label>
                                            </div>
                                        </div>

                                        <div class="form-group">
                                            <label for="image">صورة العمل</label>
                                            <div class="custom-file">
                                                <input type="file" class="custom-file-input" id="image" name="image" accept="image/*">
                                                <label class="custom-file-label" for="image">اختر صورة</label>
                                            </div>
                                            <small class="form-text text-muted">الحد الأقصى: 2MB، الأنواع المدعومة: JPG, PNG, GIF</small>
                                        </div>

                                        <div class="form-group">
                                            <label for="gallery">معرض الصور</label>
                                            <div class="custom-file">
                                                <input type="file" class="custom-file-input" id="gallery" name="gallery[]" accept="image/*" multiple>
                                                <label class="custom-file-label" for="gallery">اختر صور متعددة</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="card-footer">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ العمل
                                </button>
                                <a href="{{ route('admin.portfolios.index') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    // Custom file input labels
    $('.custom-file-input').on('change', function() {
        let fileName = $(this).val().split('\\').pop();
        $(this).siblings('.custom-file-label').addClass('selected').html(fileName);
    });
});
</script>
@endsection
