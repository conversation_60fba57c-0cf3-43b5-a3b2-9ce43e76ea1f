<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class ActivityLogController extends Controller
{
    public function index()
    {
        return view('backend.activity-log.index');
    }

    public function show($id)
    {
        return view('backend.activity-log.show', compact('id'));
    }

    public function destroy($id)
    {
        return response()->json(['message' => 'Delete functionality not implemented yet']);
    }

    public function clear()
    {
        return response()->json(['message' => 'Clear functionality not implemented yet']);
    }

    public function export()
    {
        return response()->json(['message' => 'Export functionality not implemented yet']);
    }

    public function getRecentActivity()
    {
        return response()->json(['message' => 'Recent activity functionality not implemented yet']);
    }
}
