<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Order extends Model
{
    protected $fillable = [
        'order_number',
        'customer_name',
        'customer_email',
        'customer_phone',
        'service_type',
        'hosting_plan_id',
        'hosting_provider_id',
        'email_plan_id',
        'email_provider_id',
        'email_users_count',
        'email_users',
        'domain_name',
        'order_details',
        'subtotal',
        'tax_amount',
        'total_amount',
        'status',
        'payment_status',
        'payment_method',
        'transaction_id',
        'provider_order_id',
        'provider_response',
        'service_start_date',
        'service_end_date',
        'admin_notes',
    ];

    protected $casts = [
        'order_details' => 'array',
        'provider_response' => 'array',
        'email_users' => 'array',
        'service_start_date' => 'date',
        'service_end_date' => 'date',
        'subtotal' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    public function hostingPlan(): BelongsTo
    {
        return $this->belongsTo(HostingPlan::class);
    }

    public function hostingProvider(): BelongsTo
    {
        return $this->belongsTo(HostingProvider::class);
    }

    public function emailPlan(): BelongsTo
    {
        return $this->belongsTo(EmailPlan::class);
    }

    public function emailProvider(): BelongsTo
    {
        return $this->belongsTo(EmailProvider::class);
    }
}
