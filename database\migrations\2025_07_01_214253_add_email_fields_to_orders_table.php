<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->foreignId('email_plan_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('email_provider_id')->nullable()->constrained()->onDelete('set null');
            $table->integer('email_users_count')->nullable(); // Number of email users
            $table->json('email_users')->nullable(); // Array of email users data
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropForeign(['email_plan_id']);
            $table->dropForeign(['email_provider_id']);
            $table->dropColumn(['email_plan_id', 'email_provider_id', 'email_users_count', 'email_users']);
        });
    }
};
