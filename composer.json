{"name": "rappasoft/laravel-boilerplate", "type": "project", "description": "The Laravel Boilerplate Project.", "keywords": ["framework", "laravel", "boilerplate"], "license": "MIT", "require": {"php": "^7.4|^8.0", "arcanedev/log-viewer": "8.x", "darkghosthunter/laraguard": "^3.0", "doctrine/dbal": "^3.10", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "jamesmills/laravel-timezone": "^1.9", "lab404/laravel-impersonate": "^1.6", "langleyfoxall/laravel-nist-password-rules": "^4.1", "laravel/framework": "^8.54", "laravel/sanctum": "^2.11", "laravel/socialite": "^5.0", "laravel/tinker": "^2.5", "laravel/ui": "^3.0", "livewire/livewire": "^2.0", "rappasoft/laravel-livewire-tables": "^1.0", "rappasoft/lockout": "^3.0", "spatie/laravel-activitylog": "^3.14", "spatie/laravel-permission": "^3.11", "tabuna/breadcrumbs": "^2.2"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.2", "barryvdh/laravel-ide-helper": "^2.6", "brianium/paratest": "^6.2", "facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "friendsofphp/php-cs-fixer": "^2.16", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi", "@php artisan ide-helper:generate", "@php artisan ide-helper:meta"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "clear-all": ["@php artisan clear-compiled", "@php artisan cache:clear", "@php artisan route:clear", "@php artisan view:clear", "@php artisan config:clear", "composer <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>"], "cache-all": ["@php artisan config:cache", "@php artisan route:cache"], "reset": ["composer clear-all", "composer cache-all"], "test": "@php artisan test --parallel", "test-coverage": "vendor/bin/phpunit --coverage-html coverage", "format": "vendor/bin/php-cs-fixer fix --allow-risky=yes"}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"php-http/discovery": true}}, "minimum-stability": "dev", "prefer-stable": true}