<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('hosting_plans', function (Blueprint $table) {
            $table->string('bandwidth')->nullable()->after('storage');
            $table->integer('databases_limit')->default(1)->after('email_accounts');
            $table->foreignId('hosting_provider_id')->nullable()->constrained()->onDelete('set null')->after('sort_order');
            $table->decimal('commission_rate', 5, 2)->default(0)->after('hosting_provider_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('hosting_plans', function (Blueprint $table) {
            $table->dropForeign(['hosting_provider_id']);
            $table->dropColumn([
                'bandwidth',
                'databases_limit',
                'hosting_provider_id',
                'commission_rate'
            ]);
        });
    }
};
