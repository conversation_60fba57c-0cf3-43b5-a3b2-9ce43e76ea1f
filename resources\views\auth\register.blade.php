<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>إنشاء حساب</title>

    <!-- Firebase Configuration -->
    <x-firebase-config />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: #f5f5f5;
            min-height: 100vh;
        }

        .register-container {
            background: white;
            border: 1px solid #e2e8f0;
        }

        .social-btn {
            transition: all 0.2s ease;
            border: 1px solid #e2e8f0;
        }

        .social-btn:hover {
            background-color: #f8fafc;
            border-color: #cbd5e1;
        }

        .input-field {
            transition: all 0.2s ease;
            border: 1px solid #e2e8f0;
        }

        .input-field:focus {
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
            outline: none;
        }

        .register-btn {
            background: #8b5cf6;
            transition: all 0.2s ease;
        }

        .register-btn:hover {
            background: #7c3aed;
        }

        .google-btn {
            border: 1px solid #e2e8f0;
        }

        .google-btn:hover {
            background-color: #f8fafc;
        }

        .github-btn {
            background: #24292f;
        }

        .github-btn:hover {
            background: #1c2128;
        }
    </style>
</head>

<body class="min-h-screen flex items-center justify-center p-4" style="background-color: #f5f5f5;">
    <!-- Register Container -->
    <div class="register-container w-full max-w-md p-8 rounded-lg shadow-sm">
        <!-- Register Title -->
        <div class="text-center mb-8">
            <h2 class="text-2xl font-semibold text-gray-900 mb-2">إنشاء حساب</h2>
        </div>

        <!-- Social Login Buttons -->
        <div class="grid grid-cols-2 gap-3 mb-6">
            <!-- Google Button (Firebase) -->
            <button data-auth="google" class="google-btn flex items-center justify-center px-4 py-3 rounded-lg transition-all" title="إنشاء حساب عبر Google (Firebase)">
                <svg class="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                    <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                    <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                    <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                </svg>
            </button>

            <!-- GitHub Button -->
            <button class="github-btn flex items-center justify-center px-4 py-3 text-white rounded-lg transition-all">
                <i class="fab fa-github text-lg"></i>
            </button>
        </div>

        <!-- Socialite Login Buttons -->
        <div class="mb-6">
            <div class="text-center text-sm text-gray-500 mb-3">أو استخدم Laravel Socialite</div>
            <div class="space-y-2">
                <!-- Google Socialite Button -->
                <a href="{{ route('google.redirect') }}" class="w-full flex items-center justify-center px-4 py-3 border border-gray-300 rounded-lg bg-white hover:bg-gray-50 transition-all">
                    <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
                        <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                        <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                        <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                        <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    <span class="text-gray-700 font-medium">إنشاء حساب عبر Google (Socialite)</span>
                </a>
            </div>
        </div>

        <!-- Divider -->
        <div class="relative mb-6">
            <div class="absolute inset-0 flex items-center">
                <div class="w-full border-t border-gray-200"></div>
            </div>
            <div class="relative flex justify-center text-sm">
                <span class="px-2 bg-white text-gray-500">أو</span>
            </div>
        </div>

        <!-- Register Form -->
        <form method="POST" action="{{ route('signup') }}" class="space-y-4">
            @csrf

            <!-- Name Field -->
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">الاسم الكامل</label>
                <input type="text" id="name" name="name" required
                       class="input-field w-full px-3 py-2.5 rounded-lg text-right"
                       placeholder="أدخل اسمك الكامل" value="{{ old('name') }}">
                @error('name')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Email Field -->
            <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">البريد الإلكتروني</label>
                <input type="email" id="email" name="email" required
                       class="input-field w-full px-3 py-2.5 rounded-lg text-right"
                       placeholder="أدخل بريدك الإلكتروني" value="{{ old('email') }}">
                @error('email')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Password Field -->
            <div>
                <label for="password" class="block text-sm font-medium text-gray-700 mb-1">كلمة المرور</label>
                <div class="relative">
                    <input type="password" id="password" name="password" required
                           class="input-field w-full px-3 py-2.5 rounded-lg pr-10 text-right"
                           placeholder="أدخل كلمة المرور">
                    <button type="button" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="togglePassword()">
                        <i class="fas fa-eye" id="toggleIcon"></i>
                    </button>
                </div>
                @error('password')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Confirm Password Field -->
            <div>
                <label for="password_confirmation" class="block text-sm font-medium text-gray-700 mb-1">تأكيد كلمة المرور</label>
                <div class="relative">
                    <input type="password" id="password_confirmation" name="password_confirmation" required
                           class="input-field w-full px-3 py-2.5 rounded-lg pr-10 text-right"
                           placeholder="أعد إدخال كلمة المرور">
                    <button type="button" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600" onclick="togglePasswordConfirm()">
                        <i class="fas fa-eye" id="toggleIconConfirm"></i>
                    </button>
                </div>
                @error('password_confirmation')
                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                @enderror
            </div>

            <!-- Register Button -->
            <button type="submit" class="register-btn w-full py-3 px-4 text-white font-semibold rounded-lg">
                إنشاء حساب
            </button>
        </form>

        <!-- Login Link -->
        <div class="mt-6 text-center">
            <div class="text-sm text-gray-600">
                لديك حساب بالفعل؟
                <a href="{{ route('login') }}" class="text-purple-600 hover:text-purple-500 font-medium transition-colors">تسجيل الدخول</a>
            </div>
        </div>

        <!-- Terms Notice -->
        <div class="mt-4 text-center">
            <p class="text-xs text-gray-500">
                من خلال الاستمرار، فإنك توافق على
                <a href="#" class="text-purple-600 hover:text-purple-500">شروط الخدمة</a>
                وقد قرأت
                <a href="#" class="text-purple-600 hover:text-purple-500">سياسة الخصوصية</a>
            </p>
        </div>
    </div>



    <!-- JavaScript -->
    <script>
        function togglePassword() {
            const passwordField = document.getElementById('password');
            const toggleIcon = document.getElementById('toggleIcon');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        function togglePasswordConfirm() {
            const passwordField = document.getElementById('password_confirmation');
            const toggleIcon = document.getElementById('toggleIconConfirm');

            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Google button click handler
        document.addEventListener('DOMContentLoaded', function() {
            const googleButton = document.querySelector('[data-auth="google"]');
            if (googleButton) {
                googleButton.addEventListener('click', async function(e) {
                    e.preventDefault();

                    const originalContent = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>جاري إنشاء الحساب...';
                    this.disabled = true;

                    try {
                        if (typeof window.signInWithGoogle === 'function') {
                            await window.signInWithGoogle();
                        } else {
                            throw new Error('Firebase غير مُحمل بشكل صحيح');
                        }
                    } catch (error) {
                        console.error('خطأ في المصادقة:', error);

                        let errorMessage = 'حدث خطأ أثناء إنشاء الحساب';
                        if (error.code === 'auth/popup-closed-by-user') {
                            errorMessage = 'تم إلغاء عملية إنشاء الحساب';
                        } else if (error.code === 'auth/popup-blocked') {
                            errorMessage = 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة';
                        } else if (error.code === 'auth/network-request-failed') {
                            errorMessage = 'خطأ في الاتصال بالإنترنت';
                        }

                        alert(errorMessage);
                        this.innerHTML = originalContent;
                        this.disabled = false;
                    }
                });
            }
        });

        // Show loading state on form submit
        document.querySelector('form').addEventListener('submit', function() {
            const submitBtn = this.querySelector('button[type="submit"]');
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin ml-2"></i>جاري إنشاء الحساب...';
            submitBtn.disabled = true;
        });
    </script>
</body>
</html>
