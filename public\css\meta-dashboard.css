/* ميتاء تك - لوحة التحكم الحديثة */

:root {
    /* الألوان الأساسية */
    --primary-blue: #4f46e5;
    --primary-purple: #6b46c1;
    --orange-accent: #f59e0b;
    --green-accent: #10b981;
    --red-accent: #ef4444;
    --gray-50: #f8fafc;
    --gray-100: #f1f5f9;
    --gray-200: #e2e8f0;
    --gray-300: #cbd5e1;
    --gray-400: #94a3b8;
    --gray-500: #64748b;
    --gray-600: #475569;
    --gray-700: #334155;
    --gray-800: #1e293b;
    --gray-900: #0f172a;
    --white: #ffffff;
    
    /* الظلال */
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    
    /* الانتقالات */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* إعدادات عامة */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    overflow-x: hidden;
}

/* الحاوي الرئيسي */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    background: #f8f9fa;
}

/* الشريط الجانبي */
.sidebar {
    width: 280px;
    background: #ffffff;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    border-left: 1px solid #e2e8f0;
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    z-index: 1000;
    overflow-y: auto;
    transition: transform var(--transition-normal);
}

.sidebar::-webkit-scrollbar {
    width: 6px;
}

.sidebar::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
}

.sidebar::-webkit-scrollbar-thumb {
    background: rgba(0, 0, 0, 0.2);
    border-radius: 3px;
}

/* شعار الشركة */
.sidebar-header {
    padding: 24px 20px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    text-align: center;
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
}

.logo-icon {
    width: 40px;
    height: 40px;
    background: linear-gradient(135deg, var(--primary-blue), var(--primary-purple));
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 20px;
    font-weight: bold;
}

.logo-text {
    font-size: 24px;
    font-weight: 700;
    color: var(--gray-800);
}

/* ملف المستخدم */
.user-profile {
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.user-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 12px;
    border: 3px solid var(--primary-blue);
    object-fit: cover;
}

.user-name {
    font-size: 16px;
    font-weight: 600;
    color: var(--gray-800);
    margin-bottom: 4px;
}

.user-email {
    font-size: 14px;
    color: var(--gray-500);
}

/* القائمة الجانبية */
.sidebar-menu {
    padding: 20px 0;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--gray-600);
    text-decoration: none;
    transition: all var(--transition-fast);
    border-right: 3px solid transparent;
}

.menu-item:hover {
    background: rgba(79, 70, 229, 0.1);
    color: var(--primary-blue);
    border-right-color: var(--primary-blue);
}

.menu-item.active {
    background: linear-gradient(90deg, rgba(79, 70, 229, 0.15), rgba(79, 70, 229, 0.05));
    color: var(--primary-blue);
    border-right-color: var(--primary-blue);
    font-weight: 600;
}

.menu-icon {
    width: 20px;
    margin-left: 12px;
    text-align: center;
}

.menu-text {
    flex: 1;
    font-size: 15px;
}

/* المحتوى الرئيسي */
.main-content {
    flex: 1;
    margin-right: 280px;
    min-height: 100vh;
    background: transparent;
}

/* الشريط العلوي */
.top-header {
    background: #ffffff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border-bottom: 1px solid #e2e8f0;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: sticky;
    top: 0;
    z-index: 100;
}

/* حقل البحث */
.search-container {
    position: relative;
    width: 400px;
}

.search-input {
    width: 100%;
    padding: 12px 20px 12px 50px;
    border: none;
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.9);
    font-size: 14px;
    outline: none;
    transition: all var(--transition-fast);
}

.search-input:focus {
    background: white;
    box-shadow: var(--shadow-md);
}

.search-icon {
    position: absolute;
    right: 18px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--gray-400);
}

/* أيقونات الشريط العلوي */
.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.header-btn {
    width: 44px;
    height: 44px;
    border-radius: 12px;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--gray-600);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
}

.header-btn:hover {
    background: white;
    color: var(--primary-blue);
    box-shadow: var(--shadow-md);
}

.notification-badge {
    position: absolute;
    top: -2px;
    left: -2px;
    width: 18px;
    height: 18px;
    background: var(--orange-accent);
    color: white;
    border-radius: 50%;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* زر عرض الموقع */
.website-link-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    transition: all var(--transition-normal);
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    border: none;
}

.website-link-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(102, 126, 234, 0.4);
    color: white;
    text-decoration: none;
}

.website-link-btn i {
    font-size: 12px;
}

.filter-btn {
    padding: 12px 20px;
    background: var(--primary-blue);
    color: white;
    border: none;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.filter-btn:hover {
    background: var(--primary-purple);
    box-shadow: var(--shadow-md);
}

/* محتوى لوحة التحكم */
.dashboard-content {
    padding: 24px;
}

/* التصميم المتجاوب */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
        z-index: 2000;
    }

    .sidebar.open {
        transform: translateX(0);
        box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
    }

    .main-content {
        margin-right: 0;
        width: 100%;
    }

    .search-container {
        width: 300px;
    }

    /* إضافة overlay للموبايل */
    .sidebar.open::before {
        content: '';
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(0, 0, 0, 0.5);
        z-index: -1;
    }
}

@media (max-width: 768px) {
    .top-header {
        flex-direction: column;
        gap: 16px;
        padding: 16px;
    }

    .search-container {
        width: 100%;
        order: 2;
    }

    .header-actions {
        width: 100%;
        justify-content: center;
        order: 1;
    }

    .dashboard-content {
        padding: 16px;
    }

    /* تحسين الشريط الجانبي للموبايل */
    .sidebar {
        width: 85%;
        max-width: 320px;
    }

    .user-profile {
        padding: 16px;
    }

    .user-avatar {
        width: 50px;
        height: 50px;
    }

    .menu-item {
        padding: 16px 20px;
        font-size: 16px;
    }

    .menu-icon {
        width: 24px;
        font-size: 18px;
    }
}

@media (max-width: 640px) {
    .sidebar {
        width: 100%;
        max-width: none;
    }

    .header-actions {
        flex-wrap: wrap;
        gap: 12px;
    }

    .header-btn {
        width: 40px;
        height: 40px;
    }

    .filter-btn {
        padding: 10px 16px;
        font-size: 13px;
    }

    .search-input {
        padding: 10px 16px 10px 40px;
        font-size: 14px;
    }

    .search-icon {
        right: 14px;
    }
}

/* تحسينات إضافية للأداء */
@media (max-width: 480px) {
    .dashboard-content {
        padding: 12px;
    }

    .sidebar-header {
        padding: 20px 16px;
    }

    .logo-text {
        font-size: 20px;
    }

    .logo-icon {
        width: 36px;
        height: 36px;
        font-size: 18px;
    }

    .user-name {
        font-size: 15px;
    }

    .user-email {
        font-size: 13px;
    }

    .menu-item {
        padding: 14px 16px;
    }

    .menu-text {
        font-size: 15px;
    }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1920px) {
    .dashboard-container {
        max-width: 1920px;
        margin: 0 auto;
    }

    .sidebar {
        width: 320px;
    }

    .main-content {
        margin-right: 320px;
    }

    .dashboard-content {
        padding: 32px;
    }
}

/* تحسينات للطباعة */
@media print {
    .sidebar,
    .top-header {
        display: none !important;
    }

    .main-content {
        margin-right: 0 !important;
        width: 100% !important;
    }

    .dashboard-content {
        padding: 0 !important;
    }

    .stat-card,
    .chart-card,
    .balance-card {
        break-inside: avoid;
        box-shadow: none !important;
        border: 1px solid #ddd !important;
    }
}
