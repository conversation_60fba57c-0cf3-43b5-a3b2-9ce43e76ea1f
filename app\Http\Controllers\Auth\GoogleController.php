<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Laravel\Socialite\Facades\Socialite;
use App\Models\User;
use App\Models\Setting;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class GoogleController extends Controller
{
    /**
     * Redirect to Google OAuth
     */
    public function redirect()
    {
        // Check if OAuth is enabled
        $oauthEnabled = Setting::get('oauth_enabled', true);

        if (!$oauthEnabled) {
            return redirect()->route('login')
                ->withErrors(['oauth' => 'تسجيل الدخول عبر Google غير متاح حالياً.']);
        }

        return Socialite::driver('google')
            ->scopes(['openid', 'profile', 'email'])
            ->with(['access_type' => 'offline', 'prompt' => 'consent'])
            ->redirect();
    }

    /**
     * Handle Google OAuth callback
     */
    public function callback()
    {
        try {
            // Check if OAuth is enabled
            $oauthEnabled = Setting::get('oauth_enabled', true);
            $autoRegister = Setting::get('auto_register_users', true);
            $defaultRole = Setting::get('default_user_role', 'user');

            if (!$oauthEnabled) {
                return redirect()->route('login')
                    ->withErrors(['oauth' => 'تسجيل الدخول عبر Google غير متاح حالياً.']);
            }

            $googleUser = Socialite::driver('google')->stateless()->user();

            // البحث عن المستخدم الموجود
            $existingUser = User::where('email', $googleUser->getEmail())->first();

            if ($existingUser) {
                // تحديث بيانات المستخدم الموجود
                $existingUser->update([
                    'google_id' => $googleUser->getId(),
                    'avatar' => $googleUser->getAvatar(),
                    'provider' => 'google',
                    'email_verified_at' => $existingUser->email_verified_at ?? now(),
                ]);

                Auth::login($existingUser);
                return redirect()->route('home')->with('success', 'مرحباً بعودتك! تم تسجيل الدخول بنجاح.');
            }

            // إنشاء مستخدم جديد إذا كان التسجيل التلقائي مفعل
            if (!$autoRegister) {
                return redirect()->route('login')
                    ->withErrors(['oauth' => 'التسجيل التلقائي غير مفعل. يرجى التواصل مع الإدارة.']);
            }

            $user = User::create([
                'name' => $googleUser->getName(),
                'email' => $googleUser->getEmail(),
                'email_verified_at' => now(),
                'password' => Hash::make(Str::random(24)), // كلمة مرور عشوائية
                'google_id' => $googleUser->getId(),
                'avatar' => $googleUser->getAvatar(),
                'provider' => 'google'
            ]);

            Auth::login($user);

            return redirect()->route('home')->with('success', 'مرحباً بك! تم إنشاء حسابك وتسجيل الدخول بنجاح.');

        } catch (\Exception $e) {
            \Log::error('Google OAuth Error: ' . $e->getMessage());
            return redirect('/login')->with('error', 'حدث خطأ أثناء تسجيل الدخول عبر Google. يرجى المحاولة مرة أخرى.');
        }
    }
}
