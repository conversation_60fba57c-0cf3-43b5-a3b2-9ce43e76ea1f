{{-- قالب الإشعارات والتنبيهات المتقدم --}}
@props([
    'type' => 'info', // success, error, warning, info
    'title' => '',
    'message' => '',
    'dismissible' => true,
    'autoHide' => false,
    'duration' => 5000,
    'position' => 'top-right', // top-right, top-left, bottom-right, bottom-left, top-center
    'icon' => null,
    'actions' => [],
    'progress' => false,
    'sound' => false,
    'persistent' => false
])

@php
$typeClasses = [
    'success' => 'alert-success',
    'error' => 'alert-danger',
    'warning' => 'alert-warning',
    'info' => 'alert-info'
];

$typeIcons = [
    'success' => 'fas fa-check-circle',
    'error' => 'fas fa-exclamation-triangle',
    'warning' => 'fas fa-exclamation-circle',
    'info' => 'fas fa-info-circle'
];

$alertClass = $typeClasses[$type] ?? 'alert-info';
$defaultIcon = $typeIcons[$type] ?? 'fas fa-info-circle';
$notificationIcon = $icon ?? $defaultIcon;
@endphp

<div class="notification-container position-fixed" 
     style="z-index: 9999;"
     x-data="notification()" 
     x-init="init()"
     x-show="visible"
     x-transition:enter="transition ease-out duration-300"
     x-transition:enter-start="opacity-0 transform translate-y-2"
     x-transition:enter-end="opacity-100 transform translate-y-0"
     x-transition:leave="transition ease-in duration-200"
     x-transition:leave-start="opacity-100 transform translate-y-0"
     x-transition:leave-end="opacity-0 transform translate-y-2">
    
    <div class="alert {{ $alertClass }} alert-dismissible border-0 shadow-lg notification-alert" 
         role="alert"
         style="min-width: 350px; max-width: 500px;">
        
        <!-- Progress Bar -->
        @if($progress && $autoHide)
        <div class="notification-progress">
            <div class="progress-bar" 
                 :style="`width: ${progressWidth}%`"
                 :class="{
                     'bg-success': '{{ $type }}' === 'success',
                     'bg-danger': '{{ $type }}' === 'error',
                     'bg-warning': '{{ $type }}' === 'warning',
                     'bg-info': '{{ $type }}' === 'info'
                 }"></div>
        </div>
        @endif
        
        <div class="d-flex align-items-start">
            <!-- Icon -->
            <div class="notification-icon me-3">
                <i class="{{ $notificationIcon }} fs-4"></i>
            </div>
            
            <!-- Content -->
            <div class="notification-content flex-grow-1">
                @if($title)
                <h6 class="notification-title mb-1 fw-bold">{{ $title }}</h6>
                @endif
                
                <div class="notification-message">
                    {{ $message }}
                    {{ $slot }}
                </div>
                
                <!-- Actions -->
                @if(count($actions) > 0)
                <div class="notification-actions mt-2">
                    @foreach($actions as $action)
                    <button type="button" 
                            class="btn btn-sm {{ $action['class'] ?? 'btn-outline-primary' }} me-2"
                            @if(isset($action['onclick'])) onclick="{{ $action['onclick'] }}" @endif
                            @if(isset($action['href'])) onclick="window.location.href='{{ $action['href'] }}'" @endif>
                        @if(isset($action['icon']))
                        <i class="{{ $action['icon'] }} me-1"></i>
                        @endif
                        {{ $action['text'] }}
                    </button>
                    @endforeach
                </div>
                @endif
            </div>
            
            <!-- Close Button -->
            @if($dismissible)
            <button type="button" 
                    class="btn-close" 
                    x-on:click="close()"
                    aria-label="إغلاق"></button>
            @endif
        </div>
    </div>
</div>

<!-- Notification Container for Multiple Notifications -->
<div id="notification-stack" class="notification-stack position-fixed {{ $position }}" style="z-index: 9999;">
    <!-- Dynamic notifications will be inserted here -->
</div>

<script>
function notification() {
    return {
        visible: true,
        progressWidth: 100,
        progressInterval: null,
        
        init() {
            @if($autoHide)
            this.startAutoHide();
            @endif
            
            @if($sound)
            this.playNotificationSound();
            @endif
            
            @if($persistent)
            this.saveToPersistent();
            @endif
            
            this.positionNotification();
        },
        
        startAutoHide() {
            const duration = {{ $duration }};
            const interval = 50;
            const steps = duration / interval;
            const decrementValue = 100 / steps;
            
            this.progressInterval = setInterval(() => {
                this.progressWidth -= decrementValue;
                
                if (this.progressWidth <= 0) {
                    this.close();
                }
            }, interval);
        },
        
        close() {
            if (this.progressInterval) {
                clearInterval(this.progressInterval);
            }
            
            this.visible = false;
            
            // Remove from DOM after animation
            setTimeout(() => {
                this.$el.remove();
            }, 300);
        },
        
        positionNotification() {
            const position = '{{ $position }}';
            const positions = {
                'top-right': { top: '20px', right: '20px' },
                'top-left': { top: '20px', left: '20px' },
                'bottom-right': { bottom: '20px', right: '20px' },
                'bottom-left': { bottom: '20px', left: '20px' },
                'top-center': { top: '20px', left: '50%', transform: 'translateX(-50%)' }
            };
            
            const pos = positions[position] || positions['top-right'];
            Object.assign(this.$el.style, pos);
        },
        
        playNotificationSound() {
            const audio = new Audio('/sounds/notification.mp3');
            audio.volume = 0.3;
            audio.play().catch(() => {
                // Ignore if sound fails to play
            });
        },
        
        saveToPersistent() {
            const notifications = JSON.parse(localStorage.getItem('persistent_notifications') || '[]');
            notifications.push({
                type: '{{ $type }}',
                title: '{{ $title }}',
                message: '{{ $message }}',
                timestamp: Date.now()
            });
            
            // Keep only last 50 notifications
            if (notifications.length > 50) {
                notifications.splice(0, notifications.length - 50);
            }
            
            localStorage.setItem('persistent_notifications', JSON.stringify(notifications));
        }
    }
}

// Global Notification System
window.NotificationSystem = {
    show(options) {
        const {
            type = 'info',
            title = '',
            message = '',
            duration = 5000,
            position = 'top-right',
            dismissible = true,
            autoHide = true,
            progress = true,
            sound = false,
            actions = []
        } = options;
        
        const notificationHtml = this.createNotificationHTML({
            type, title, message, duration, position, 
            dismissible, autoHide, progress, sound, actions
        });
        
        const container = document.getElementById('notification-stack');
        container.insertAdjacentHTML('beforeend', notificationHtml);
        
        // Initialize Alpine.js for the new notification
        const newNotification = container.lastElementChild;
        Alpine.initTree(newNotification);
        
        return newNotification;
    },
    
    createNotificationHTML(options) {
        const typeClasses = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        };
        
        const typeIcons = {
            'success': 'fas fa-check-circle',
            'error': 'fas fa-exclamation-triangle',
            'warning': 'fas fa-exclamation-circle',
            'info': 'fas fa-info-circle'
        };
        
        const alertClass = typeClasses[options.type] || 'alert-info';
        const icon = typeIcons[options.type] || 'fas fa-info-circle';
        
        return `
            <div class="notification-item mb-3" 
                 x-data="notification()" 
                 x-init="init()"
                 x-show="visible"
                 x-transition:enter="transition ease-out duration-300"
                 x-transition:enter-start="opacity-0 transform translate-x-full"
                 x-transition:enter-end="opacity-100 transform translate-x-0"
                 x-transition:leave="transition ease-in duration-200"
                 x-transition:leave-start="opacity-100 transform translate-x-0"
                 x-transition:leave-end="opacity-0 transform translate-x-full">
                
                <div class="alert ${alertClass} alert-dismissible border-0 shadow-lg" 
                     style="min-width: 350px; max-width: 500px;">
                    
                    ${options.progress && options.autoHide ? `
                    <div class="notification-progress">
                        <div class="progress-bar bg-${options.type}" 
                             :style="\`width: \${progressWidth}%\`"></div>
                    </div>
                    ` : ''}
                    
                    <div class="d-flex align-items-start">
                        <div class="notification-icon me-3">
                            <i class="${icon} fs-4"></i>
                        </div>
                        
                        <div class="notification-content flex-grow-1">
                            ${options.title ? `<h6 class="notification-title mb-1 fw-bold">${options.title}</h6>` : ''}
                            <div class="notification-message">${options.message}</div>
                            
                            ${options.actions.length > 0 ? `
                            <div class="notification-actions mt-2">
                                ${options.actions.map(action => `
                                    <button type="button" 
                                            class="btn btn-sm ${action.class || 'btn-outline-primary'} me-2"
                                            ${action.onclick ? `onclick="${action.onclick}"` : ''}
                                            ${action.href ? `onclick="window.location.href='${action.href}'"` : ''}>
                                        ${action.icon ? `<i class="${action.icon} me-1"></i>` : ''}
                                        ${action.text}
                                    </button>
                                `).join('')}
                            </div>
                            ` : ''}
                        </div>
                        
                        ${options.dismissible ? `
                        <button type="button" 
                                class="btn-close" 
                                x-on:click="close()"
                                aria-label="إغلاق"></button>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;
    },
    
    success(message, title = 'نجح!', options = {}) {
        return this.show({ ...options, type: 'success', title, message, sound: true });
    },
    
    error(message, title = 'خطأ!', options = {}) {
        return this.show({ ...options, type: 'error', title, message, sound: true });
    },
    
    warning(message, title = 'تحذير!', options = {}) {
        return this.show({ ...options, type: 'warning', title, message });
    },
    
    info(message, title = 'معلومة', options = {}) {
        return this.show({ ...options, type: 'info', title, message });
    },
    
    clearAll() {
        const container = document.getElementById('notification-stack');
        container.innerHTML = '';
    }
};

// Laravel Integration
@if(session()->has('notification'))
document.addEventListener('DOMContentLoaded', function() {
    const notification = @json(session('notification'));
    NotificationSystem.show(notification);
});
@endif
</script>

<style>
.notification-container,
.notification-stack {
    pointer-events: none;
}

.notification-container > *,
.notification-stack > * {
    pointer-events: auto;
}

.notification-stack.top-right {
    top: 20px;
    right: 20px;
}

.notification-stack.top-left {
    top: 20px;
    left: 20px;
}

.notification-stack.bottom-right {
    bottom: 20px;
    right: 20px;
}

.notification-stack.bottom-left {
    bottom: 20px;
    left: 20px;
}

.notification-stack.top-center {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
}

.notification-alert {
    border-radius: 12px;
    border-left: 4px solid;
    backdrop-filter: blur(10px);
}

.alert-success {
    background: rgba(25, 135, 84, 0.1);
    border-left-color: #198754;
    color: #0f5132;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    border-left-color: #dc3545;
    color: #842029;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    border-left-color: #ffc107;
    color: #664d03;
}

.alert-info {
    background: rgba(13, 202, 240, 0.1);
    border-left-color: #0dcaf0;
    color: #055160;
}

.notification-progress {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 12px 12px 0 0;
    overflow: hidden;
}

.notification-progress .progress-bar {
    height: 100%;
    transition: width 0.1s linear;
}

.notification-icon {
    flex-shrink: 0;
}

.notification-title {
    font-size: 1rem;
}

.notification-message {
    font-size: 0.875rem;
    line-height: 1.4;
}

.notification-actions .btn {
    font-size: 0.75rem;
    padding: 0.25rem 0.75rem;
}

.notification-item {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .alert-success {
        background: rgba(25, 135, 84, 0.2);
        color: #75b798;
    }
    
    .alert-danger {
        background: rgba(220, 53, 69, 0.2);
        color: #ea868f;
    }
    
    .alert-warning {
        background: rgba(255, 193, 7, 0.2);
        color: #ffda6a;
    }
    
    .alert-info {
        background: rgba(13, 202, 240, 0.2);
        color: #6edff6;
    }
}
</style>
