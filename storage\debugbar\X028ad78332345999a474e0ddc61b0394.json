{"__meta": {"id": "X028ad78332345999a474e0ddc61b0394", "datetime": "2025-07-26 02:40:01", "utime": **********.619019, "method": "GET", "uri": "/", "ip": "127.0.0.1"}, "php": {"version": "8.2.12", "interface": "cli-server"}, "messages": {"count": 1, "messages": [{"message": "[02:40:01] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\xampp2\\htdocs\\‏‏Meta\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": **********.539306, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.266181, "end": **********.61904, "duration": 0.3528590202331543, "duration_str": "353ms", "measures": [{"label": "Booting", "start": **********.266181, "relative_start": 0, "end": **********.522213, "relative_end": **********.522213, "duration": 0.25603199005126953, "duration_str": "256ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": **********.522226, "relative_start": 0.****************, "end": **********.619041, "relative_end": 9.5367431640625e-07, "duration": 0.*****************, "duration_str": "96.81ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "22MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 0, "templates": []}, "route": {"uri": "GET /", "middleware": "web", "uses": "Closure() {#507\n  class: \"Illuminate\\Routing\\RouteFileRegistrar\"\n  this: Illuminate\\Routing\\RouteFileRegistrar {#505 …}\n  file: \"C:\\xampp2\\htdocs\\‏‏Meta\\routes\\web.php\"\n  line: \"21 to 23\"\n}", "namespace": null, "prefix": "", "where": [], "as": "home", "file": "<a href=\"phpstorm://open?file=C:\\xampp2\\htdocs\\‏‏Meta\\routes\\web.php&line=21\">\\routes\\web.php:21-23</a>"}, "queries": {"nb_statements": 4, "nb_failed_statements": 0, "accumulated_duration": 0.024779999999999996, "accumulated_duration_str": "24.78ms", "statements": [{"sql": "select * from \"sessions\" where \"id\" = '0eE4pExfnJoPMhAqLOpIHYTuhTua30THgPEzE3NC' limit 1", "type": "query", "params": [], "bindings": ["0eE4pExfnJoPMhAqLOpIHYTuhTua30THgPEzE3NC"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 100}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 87}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 147}], "duration": 0.00061, "duration_str": "610μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php:100", "connection": "C:\\xampp2\\htdocs\\‏‏Meta\\database\\database.sqlite", "start_percent": 0, "width_percent": 2.462}, {"sql": "select * from \"sessions\" where \"id\" = '0eE4pExfnJoPMhAqLOpIHYTuhTua30THgPEzE3NC' limit 1", "type": "query", "params": [], "bindings": ["0eE4pExfnJoPMhAqLOpIHYTuhTua30THgPEzE3NC"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 100}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 140}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 236}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "duration": 0.00017999999999999998, "duration_str": "180μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php:100", "connection": "C:\\xampp2\\htdocs\\‏‏Meta\\database\\database.sqlite", "start_percent": 2.462, "width_percent": 0.726}, {"sql": "insert into \"sessions\" (\"payload\", \"last_activity\", \"user_id\", \"ip_address\", \"user_agent\", \"id\") values ('YTozOntzOjY6Il90b2tlbiI7czo0MDoidkJVbnNGcW9kM2g3WjJnUWtlNlZlSWxWTnhlQXpLMXdkbjg4Q0xRZyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', **********, '', '127.0.0.1', 'Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4768', '0eE4pExfnJoPMhAqLOpIHYTuhTua30THgPEzE3NC')", "type": "query", "params": [], "bindings": ["YTozOntzOjY6Il90b2tlbiI7czo0MDoidkJVbnNGcW9kM2g3WjJnUWtlNlZlSWxWTnhlQXpLMXdkbjg4Q0xRZyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MjE6Imh0dHA6Ly8xMjcuMC4wLjE6ODAwMCI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=", "**********", "", "127.0.0.1", "Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4768", "0eE4pExfnJoPMhAqLOpIHYTuhTua30THgPEzE3NC"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 162}, {"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 146}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 128}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 236}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php", "line": 130}], "duration": 0.023809999999999998, "duration_str": "23.81ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php:162", "connection": "C:\\xampp2\\htdocs\\‏‏Meta\\database\\database.sqlite", "start_percent": 3.188, "width_percent": 96.086}, {"sql": "select * from \"sessions\" where \"id\" = '0eE4pExfnJoPMhAqLOpIHYTuhTua30THgPEzE3NC' limit 1", "type": "query", "params": [], "bindings": ["0eE4pExfnJoPMhAqLOpIHYTuhTua30THgPEzE3NC"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php", "line": 100}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 97}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 87}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Store.php", "line": 71}, {"index": 20, "namespace": null, "name": "\\vendor\\maximebf\\debugbar\\src\\DebugBar\\DebugBar.php", "line": 446}], "duration": 0.00017999999999999998, "duration_str": "180μs", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\DatabaseSessionHandler.php:100", "connection": "C:\\xampp2\\htdocs\\‏‏Meta\\database\\database.sqlite", "start_percent": 99.274, "width_percent": 0.726}]}, "models": {"data": [], "count": 0}, "livewire": {"data": [], "count": 0}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "vBUnsFqod3h7Z2gQke6VeIlVNxeAzK1wdn88CLQg", "_previous": "array:1 [\n  \"url\" => \"http://127.0.0.1:8000\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/", "status_code": "<pre class=sf-dump id=sf-dump-145829233 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-145829233\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-1163341848 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1163341848\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-621045771 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-621045771\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1508660002 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"81 characters\">Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4768</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">Keep-Alive</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1508660002\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-847979428 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"30 characters\">C:\\xampp2\\htdocs\\&#8207;&#8207;Meta\\public</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60662</span>\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"29 characters\">PHP 8.2.12 Development Server</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"4 characters\">8000</span>\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str>/</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"40 characters\">C:\\xampp2\\htdocs\\&#8207;&#8207;Meta\\public\\index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"81 characters\">Mozilla/5.0 (Windows NT; Windows NT 10.0; fr-FR) WindowsPowerShell/5.1.26100.4768</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">127.0.0.1:8000</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Keep-Alive</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>**********.2662</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>**********</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-847979428\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-290665801 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-290665801\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1497957251 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Sat, 26 Jul 2025 02:40:01 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InZrdHB1YnBlZEhURklINnV2UUtTbWc9PSIsInZhbHVlIjoiYTg4WEhpdG9sT0dZbnlDU2tIcFBESlJvcEQyaGZWbGRLb0lYWGMrZksxdzhHeXE3aE9YZXBXMmdKS255aDJlNFhXUnMvbEpicnZRQ3NodStBWVJMRXJNSWttMUtZM2hUMyt5Vy8wNWg5OVozV3ZWZU05bDJ2eEd2TnFVbFpRZzciLCJtYWMiOiI1MDE2OTZjMzQ2M2UyZDQ2ZGExMmUxNmZkNjNiOTk1NTdhZDJmOThjN2JjNzQ0NDRhOWIzMTQwNGM2NzA2ZmY2IiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:40:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"479 characters\">mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session=eyJpdiI6IkNmdUc5VHc4UThrM2FSQTQ3K0daRlE9PSIsInZhbHVlIjoicWk3cHFVY2E2MXFXQWdqVk5qOHBUYWpjRmNpVFhWQ2FBS0RDYWtreXIvT1loaE9ORmFPSURLZ1JuV1l6RzBmYjdjTWZ4U0pmbTRwNmlHVTRSZ1kwTktuK3RvSTdkNUxiZVJJbUtaL3Y4SHI3VHRxSUkzZ1NVYlR5WUUyZzJyWkoiLCJtYWMiOiIzNGQ1NDYzMzY0N2RjZGExNThlNjgzNTdmZjdiOTc3NjFhNDljZTlmYmY3NDMyMTJjNjE1MGRkZmM0ZjQ5OGQ3IiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:40:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InZrdHB1YnBlZEhURklINnV2UUtTbWc9PSIsInZhbHVlIjoiYTg4WEhpdG9sT0dZbnlDU2tIcFBESlJvcEQyaGZWbGRLb0lYWGMrZksxdzhHeXE3aE9YZXBXMmdKS255aDJlNFhXUnMvbEpicnZRQ3NodStBWVJMRXJNSWttMUtZM2hUMyt5Vy8wNWg5OVozV3ZWZU05bDJ2eEd2TnFVbFpRZzciLCJtYWMiOiI1MDE2OTZjMzQ2M2UyZDQ2ZGExMmUxNmZkNjNiOTk1NTdhZDJmOThjN2JjNzQ0NDRhOWIzMTQwNGM2NzA2ZmY2IiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:40:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"451 characters\">mytaaa_tk_llbrmg_otsmym_almoakaa_oalttbykat_session=eyJpdiI6IkNmdUc5VHc4UThrM2FSQTQ3K0daRlE9PSIsInZhbHVlIjoicWk3cHFVY2E2MXFXQWdqVk5qOHBUYWpjRmNpVFhWQ2FBS0RDYWtreXIvT1loaE9ORmFPSURLZ1JuV1l6RzBmYjdjTWZ4U0pmbTRwNmlHVTRSZ1kwTktuK3RvSTdkNUxiZVJJbUtaL3Y4SHI3VHRxSUkzZ1NVYlR5WUUyZzJyWkoiLCJtYWMiOiIzNGQ1NDYzMzY0N2RjZGExNThlNjgzNTdmZjdiOTc3NjFhNDljZTlmYmY3NDMyMTJjNjE1MGRkZmM0ZjQ5OGQ3IiwidGFnIjoiIn0%3D; expires=Sat, 26-Jul-2025 04:40:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1497957251\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-931416890 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vBUnsFqod3h7Z2gQke6VeIlVNxeAzK1wdn88CLQg</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://127.0.0.1:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-931416890\", {\"maxDepth\":0})</script>\n"}}