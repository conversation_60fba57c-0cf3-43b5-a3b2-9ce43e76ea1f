# Root Directory Protection for Laravel Application
# This file protects sensitive files and directories from web access

# Deny access to all files in root directory
<Files "*">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

# Allow access only to public directory
<Directory "public">
    Order allow,deny
    Allow from all
</Directory>

# Redirect all requests to public directory
RewriteEngine On
RewriteRule ^(.*)$ public/$1 [L]

# Protect sensitive files
<Files ".env*">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "composer.*">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "*.md">
    Order allow,deny
    <PERSON><PERSON> from all
</Files>

<Files "artisan">
    Order allow,deny
    <PERSON>y from all
</Files>

# Protect directories
<Directory "app">
    Order allow,deny
    <PERSON>y from all
</Directory>

<Directory "bootstrap">
    Order allow,deny
    <PERSON><PERSON> from all
</Directory>

<Directory "config">
    Order allow,deny
    Deny from all
</Directory>

<Directory "database">
    Order allow,deny
    <PERSON>y from all
</Directory>

<Directory "resources">
    Order allow,deny
    Deny from all
</Directory>

<Directory "routes">
    Order allow,deny
    <PERSON><PERSON> from all
</Directory>

<Directory "storage">
    Order allow,deny
    <PERSON><PERSON> from all
</Directory>

<Directory "vendor">
    Order allow,deny
    <PERSON><PERSON> from all
</Directory>

# Prevent directory browsing
Options -Indexes

# Security headers
<IfModule mod_headers.c>
    Header always set X-Content-Type-Options nosniff
    Header always set X-Frame-Options DENY
    Header always set X-XSS-Protection "1; mode=block"
</IfModule>
