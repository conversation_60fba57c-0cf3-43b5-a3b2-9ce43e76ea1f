<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>تسجيل الدخول - لوحة التحكم الإدارية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 20px;
        }
        
        .login-container {
            background: rgba(55, 65, 81, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2.5rem;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 2rem;
        }
        
        .login-title {
            font-size: 1.8rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
            color: white;
        }
        
        .login-subtitle {
            color: rgba(255, 255, 255, 0.7);
            font-size: 0.9rem;
            margin-bottom: 0;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            color: rgba(255, 255, 255, 0.9);
            font-weight: 500;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }
        
        .form-control {
            background: rgba(31, 41, 55, 0.8);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 12px;
            padding: 0.75rem 1rem;
            color: white;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .form-control:focus {
            background: rgba(31, 41, 55, 0.9);
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
            color: white;
        }
        
        .form-control::placeholder {
            color: rgba(255, 255, 255, 0.5);
        }
        
        .user-type-selector {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 0.75rem;
            margin-bottom: 1.5rem;
        }
        
        .user-type-option {
            background: rgba(31, 41, 55, 0.6);
            border: 2px solid rgba(75, 85, 99, 0.5);
            border-radius: 12px;
            padding: 1rem;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            color: rgba(255, 255, 255, 0.7);
        }
        
        .user-type-option:hover {
            border-color: rgba(102, 126, 234, 0.5);
            background: rgba(31, 41, 55, 0.8);
        }
        
        .user-type-option.active {
            border-color: #e91e63;
            background: rgba(233, 30, 99, 0.1);
            color: #e91e63;
        }
        
        .user-type-option i {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
            display: block;
        }
        
        .captcha-container {
            background: rgba(31, 41, 55, 0.6);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 12px;
            padding: 1rem;
            margin-bottom: 1rem;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .captcha-refresh {
            background: rgba(233, 30, 99, 0.2);
            border: 1px solid #e91e63;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #e91e63;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .captcha-refresh:hover {
            background: rgba(233, 30, 99, 0.3);
            transform: rotate(180deg);
        }
        
        .captcha-code {
            background: white;
            color: #333;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: bold;
            font-size: 1.1rem;
            letter-spacing: 2px;
            font-family: monospace;
        }
        
        .form-check {
            margin-bottom: 1.5rem;
        }
        
        .form-check-input {
            background: rgba(31, 41, 55, 0.8);
            border: 1px solid rgba(75, 85, 99, 0.5);
            border-radius: 4px;
        }
        
        .form-check-input:checked {
            background-color: #e91e63;
            border-color: #e91e63;
        }
        
        .form-check-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 0.9rem;
        }
        
        .forgot-password {
            color: #e91e63;
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.3s ease;
        }
        
        .forgot-password:hover {
            color: #f06292;
            text-decoration: underline;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            margin-bottom: 1.5rem;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(233, 30, 99, 0.4);
            background: linear-gradient(135deg, #f06292 0%, #c2185b 100%);
        }
        
        .btn-login:active {
            transform: translateY(0);
        }
        
        .help-link {
            text-align: center;
            margin-top: 1rem;
        }
        
        .help-link a {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            font-size: 0.85rem;
            transition: all 0.3s ease;
        }
        
        .help-link a:hover {
            color: rgba(255, 255, 255, 0.9);
        }
        
        .help-link i {
            margin-left: 0.5rem;
        }
        
        /* Loading Animation */
        .btn-login.loading {
            pointer-events: none;
            opacity: 0.7;
        }
        
        .btn-login.loading::after {
            content: '';
            width: 16px;
            height: 16px;
            border: 2px solid transparent;
            border-top: 2px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 0.5rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Responsive */
        @media (max-width: 480px) {
            .login-container {
                padding: 2rem 1.5rem;
                margin: 10px;
            }
            
            .login-title {
                font-size: 1.5rem;
            }
            
            .user-type-selector {
                grid-template-columns: 1fr;
            }
        }
        
        /* Animation */
        .login-container {
            animation: fadeInUp 0.6s ease;
        }
        
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1 class="login-title">تسجيل الدخول</h1>
            <p class="login-subtitle">أدخل بياناتك للوصول إلى صفحة حسابك</p>
        </div>
        
        <form id="loginForm" onsubmit="handleLogin(event)">
            <!-- Email Field -->
            <div class="form-group">
                <label class="form-label">البريد الإلكتروني أو اسم المستخدم</label>
                <input type="text" class="form-control" id="email" name="email" 
                       placeholder="أدخل البريد الإلكتروني أو اسم المستخدم" required>
            </div>
            
            <!-- Password Field -->
            <div class="form-group">
                <label class="form-label">كلمة المرور</label>
                <input type="password" class="form-control" id="password" name="password" 
                       placeholder="أدخل كلمة المرور" required>
            </div>
            
            <!-- User Type Selector -->
            <div class="form-group">
                <label class="form-label">نوع المستخدم</label>
                <div class="user-type-selector">
                    <div class="user-type-option" onclick="selectUserType('employee')">
                        <i class="fas fa-user"></i>
                        <div>موظف</div>
                    </div>
                    <div class="user-type-option active" onclick="selectUserType('admin')">
                        <i class="fas fa-user-shield"></i>
                        <div>مدير</div>
                    </div>
                </div>
                <input type="hidden" id="userType" name="userType" value="admin">
            </div>
            
            <!-- Captcha -->
            <div class="form-group">
                <label class="form-label">رمز التحقق</label>
                <div class="captcha-container">
                    <div class="captcha-refresh" onclick="refreshCaptcha()" title="تحديث الرمز">
                        <i class="fas fa-sync-alt"></i>
                    </div>
                    <div class="captcha-code" id="captchaCode">9aEXb</div>
                </div>
                <input type="text" class="form-control" id="captchaInput" name="captcha" 
                       placeholder="أدخل رمز التحقق" required>
            </div>
            
            <!-- Remember Me -->
            <div class="form-check">
                <input class="form-check-input" type="checkbox" id="rememberMe" name="rememberMe">
                <label class="form-check-label" for="rememberMe">
                    تذكرني
                    <a href="#" class="forgot-password float-end">نسيت كلمة المرور؟</a>
                </label>
            </div>
            
            <!-- Login Button -->
            <button type="submit" class="btn btn-login" id="loginBtn">
                تسجيل الدخول
            </button>
        </form>
        
        <!-- Help Link -->
        <div class="help-link">
            <a href="#" onclick="showHelp()">
                <i class="fas fa-question-circle"></i>
                احتاج إلى مساعدة
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script>
        let currentCaptcha = '9aEXb';
        
        // Generate random captcha
        function generateCaptcha() {
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
            let result = '';
            for (let i = 0; i < 5; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            return result;
        }
        
        // Refresh captcha
        function refreshCaptcha() {
            currentCaptcha = generateCaptcha();
            document.getElementById('captchaCode').textContent = currentCaptcha;
            document.getElementById('captchaInput').value = '';
            
            // Add rotation animation
            const refreshBtn = document.querySelector('.captcha-refresh');
            refreshBtn.style.transform = 'rotate(360deg)';
            setTimeout(() => {
                refreshBtn.style.transform = 'rotate(0deg)';
            }, 300);
        }
        
        // Select user type
        function selectUserType(type) {
            // Remove active class from all options
            document.querySelectorAll('.user-type-option').forEach(option => {
                option.classList.remove('active');
            });
            
            // Add active class to selected option
            event.target.closest('.user-type-option').classList.add('active');
            
            // Update hidden input
            document.getElementById('userType').value = type;
        }
        
        // Handle login form submission
        function handleLogin(event) {
            event.preventDefault();
            
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            const captchaInput = document.getElementById('captchaInput').value;
            const userType = document.getElementById('userType').value;
            const rememberMe = document.getElementById('rememberMe').checked;
            
            // Validate captcha
            if (captchaInput !== currentCaptcha) {
                Swal.fire({
                    icon: 'error',
                    title: 'خطأ في رمز التحقق',
                    text: 'رمز التحقق غير صحيح، يرجى المحاولة مرة أخرى',
                    confirmButtonText: 'حسناً',
                    confirmButtonColor: '#e91e63'
                });
                refreshCaptcha();
                return;
            }
            
            // Show loading state
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.classList.add('loading');
            loginBtn.textContent = 'جاري تسجيل الدخول...';
            
            // Simulate login process
            setTimeout(() => {
                // Demo credentials
                if ((email === '<EMAIL>' || email === 'admin') && password === 'admin123') {
                    // Success
                    Swal.fire({
                        icon: 'success',
                        title: 'مرحباً بك!',
                        text: 'تم تسجيل الدخول بنجاح',
                        showConfirmButton: false,
                        timer: 2000,
                        timerProgressBar: true,
                        background: '#374151',
                        color: '#e5e7eb',
                        iconColor: '#10b981'
                    }).then(() => {
                        // Save remember me preference
                        if (rememberMe) {
                            localStorage.setItem('rememberMe', 'true');
                            localStorage.setItem('userEmail', email);
                        }

                        // Redirect to dashboard
                        window.location.href = 'admin-dashboard.php';
                    });
                } else {
                    // Error
                    Swal.fire({
                        icon: 'error',
                        title: 'خطأ في تسجيل الدخول',
                        text: 'بيانات الدخول غير صحيحة، يرجى التحقق من البريد الإلكتروني وكلمة المرور',
                        confirmButtonText: 'المحاولة مرة أخرى',
                        confirmButtonColor: '#e91e63',
                        background: '#374151',
                        color: '#e5e7eb'
                    });
                    refreshCaptcha();
                }
                
                // Reset button
                loginBtn.classList.remove('loading');
                loginBtn.textContent = 'تسجيل الدخول';
            }, 2000);
        }
        
        // Show help modal
        function showHelp() {
            Swal.fire({
                title: '<i class="fas fa-question-circle text-info"></i> مساعدة تسجيل الدخول',
                html: `
                    <div class="text-start" style="direction: rtl;">
                        <h6 class="text-primary mb-3"><i class="fas fa-key me-2"></i>بيانات تجريبية:</h6>
                        <div class="mb-2">
                            <strong>البريد الإلكتروني:</strong>
                            <code class="bg-light px-2 py-1 rounded"><EMAIL></code> أو
                            <code class="bg-light px-2 py-1 rounded">admin</code>
                        </div>
                        <div class="mb-3">
                            <strong>كلمة المرور:</strong>
                            <code class="bg-light px-2 py-1 rounded">admin123</code>
                        </div>
                        <hr>
                        <h6 class="text-success mb-2"><i class="fas fa-lightbulb me-2"></i>نصائح:</h6>
                        <ul class="text-muted small">
                            <li>تأكد من إدخال رمز التحقق بشكل صحيح</li>
                            <li>يمكنك تحديث رمز التحقق بالنقر على زر التحديث</li>
                            <li>فعّل "تذكرني" لحفظ بيانات الدخول</li>
                        </ul>
                        <hr>
                        <div class="text-center">
                            <small class="text-muted">
                                <i class="fas fa-envelope me-1"></i>
                                للدعم الفني: <strong><EMAIL></strong>
                            </small>
                        </div>
                    </div>
                `,
                width: 600,
                confirmButtonText: 'فهمت',
                confirmButtonColor: '#667eea',
                background: '#374151',
                color: '#e5e7eb',
                customClass: {
                    popup: 'text-end'
                }
            });
        }
        
        // Load saved email if remember me was checked
        document.addEventListener('DOMContentLoaded', function() {
            if (localStorage.getItem('rememberMe') === 'true') {
                const savedEmail = localStorage.getItem('userEmail');
                if (savedEmail) {
                    document.getElementById('email').value = savedEmail;
                    document.getElementById('rememberMe').checked = true;
                }
            }
        });
        
        // Add input animations
        document.querySelectorAll('.form-control').forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'scale(1.02)';
                this.parentElement.style.transition = 'transform 0.3s ease';
            });
            
            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'scale(1)';
            });
        });
    </script>
</body>
</html>
