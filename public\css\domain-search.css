/* Domain Search Page Styles */
.domain-search-container {
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

.domain-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.domain-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.toggle-button {
    position: relative;
    overflow: hidden;
}

.toggle-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.toggle-button:hover::before {
    left: 100%;
}

.search-input {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.search-input:focus {
    background: rgba(255, 255, 255, 1);
    border-color: #8b5cf6;
    box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
}

.search-button {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    position: relative;
    overflow: hidden;
}

.search-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.search-button:hover::before {
    left: 100%;
}

.domain-extension {
    font-family: 'Cairo', sans-serif;
    font-weight: 700;
    letter-spacing: -0.5px;
}

.price-text {
    background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.result-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.result-card:hover {
    transform: translateX(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.status-indicator {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

.order-button {
    background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
    position: relative;
    overflow: hidden;
    transform: perspective(1px) translateZ(0);
}

.order-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.order-button:hover::before {
    left: 100%;
}

.loading-spinner {
    border: 3px solid #f3f4f6;
    border-top: 3px solid #8b5cf6;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Arabic font improvements */
.arabic-text {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    font-weight: 500;
    line-height: 1.6;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .domain-card {
        margin-bottom: 1rem;
    }
    
    .search-input {
        font-size: 16px; /* Prevent zoom on iOS */
    }
    
    .toggle-button {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
    }
}

/* RTL improvements */
[dir="rtl"] .result-card:hover {
    transform: translateX(5px);
}

[dir="rtl"] .search-input {
    text-align: right;
}

/* Accessibility improvements */
.domain-card:focus-within {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
}

.toggle-button:focus {
    outline: 2px solid #8b5cf6;
    outline-offset: 2px;
}

/* Animation for domain cards */
.domain-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Stagger animation for domain grid */
.domain-card:nth-child(1) { animation-delay: 0.1s; }
.domain-card:nth-child(2) { animation-delay: 0.2s; }
.domain-card:nth-child(3) { animation-delay: 0.3s; }
.domain-card:nth-child(4) { animation-delay: 0.4s; }
.domain-card:nth-child(5) { animation-delay: 0.5s; }
.domain-card:nth-child(6) { animation-delay: 0.6s; }
