/* Fixed Sidebar CSS - Ultimate Override */

/* Critical: Override all AdminLTE sidebar positioning */
.main-sidebar,
.main-sidebar.sidebar-dark-primary,
.sidebar-mini .main-sidebar,
.sidebar-mini-md .main-sidebar,
.sidebar-mini-xs .main-sidebar,
.sidebar-collapse .main-sidebar,
body.sidebar-mini .main-sidebar,
body.sidebar-collapse .main-sidebar {
    position: fixed !important;
    top: 0 !important;
    right: 0 !important;
    left: auto !important;
    width: 250px !important;
    height: 100vh !important;
    z-index: 1050 !important;
    transform: translateX(0) !important;
    transition: none !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
    margin: 0 !important;
    background: linear-gradient(180deg, #343a40 0%, #495057 100%) !important;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
}

/* Force content wrapper positioning - Override all states */
.content-wrapper,
.sidebar-mini .content-wrapper,
.sidebar-mini-md .content-wrapper,
.sidebar-mini-xs .content-wrapper,
.sidebar-collapse .content-wrapper,
body.sidebar-mini .content-wrapper,
body.sidebar-collapse .content-wrapper {
    margin-right: 250px !important;
    margin-left: 0 !important;
    min-height: 100vh !important;
    position: relative !important;
    padding-top: 60px !important;
    transition: none !important;
}

/* Force header positioning - Override all states */
.main-header,
.main-header.navbar,
.sidebar-mini .main-header,
.sidebar-mini-md .main-header,
.sidebar-mini-xs .main-header,
.sidebar-collapse .main-header,
body.sidebar-mini .main-header,
body.sidebar-collapse .main-header {
    margin-right: 250px !important;
    margin-left: 0 !important;
    position: fixed !important;
    top: 0 !important;
    right: 250px !important;
    left: 0 !important;
    width: calc(100% - 250px) !important;
    z-index: 1001 !important;
    transition: none !important;
}

/* Critical: Override ALL AdminLTE responsive and collapse behaviors */
@media (max-width: 991.98px) {
    .main-sidebar,
    .sidebar-mini .main-sidebar,
    .sidebar-collapse .main-sidebar,
    body.sidebar-mini .main-sidebar,
    body.sidebar-collapse .main-sidebar {
        position: fixed !important;
        right: 0 !important;
        left: auto !important;
        width: 250px !important;
        transform: translateX(0) !important;
        margin-right: 0 !important;
        visibility: visible !important;
        opacity: 1 !important;
        display: block !important;
    }

    .content-wrapper,
    .sidebar-mini .content-wrapper,
    .sidebar-collapse .content-wrapper {
        margin-right: 250px !important;
        margin-left: 0 !important;
    }

    .main-header,
    .sidebar-mini .main-header,
    .sidebar-collapse .main-header {
        margin-right: 250px !important;
        margin-left: 0 !important;
        width: calc(100% - 250px) !important;
    }
}

/* Override ALL sidebar states - Critical */
.sidebar-collapse .main-sidebar,
.sidebar-mini .main-sidebar,
.sidebar-mini-md .main-sidebar,
.sidebar-mini-xs .main-sidebar,
body.sidebar-collapse .main-sidebar,
body.sidebar-mini .main-sidebar,
body.sidebar-mini-md .main-sidebar,
body.sidebar-mini-xs .main-sidebar,
.wrapper.sidebar-collapse .main-sidebar,
.wrapper.sidebar-mini .main-sidebar {
    position: fixed !important;
    right: 0 !important;
    left: auto !important;
    width: 250px !important;
    transform: translateX(0) !important;
    margin-right: 0 !important;
    visibility: visible !important;
    opacity: 1 !important;
    display: block !important;
    height: 100vh !important;
    z-index: 1050 !important;
}

/* Critical: Ensure wrapper and body don't interfere */
.wrapper,
.wrapper.sidebar-collapse,
.wrapper.sidebar-mini,
body.sidebar-collapse .wrapper,
body.sidebar-mini .wrapper {
    position: relative !important;
    min-height: 100vh !important;
    overflow-x: hidden !important;
}

/* Body adjustments - Override all states */
body,
body.hold-transition,
body.sidebar-collapse,
body.sidebar-mini,
body.sidebar-mini-md,
body.sidebar-mini-xs {
    overflow-x: hidden !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Critical: Prevent AdminLTE from hiding sidebar */
.main-sidebar[style*="display: none"],
.main-sidebar[style*="visibility: hidden"],
.main-sidebar[style*="opacity: 0"] {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Critical: Override any transform animations */
.main-sidebar[style*="transform"],
.main-sidebar[style*="translateX"] {
    transform: translateX(0) !important;
}

/* Prevent any layout shifts */
.main-sidebar,
.content-wrapper,
.main-header {
    backface-visibility: hidden !important;
    -webkit-backface-visibility: hidden !important;
}

/* Smooth scrolling for sidebar content */
.main-sidebar .sidebar {
    overflow-y: auto !important;
    overflow-x: hidden !important;
    height: 100vh !important;
    scroll-behavior: smooth !important;
}

/* Custom scrollbar for sidebar */
.main-sidebar::-webkit-scrollbar {
    width: 6px !important;
}

.main-sidebar::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1) !important;
}

.main-sidebar::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3) !important;
    border-radius: 3px !important;
}

.main-sidebar::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5) !important;
}

/* Ensure navigation items are properly styled */
.nav-sidebar .nav-link {
    color: #ecf0f1 !important;
    transition: all 0.3s ease !important;
}

.nav-sidebar .nav-link:hover {
    background-color: rgba(255,255,255,0.1) !important;
    color: #ffffff !important;
}

.nav-sidebar .nav-link.active {
    background-color: #007bff !important;
    color: #ffffff !important;
}

/* Brand link styling */
.brand-link {
    border-bottom: 1px solid rgba(255,255,255,0.1) !important;
    padding: 0.8125rem 1rem !important;
    display: block !important;
    color: #ecf0f1 !important;
    text-decoration: none !important;
}

.brand-link:hover {
    color: #ffffff !important;
    text-decoration: none !important;
}
