<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;

class SocialAuthController extends Controller
{
    /**
     * Redirect to Google OAuth
     */
    public function redirectToGoogle()
    {
        return Socialite::driver('google')->redirect();
    }

    /**
     * Handle Google OAuth callback
     */
    public function handleGoogleCallback()
    {
        try {
            $googleUser = Socialite::driver('google')->user();
            
            // Check if user already exists
            $user = User::where('email', $googleUser->getEmail())->first();
            
            if ($user) {
                // User exists, log them in
                Auth::login($user);
                return redirect()->intended('/dashboard')->with('success', 'تم تسجيل الدخول بنجاح عبر Google');
            } else {
                // Create new user
                $user = User::create([
                    'name' => $googleUser->getName(),
                    'email' => $googleUser->getEmail(),
                    'email_verified_at' => now(),
                    'password' => Hash::make(Str::random(24)), // Random password since they'll use Google
                    'google_id' => $googleUser->getId(),
                    'avatar' => $googleUser->getAvatar(),
                ]);
                
                Auth::login($user);
                return redirect()->intended(route('home'))->with('success', 'تم إنشاء حسابك بنجاح عبر Google');
            }
            
        } catch (\Exception $e) {
            return redirect('/login')->with('error', 'حدث خطأ أثناء تسجيل الدخول عبر Google. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * Redirect to Facebook OAuth
     */
    public function redirectToFacebook()
    {
        try {
            return Socialite::driver('facebook')->redirect();
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'خطأ في الاتصال بـ Facebook. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * Handle Facebook OAuth callback
     */
    public function handleFacebookCallback()
    {
        try {
            $facebookUser = Socialite::driver('facebook')->user();

            // Check if user already exists
            $user = User::where('email', $facebookUser->getEmail())->first();

            if ($user) {
                // Update existing user with Facebook data if not already set
                if (!$user->facebook_id) {
                    $user->update([
                        'facebook_id' => $facebookUser->getId(),
                        'avatar' => $facebookUser->getAvatar(),
                    ]);
                }

                Auth::login($user);
                return redirect()->intended('/dashboard')->with('success', 'تم تسجيل الدخول بنجاح عبر Facebook');
            } else {
                // Create new user
                $user = User::create([
                    'name' => $facebookUser->getName(),
                    'email' => $facebookUser->getEmail(),
                    'email_verified_at' => now(),
                    'password' => Hash::make(Str::random(24)),
                    'facebook_id' => $facebookUser->getId(),
                    'avatar' => $facebookUser->getAvatar(),
                    'provider' => 'facebook'
                ]);

                Auth::login($user);
                return redirect()->intended('/dashboard')->with('success', 'تم إنشاء حسابك بنجاح عبر Facebook');
            }

        } catch (\Exception $e) {
            return redirect('/login')->with('error', 'حدث خطأ أثناء تسجيل الدخول عبر Facebook. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * Redirect to GitHub OAuth
     */
    public function redirectToGithub()
    {
        try {
            return Socialite::driver('github')->redirect();
        } catch (\Exception $e) {
            return redirect()->route('login')->with('error', 'خطأ في الاتصال بـ GitHub. يرجى المحاولة مرة أخرى.');
        }
    }

    /**
     * Handle GitHub OAuth callback
     */
    public function handleGithubCallback()
    {
        try {
            $githubUser = Socialite::driver('github')->user();

            // Check if user already exists
            $user = User::where('email', $githubUser->getEmail())->first();

            if ($user) {
                // Update existing user with GitHub data if not already set
                if (!$user->github_id) {
                    $user->update([
                        'github_id' => $githubUser->getId(),
                        'avatar' => $githubUser->getAvatar(),
                    ]);
                }

                Auth::login($user);
                return redirect()->intended('/dashboard')->with('success', 'تم تسجيل الدخول بنجاح عبر GitHub');
            } else {
                // Create new user
                $user = User::create([
                    'name' => $githubUser->getName() ?: $githubUser->getNickname(),
                    'email' => $githubUser->getEmail(),
                    'email_verified_at' => now(),
                    'password' => Hash::make(Str::random(24)),
                    'github_id' => $githubUser->getId(),
                    'avatar' => $githubUser->getAvatar(),
                    'provider' => 'github'
                ]);

                Auth::login($user);
                return redirect()->intended('/dashboard')->with('success', 'تم إنشاء حسابك بنجاح عبر GitHub');
            }

        } catch (\Exception $e) {
            return redirect('/login')->with('error', 'حدث خطأ أثناء تسجيل الدخول عبر GitHub. يرجى المحاولة مرة أخرى.');
        }
    }
}
