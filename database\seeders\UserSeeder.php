<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // إنشاء مستخدم تجريبي رئيسي
        User::create([
            'name' => 'أحمد محمد',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'email_verified_at' => now(),
            'phone' => '967782871439',
            'address' => 'صنعاء، اليمن',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // إنشاء مستخدم تجريبي ثاني
        User::create([
            'name' => 'فاطمة علي',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'email_verified_at' => now(),
            'phone' => '967777123456',
            'address' => 'عدن، اليمن',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // إنشاء مستخدم تجريبي ثالث
        User::create([
            'name' => 'محمد سالم',
            'email' => '<EMAIL>',
            'password' => Hash::make('123456'),
            'email_verified_at' => now(),
            'phone' => '967733987654',
            'address' => 'تعز، اليمن',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // إنشاء مستخدم للاختبار
        User::create([
            'name' => 'مستخدم تجريبي',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
            'phone' => '967737662752',
            'address' => 'المكلا، حضرموت، اليمن',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        // إنشاء مستخدمين إضافيين باستخدام Factory
        User::factory(10)->create();
    }
}
