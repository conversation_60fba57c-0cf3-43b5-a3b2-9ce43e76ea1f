<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hosting_providers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Hostinger, DigitalOcean, etc.
            $table->string('type'); // reseller, vps, dedicated
            $table->string('api_endpoint')->nullable();
            $table->string('api_key')->nullable();
            $table->string('api_secret')->nullable();
            $table->json('api_config')->nullable(); // Additional API configuration
            $table->decimal('commission_rate', 5, 2)->default(0); // Commission percentage
            $table->boolean('is_active')->default(true);
            $table->json('supported_features')->nullable(); // Features like SSL, backups, etc.
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hosting_providers');
    }
};
