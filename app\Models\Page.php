<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

class Page extends Model
{
    protected $fillable = [
        'slug',
        'title_ar',
        'title_en',
        'content_ar',
        'content_en',
        'excerpt_ar',
        'excerpt_en',
        'meta_title_ar',
        'meta_title_en',
        'meta_description_ar',
        'meta_description_en',
        'meta_keywords_ar',
        'meta_keywords_en',
        'featured_image',
        'gallery',
        'page_type',
        'template',
        'custom_fields',
        'is_published',
        'show_in_menu',
        'menu_order',
        'published_at',
        'scheduled_at',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'gallery' => 'array',
        'custom_fields' => 'array',
        'is_published' => 'boolean',
        'show_in_menu' => 'boolean',
        'published_at' => 'datetime',
        'scheduled_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($page) {
            if (empty($page->slug)) {
                $page->slug = Str::slug($page->title_ar);
            }
            if (auth()->check()) {
                $page->created_by = auth()->id();
            }
        });

        static::updating(function ($page) {
            if (auth()->check()) {
                $page->updated_by = auth()->id();
            }
        });
    }

    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater(): BelongsTo
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    public function getTitle($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $locale === 'ar' ? $this->title_ar : ($this->title_en ?: $this->title_ar);
    }

    public function getContent($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $locale === 'ar' ? $this->content_ar : ($this->content_en ?: $this->content_ar);
    }

    public function getExcerpt($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        return $locale === 'ar' ? $this->excerpt_ar : ($this->excerpt_en ?: $this->excerpt_ar);
    }

    public function getMetaTitle($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $metaTitle = $locale === 'ar' ? $this->meta_title_ar : ($this->meta_title_en ?: $this->meta_title_ar);
        return $metaTitle ?: $this->getTitle($locale);
    }

    public function getMetaDescription($locale = null)
    {
        $locale = $locale ?: app()->getLocale();
        $metaDesc = $locale === 'ar' ? $this->meta_description_ar : ($this->meta_description_en ?: $this->meta_description_ar);
        return $metaDesc ?: $this->getExcerpt($locale);
    }

    public function scopePublished($query)
    {
        return $query->where('is_published', true)
                    ->where(function ($q) {
                        $q->whereNull('published_at')
                          ->orWhere('published_at', '<=', now());
                    });
    }

    public function scopeInMenu($query)
    {
        return $query->where('show_in_menu', true)->orderBy('menu_order');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('page_type', $type);
    }
}
