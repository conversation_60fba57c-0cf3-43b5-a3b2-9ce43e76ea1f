@extends('layouts.app')

@section('title', $plan['name'] . ' - خطط الاستضافة')

@section('content')
<!-- Hero Section -->
<section class="bg-gradient-to-br from-blue-600 to-purple-700 text-white py-20">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">{{ $plan['name'] }}</h1>
            <p class="text-xl md:text-2xl mb-8">{{ $plan['description'] }}</p>
            <div class="bg-white/10 backdrop-blur-sm rounded-lg p-8 inline-block">
                <div class="text-5xl font-bold mb-2">${{ $plan['price'] }}</div>
                <div class="text-xl">{{ $plan['period'] }}</div>
            </div>
        </div>
    </div>
</section>

<!-- Plan Details -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
                <!-- Features List -->
                <div>
                    <h2 class="text-3xl font-bold text-gray-800 mb-8">مميزات الخطة</h2>
                    <div class="space-y-4">
                        @foreach($plan['features'] as $feature)
                        <div class="flex items-center p-4 bg-gray-50 rounded-lg">
                            <div class="bg-green-100 w-10 h-10 rounded-full flex items-center justify-center ml-4">
                                <i class="fas fa-check text-green-600"></i>
                            </div>
                            <span class="text-gray-700 font-medium">{{ $feature }}</span>
                        </div>
                        @endforeach
                    </div>
                </div>

                <!-- Order Form -->
                <div class="bg-white rounded-lg shadow-lg p-8">
                    <h3 class="text-2xl font-bold text-gray-800 mb-6">اطلب الخطة الآن</h3>
                    
                    <form action="#" method="POST" class="space-y-6">
                        @csrf
                        <input type="hidden" name="plan_id" value="{{ $plan['id'] }}">
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل</label>
                            <input type="text" name="name" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                            <input type="email" name="email" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                            <input type="tel" name="phone" required 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم الدومين (اختياري)</label>
                            <input type="text" name="domain" placeholder="example.com" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <p class="text-sm text-gray-500 mt-1">إذا لم يكن لديك دومين، سنساعدك في اختيار واحد</p>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">ملاحظات إضافية</label>
                            <textarea name="notes" rows="3" 
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                                      placeholder="أي متطلبات خاصة أو ملاحظات..."></textarea>
                        </div>

                        <!-- Order Summary -->
                        <div class="bg-gray-50 rounded-lg p-6">
                            <h4 class="font-semibold text-gray-800 mb-4">ملخص الطلب</h4>
                            <div class="flex justify-between items-center mb-2">
                                <span>{{ $plan['name'] }}</span>
                                <span>${{ $plan['price'] }}</span>
                            </div>
                            <div class="flex justify-between items-center mb-2">
                                <span>مدة الاشتراك</span>
                                <span>{{ $plan['period'] }}</span>
                            </div>
                            <hr class="my-4">
                            <div class="flex justify-between items-center font-bold text-lg">
                                <span>المجموع</span>
                                <span>${{ $plan['price'] }}</span>
                            </div>
                        </div>

                        <button type="submit" 
                                class="w-full bg-blue-600 text-white py-4 rounded-lg font-semibold hover:bg-blue-700 transition duration-300">
                            <i class="fas fa-shopping-cart ml-2"></i>
                            اطلب الآن
                        </button>
                    </form>

                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-500">
                            <i class="fas fa-shield-alt text-green-500 ml-1"></i>
                            دفع آمن ومحمي بتشفير SSL
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Technical Specifications -->
<section class="py-16 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <h2 class="text-3xl font-bold text-gray-800 mb-8 text-center">المواصفات التقنية</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-xl font-semibold mb-4 text-blue-600">
                        <i class="fas fa-server ml-2"></i>
                        مواصفات الخادم
                    </h3>
                    <ul class="space-y-2 text-gray-700">
                        <li><strong>نوع التخزين:</strong> SSD NVMe</li>
                        <li><strong>ذاكرة الوصول العشوائي:</strong> DDR4</li>
                        <li><strong>المعالج:</strong> Intel Xeon</li>
                        <li><strong>نظام التشغيل:</strong> Linux CentOS</li>
                    </ul>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-xl font-semibold mb-4 text-green-600">
                        <i class="fas fa-code ml-2"></i>
                        الدعم التقني
                    </h3>
                    <ul class="space-y-2 text-gray-700">
                        <li><strong>PHP:</strong> 7.4, 8.0, 8.1, 8.2</li>
                        <li><strong>قواعد البيانات:</strong> MySQL, PostgreSQL</li>
                        <li><strong>لوحة التحكم:</strong> cPanel</li>
                        <li><strong>البريد الإلكتروني:</strong> دعم كامل</li>
                    </ul>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-xl font-semibold mb-4 text-purple-600">
                        <i class="fas fa-shield-alt ml-2"></i>
                        الأمان والحماية
                    </h3>
                    <ul class="space-y-2 text-gray-700">
                        <li><strong>شهادة SSL:</strong> مجانية</li>
                        <li><strong>جدار الحماية:</strong> متقدم</li>
                        <li><strong>مكافحة البرمجيات الخبيثة:</strong> تلقائي</li>
                        <li><strong>النسخ الاحتياطي:</strong> يومي</li>
                    </ul>
                </div>

                <div class="bg-white rounded-lg shadow p-6">
                    <h3 class="text-xl font-semibold mb-4 text-orange-600">
                        <i class="fas fa-headset ml-2"></i>
                        الدعم والخدمات
                    </h3>
                    <ul class="space-y-2 text-gray-700">
                        <li><strong>الدعم الفني:</strong> 24/7</li>
                        <li><strong>وقت الاستجابة:</strong> أقل من ساعة</li>
                        <li><strong>طرق التواصل:</strong> تذاكر، دردشة، هاتف</li>
                        <li><strong>نقل الموقع:</strong> مجاني</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Related Plans -->
<section class="py-16">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-800 mb-4">خطط أخرى قد تهمك</h2>
            <p class="text-gray-600">اكتشف المزيد من خططنا المتنوعة</p>
        </div>

        <div class="flex flex-wrap justify-center gap-4">
            <a href="{{ route('hosting.dashboard') }}"
               class="bg-blue-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-blue-700 transition duration-300">
                عرض جميع خطط الاستضافة
            </a>
            <a href="{{ route('domains.index') }}" 
               class="border-2 border-blue-600 text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-blue-600 hover:text-white transition duration-300">
                تسجيل دومين
            </a>
            <a href="{{ route('email.index') }}" 
               class="border-2 border-green-600 text-green-600 px-6 py-3 rounded-lg font-semibold hover:bg-green-600 hover:text-white transition duration-300">
               البريد المهني
            </a>
        </div>
    </div>
</section>
@endsection
