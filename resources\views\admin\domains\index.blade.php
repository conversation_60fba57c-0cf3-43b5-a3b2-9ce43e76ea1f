@extends('layouts.admin')

@section('title', 'إدارة الدومينات')

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">إدارة الدومينات</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">الرئيسية</a></li>
                        <li class="breadcrumb-item active">الدومينات</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <!-- Stats Cards -->
            <div class="row">
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-info">
                        <div class="inner">
                            <h3>{{ \App\Models\Domain::count() }}</h3>
                            <p>إجمالي الدومينات</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-globe"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-success">
                        <div class="inner">
                            <h3>{{ \App\Models\Domain::where('status', 'active')->count() }}</h3>
                            <p>دومينات نشطة</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-check-circle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-warning">
                        <div class="inner">
                            <h3>0</h3>
                            <p>تنتهي قريباً</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-3 col-6">
                    <div class="small-box bg-danger">
                        <div class="inner">
                            <h3>{{ \App\Models\Domain::where('status', 'expired')->count() }}</h3>
                            <p>دومينات منتهية</p>
                        </div>
                        <div class="icon">
                            <i class="fas fa-times-circle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Domains Table -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">قائمة الدومينات</h3>
                            <div class="card-tools">
                                <button type="button" class="btn btn-primary btn-sm">
                                    <i class="fas fa-plus"></i> إضافة دومين جديد
                                </button>
                            </div>
                        </div>
                        <div class="card-body table-responsive p-0">
                            <table class="table table-hover text-nowrap">
                                <thead>
                                    <tr>
                                        <th>اسم الدومين</th>
                                        <th>النوع</th>
                                        <th>السعر</th>
                                        <th>الحالة</th>
                                        <th>تاريخ الإضافة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse(\App\Models\Domain::latest()->take(10)->get() as $domain)
                                    <tr>
                                        <td>{{ $domain->name }}</td>
                                        <td>{{ $domain->extension }}</td>
                                        <td>${{ number_format($domain->price, 2) }}</td>
                                        <td>
                                            @if($domain->status == 'active')
                                                <span class="badge badge-success">نشط</span>
                                            @elseif($domain->status == 'pending')
                                                <span class="badge badge-warning">قيد الانتظار</span>
                                            @else
                                                <span class="badge badge-danger">منتهي</span>
                                            @endif
                                        </td>
                                        <td>{{ $domain->created_at->format('Y-m-d') }}</td>
                                        <td>
                                            <a href="#" class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="#" class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @empty
                                    <tr>
                                        <td colspan="6" class="text-center text-muted py-4">
                                            <i class="fas fa-globe fa-3x mb-3"></i>
                                            <br>
                                            لا توجد دومينات مضافة حالياً
                                        </td>
                                    </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>
@endsection

@section('scripts')
<script>
$(document).ready(function() {
    console.log('Domains page loaded');
});
</script>
@endsection
