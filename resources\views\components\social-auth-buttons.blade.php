@props(['type' => 'login'])

<!-- Social Authentication Buttons -->
<div class="social-auth-buttons">
    <!-- Google Button -->
    <button data-auth="google" class="google-btn flex items-center justify-center px-4 py-3 rounded-lg transition-all w-full mb-3 bg-white border border-gray-300 hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
        <svg class="w-5 h-5 mr-3" viewBox="0 0 24 24">
            <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </svg>
        <span class="text-gray-700 font-medium">
            @if($type === 'register')
                إنشاء حساب عبر Google
            @else
                تسجيل الدخول عبر Google
            @endif
        </span>
    </button>
</div>

<!-- Social Auth JavaScript -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const googleButton = document.querySelector('[data-auth="google"]');
        if (googleButton) {
            googleButton.addEventListener('click', async function(e) {
                e.preventDefault();
                
                const originalContent = this.innerHTML;
                const loadingText = '{{ $type === "register" ? "جاري إنشاء الحساب..." : "جاري تسجيل الدخول..." }}';
                
                this.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>' + loadingText;
                this.disabled = true;
                
                try {
                    if (typeof window.signInWithGoogle === 'function') {
                        await window.signInWithGoogle();
                    } else {
                        throw new Error('Firebase غير مُحمل بشكل صحيح');
                    }
                } catch (error) {
                    console.error('خطأ في المصادقة:', error);
                    
                    let errorMessage = 'حدث خطأ أثناء {{ $type === "register" ? "إنشاء الحساب" : "تسجيل الدخول" }}';
                    
                    if (error.code === 'auth/popup-closed-by-user') {
                        errorMessage = 'تم إلغاء العملية';
                    } else if (error.code === 'auth/popup-blocked') {
                        errorMessage = 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة';
                    } else if (error.code === 'auth/network-request-failed') {
                        errorMessage = 'خطأ في الاتصال بالإنترنت';
                    }
                    
                    // Show error message
                    const errorDiv = document.createElement('div');
                    errorDiv.className = 'bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4';
                    errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle mr-2"></i>' + errorMessage;
                    
                    // Insert error message before the button
                    this.parentNode.insertBefore(errorDiv, this);
                    
                    // Remove error message after 5 seconds
                    setTimeout(() => {
                        if (errorDiv.parentNode) {
                            errorDiv.parentNode.removeChild(errorDiv);
                        }
                    }, 5000);
                    
                    // Restore button state
                    this.innerHTML = originalContent;
                    this.disabled = false;
                }
            });
        }
    });
</script>
