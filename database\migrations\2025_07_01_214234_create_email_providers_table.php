<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('email_providers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Zoho Mail, Google Workspace, Microsoft 365, Titan Mail
            $table->string('type'); // zoho, google, microsoft, titan
            $table->string('api_endpoint')->nullable();
            $table->string('api_key')->nullable();
            $table->string('api_secret')->nullable();
            $table->json('api_config')->nullable(); // Provider-specific API configuration
            $table->string('white_label_url')->nullable(); // White label signup URL
            $table->decimal('commission_rate', 5, 2)->default(0); // Commission percentage
            $table->boolean('is_active')->default(true);
            $table->json('supported_features')->nullable(); // Features supported by this provider
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('email_providers');
    }
};
