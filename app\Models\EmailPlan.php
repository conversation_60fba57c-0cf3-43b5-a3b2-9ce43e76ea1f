<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EmailPlan extends Model
{
    protected $fillable = [
        'name_ar',
        'name_en',
        'description_ar',
        'description_en',
        'provider',
        'monthly_price',
        'yearly_price',
        'max_users',
        'storage_per_user',
        'features',
        'commission_rate',
        'is_active',
        'sort_order',
        'provider_config',
    ];

    protected $casts = [
        'features' => 'array',
        'provider_config' => 'array',
        'monthly_price' => 'decimal:2',
        'yearly_price' => 'decimal:2',
        'commission_rate' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function emailProvider(): BelongsTo
    {
        return $this->belongsTo(EmailProvider::class);
    }

    public function getDiscountPercentageAttribute()
    {
        if ($this->monthly_price > 0) {
            $yearlyMonthly = $this->yearly_price / 12;
            return round((($this->monthly_price - $yearlyMonthly) / $this->monthly_price) * 100);
        }
        return 0;
    }
}
