<?php

namespace App\Http\Controllers;

use App\Models\Contact;
use App\Models\ContactReply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class ContactController extends Controller
{
    public function index()
    {
        return view('contact');
    }

    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'company_name' => 'nullable|string|max:255',
            'service_type' => 'nullable|string|max:100',
            'subject' => 'required|string|max:255',
            'message' => 'required|string|max:5000',
        ], [
            'name.required' => 'الاسم مطلوب',
            'email.required' => 'البريد الإلكتروني مطلوب',
            'email.email' => 'البريد الإلكتروني غير صحيح',
            'subject.required' => 'موضوع الرسالة مطلوب',
            'message.required' => 'نص الرسالة مطلوب',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        // تحويل نوع الخدمة إلى نص مفهوم
        $serviceTypeText = $this->getServiceTypeText($request->service_type);

        $contact = Contact::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'company' => $request->company_name,
            'subject' => $request->subject,
            'message' => $request->message . ($serviceTypeText ? "\n\nنوع الخدمة المطلوبة: " . $serviceTypeText : ''),
            'status' => 'new'
        ]);

        // إرسال إيميل تأكيد للعميل
        $this->sendConfirmationEmail($contact);

        // إرسال إشعار للإدارة
        $this->sendAdminNotification($contact);

        return back()->with('success', 'تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.');
    }

    public function reply(Request $request, Contact $contact)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:5000',
        ], [
            'message.required' => 'نص الرد مطلوب',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator);
        }

        $reply = ContactReply::create([
            'contact_id' => $contact->id,
            'sender_type' => 'client',
            'sender_name' => $contact->name,
            'sender_email' => $contact->email,
            'message' => $request->message,
            'sent_at' => now()
        ]);

        // تحديث حالة الرسالة
        $contact->update(['status' => 'in_progress']);

        // إرسال إشعار للإدارة
        $this->sendReplyNotification($contact, $reply);

        return back()->with('success', 'تم إرسال ردك بنجاح!');
    }

    private function sendConfirmationEmail(Contact $contact)
    {
        try {
            Mail::send('emails.contact-confirmation', ['contact' => $contact], function ($message) use ($contact) {
                $message->to($contact->email, $contact->name)
                        ->subject('تأكيد استلام رسالتك - ميتاء تك');
            });
        } catch (\Exception $e) {
            \Log::error('Failed to send confirmation email: ' . $e->getMessage());
        }
    }

    private function sendAdminNotification(Contact $contact)
    {
        try {
            Mail::send('emails.admin-notification', ['contact' => $contact], function ($message) use ($contact) {
                $message->to('<EMAIL>', 'ميتاء تك')
                        ->subject('رسالة جديدة من العميل: ' . $contact->name);
            });
        } catch (\Exception $e) {
            \Log::error('Failed to send admin notification: ' . $e->getMessage());
        }
    }

    private function sendReplyNotification(Contact $contact, ContactReply $reply)
    {
        try {
            Mail::send('emails.reply-notification', ['contact' => $contact, 'reply' => $reply], function ($message) use ($contact) {
                $message->to('<EMAIL>', 'ميتاء تك')
                        ->subject('رد جديد من العميل: ' . $contact->name);
            });
        } catch (\Exception $e) {
            \Log::error('Failed to send reply notification: ' . $e->getMessage());
        }
    }

    private function getServiceTypeText($serviceType)
    {
        $serviceTypes = [
            'website' => 'تطوير موقع إلكتروني',
            'mobile' => 'تطبيق موبايل (iOS/Android)',
            'ecommerce' => 'متجر إلكتروني',
            'hosting' => 'خدمات الاستضافة',
            'domain' => 'تسجيل دومين',
            'email' => 'خدمات البريد الإلكتروني',
            'seo' => 'تحسين محركات البحث (SEO)',
            'design' => 'تصميم جرافيك وهوية بصرية',
            'maintenance' => 'صيانة ودعم فني',
            'consultation' => 'استشارات تقنية',
            'custom' => 'حلول مخصصة أخرى'
        ];

        return $serviceTypes[$serviceType] ?? null;
    }
}
