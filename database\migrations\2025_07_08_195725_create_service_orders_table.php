<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_orders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('service_type'); // web_development, mobile_app, graphic_design, digital_marketing
            $table->string('service_name');
            $table->text('description');
            $table->decimal('budget', 10, 2);
            $table->date('deadline')->nullable();
            $table->text('requirements')->nullable();
            $table->enum('contact_method', ['email', 'phone', 'whatsapp']);
            $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending');
            $table->integer('progress')->default(0); // 0-100
            $table->integer('rating')->nullable(); // 1-5
            $table->text('review')->nullable();
            $table->timestamp('rated_at')->nullable();
            $table->json('files')->nullable(); // Delivered files
            $table->text('admin_notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_orders');
    }
};
