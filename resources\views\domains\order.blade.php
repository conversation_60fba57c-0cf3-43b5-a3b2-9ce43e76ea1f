@extends('layouts.app')

@section('title', 'طلب دومين - ' . $fullDomain)

@section('content')
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-blue-900 via-purple-900 to-gray-800 py-20">
    <div class="absolute inset-0 bg-black/30"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center" data-aos="fade-up">
            <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                طلب دومين - <span class="text-blue-400">{{ $fullDomain }}</span>
            </h1>
            <p class="text-xl text-gray-300 max-w-2xl mx-auto">
                أكمل البيانات أدناه لحجز دومينك الجديد
            </p>
        </div>
    </div>
</section>

<!-- Order Form -->
<section class="py-20 bg-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Order Form -->
            <div class="lg:col-span-2">
                <div class="bg-gray-800 rounded-2xl p-8 border border-gray-700">
                    <h2 class="text-2xl font-bold text-white mb-6">بيانات الطلب</h2>
                    
                    <form action="{{ route('domains.order.process') }}" method="POST" 
                          x-data="{ 
                              serviceType: 'registration',
                              years: 1,
                              paymentMethod: 'credit_card'
                          }">
                        @csrf
                        <input type="hidden" name="domain_name" value="{{ $domainName }}">
                        <input type="hidden" name="domain_extension" value="{{ $domainInfo->extension }}">
                        
                        <!-- Domain Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-white mb-4">معلومات الدومين</h3>
                            <div class="bg-gray-700 rounded-lg p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h4 class="text-xl font-bold text-white">{{ $fullDomain }}</h4>
                                        <p class="text-gray-300">{{ $domainInfo->description }}</p>
                                    </div>
                                    <div class="text-right">
                                        <span class="inline-block px-3 py-1 rounded-full text-sm font-semibold {{ $domainInfo->category_color }} text-white">
                                            {{ $domainInfo->category_name }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Service Type -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-white mb-4">نوع الخدمة</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <label class="relative">
                                    <input type="radio" name="service_type" value="registration" x-model="serviceType" class="sr-only">
                                    <div :class="serviceType === 'registration' ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'"
                                         class="border-2 rounded-lg p-4 cursor-pointer transition-all">
                                        <div class="flex items-center mb-2">
                                            <svg class="w-6 h-6 text-blue-400 ml-3 rtl:mr-3 rtl:ml-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                            </svg>
                                            <h4 class="text-white font-semibold">تسجيل جديد</h4>
                                        </div>
                                        <p class="text-gray-400 text-sm">تسجيل دومين جديد لأول مرة</p>
                                    </div>
                                </label>
                                
                                <label class="relative">
                                    <input type="radio" name="service_type" value="transfer" x-model="serviceType" class="sr-only">
                                    <div :class="serviceType === 'transfer' ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'"
                                         class="border-2 rounded-lg p-4 cursor-pointer transition-all">
                                        <div class="flex items-center mb-2">
                                            <svg class="w-6 h-6 text-blue-400 ml-3 rtl:mr-3 rtl:ml-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"/>
                                            </svg>
                                            <h4 class="text-white font-semibold">نقل دومين</h4>
                                        </div>
                                        <p class="text-gray-400 text-sm">نقل دومين موجود من مزود آخر</p>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Registration Period -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-white mb-4">مدة التسجيل</h3>
                            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                                @for($i = $domainInfo->min_years; $i <= $domainInfo->max_years; $i++)
                                    <label class="relative">
                                        <input type="radio" name="years" value="{{ $i }}" x-model="years" class="sr-only" {{ $i == 1 ? 'checked' : '' }}>
                                        <div :class="years == {{ $i }} ? 'border-blue-500 bg-blue-500/10' : 'border-gray-600'"
                                             class="border-2 rounded-lg p-4 cursor-pointer transition-all text-center">
                                            <div class="text-white font-semibold">{{ $i }} {{ $i == 1 ? 'سنة' : 'سنوات' }}</div>
                                            <div class="text-blue-400 text-sm">
                                                {{ number_format($domainInfo->registration_price * $i) }} ريال
                                            </div>
                                        </div>
                                    </label>
                                @endfor
                            </div>
                        </div>

                        <!-- Customer Information -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-white mb-4">بيانات العميل</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-gray-300 text-sm font-medium mb-2">الاسم الكامل</label>
                                    <input type="text" name="customer_name" required
                                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                                           placeholder="أحمد محمد">
                                </div>
                                <div>
                                    <label class="block text-gray-300 text-sm font-medium mb-2">البريد الإلكتروني</label>
                                    <input type="email" name="customer_email" required
                                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                                           placeholder="<EMAIL>">
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-gray-300 text-sm font-medium mb-2">رقم الهاتف</label>
                                    <input type="tel" name="customer_phone" required
                                           class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                                           placeholder="+966 50 123 4567">
                                </div>
                            </div>
                        </div>

                        <!-- Transfer Code (if transfer) -->
                        <div x-show="serviceType === 'transfer'" class="mb-8">
                            <h3 class="text-lg font-semibold text-white mb-4">كود النقل</h3>
                            <div>
                                <label class="block text-gray-300 text-sm font-medium mb-2">كود التفويض (Auth Code)</label>
                                <input type="text" name="auth_code"
                                       class="w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-blue-500"
                                       placeholder="أدخل كود التفويض من المزود الحالي">
                                <p class="text-gray-400 text-sm mt-1">يمكنك الحصول على كود التفويض من لوحة تحكم المزود الحالي</p>
                            </div>
                        </div>

                        <!-- Payment Method -->
                        <div class="mb-8">
                            <h3 class="text-lg font-semibold text-white mb-4">طريقة الدفع</h3>
                            <div class="space-y-3">
                                <label class="flex items-center p-4 border border-gray-600 rounded-lg cursor-pointer hover:border-blue-500 transition-colors">
                                    <input type="radio" name="payment_method" value="credit_card" x-model="paymentMethod" class="text-blue-500">
                                    <div class="mr-3 rtl:ml-3 rtl:mr-0">
                                        <div class="text-white font-medium">بطاقة ائتمانية</div>
                                        <div class="text-gray-400 text-sm">Visa, Mastercard, Mada</div>
                                    </div>
                                </label>
                                
                                <label class="flex items-center p-4 border border-gray-600 rounded-lg cursor-pointer hover:border-blue-500 transition-colors">
                                    <input type="radio" name="payment_method" value="bank_transfer" x-model="paymentMethod" class="text-blue-500">
                                    <div class="mr-3 rtl:ml-3 rtl:mr-0">
                                        <div class="text-white font-medium">تحويل بنكي</div>
                                        <div class="text-gray-400 text-sm">تحويل مباشر إلى حساب الشركة</div>
                                    </div>
                                </label>
                                
                                <label class="flex items-center p-4 border border-gray-600 rounded-lg cursor-pointer hover:border-blue-500 transition-colors">
                                    <input type="radio" name="payment_method" value="paypal" x-model="paymentMethod" class="text-blue-500">
                                    <div class="mr-3 rtl:ml-3 rtl:mr-0">
                                        <div class="text-white font-medium">PayPal</div>
                                        <div class="text-gray-400 text-sm">دفع آمن عبر PayPal</div>
                                    </div>
                                </label>
                            </div>
                        </div>

                        <!-- Terms and Conditions -->
                        <div class="mb-8">
                            <label class="flex items-start">
                                <input type="checkbox" required class="mt-1 text-blue-500">
                                <span class="mr-3 rtl:ml-3 rtl:mr-0 text-gray-300 text-sm">
                                    أوافق على <a href="#" class="text-blue-400 hover:underline">شروط الخدمة</a> و 
                                    <a href="#" class="text-blue-400 hover:underline">سياسة الخصوصية</a>
                                </span>
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" 
                                class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105">
                            إتمام الطلب والدفع
                        </button>
                    </form>
                </div>
            </div>

            <!-- Order Summary -->
            <div class="lg:col-span-1">
                <div class="bg-gray-800 rounded-2xl p-6 border border-gray-700 sticky top-8">
                    <h3 class="text-xl font-bold text-white mb-6">ملخص الطلب</h3>
                    
                    <!-- Domain Details -->
                    <div class="mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-300">الدومين</span>
                            <span class="text-white font-semibold">{{ $fullDomain }}</span>
                        </div>
                        <div class="text-gray-400 text-sm">
                            {{ $domainInfo->description }}
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="mb-6">
                        <h4 class="text-white font-semibold mb-3">المميزات المشمولة</h4>
                        <ul class="space-y-2 text-sm">
                            @if($domainInfo->features)
                                @foreach($domainInfo->features as $feature)
                                    <li class="flex items-center text-gray-300">
                                        <svg class="w-4 h-4 text-blue-400 ml-2 rtl:mr-2 rtl:ml-0" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"/>
                                        </svg>
                                        {{ $feature }}
                                    </li>
                                @endforeach
                            @endif
                        </ul>
                    </div>

                    <!-- Pricing Calculator -->
                    <div class="border-t border-gray-700 pt-4" x-data="{ 
                        get basePrice() { 
                            return {{ $domainInfo->registration_price }};
                        },
                        get setupFee() {
                            return {{ $domainInfo->setup_fee }};
                        },
                        get subtotal() {
                            return (this.basePrice * this.years) + this.setupFee;
                        },
                        get taxAmount() {
                            return this.subtotal * 0.15;
                        },
                        get totalAmount() {
                            return this.subtotal + this.taxAmount;
                        }
                    }">
                        <div class="space-y-2 mb-4">
                            <div class="flex justify-between">
                                <span class="text-gray-300">السعر الأساسي</span>
                                <span class="text-white" x-text="(basePrice * years).toLocaleString() + ' ريال'"></span>
                            </div>
                            <div x-show="setupFee > 0" class="flex justify-between">
                                <span class="text-gray-300">رسوم الإعداد</span>
                                <span class="text-white" x-text="setupFee.toLocaleString() + ' ريال'"></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">المجموع الفرعي</span>
                                <span class="text-white" x-text="subtotal.toLocaleString() + ' ريال'"></span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-300">ضريبة القيمة المضافة (15%)</span>
                                <span class="text-white" x-text="taxAmount.toLocaleString() + ' ريال'"></span>
                            </div>
                        </div>
                        <div class="border-t border-gray-700 pt-2">
                            <div class="flex justify-between items-center">
                                <span class="text-lg font-semibold text-white">المجموع</span>
                                <span class="text-xl font-bold text-blue-400" x-text="totalAmount.toLocaleString() + ' ريال'"></span>
                            </div>
                        </div>
                    </div>

                    <!-- Support Info -->
                    <div class="mt-6 p-4 bg-blue-500/10 border border-blue-500/30 rounded-lg">
                        <div class="flex items-center mb-2">
                            <svg class="w-5 h-5 text-blue-400 ml-2 rtl:mr-2 rtl:ml-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M12 12h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                            <span class="text-blue-400 font-semibold">دعم فني 24/7</span>
                        </div>
                        <p class="text-gray-300 text-sm">
                            فريق الدعم الفني متاح على مدار الساعة لمساعدتك
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
