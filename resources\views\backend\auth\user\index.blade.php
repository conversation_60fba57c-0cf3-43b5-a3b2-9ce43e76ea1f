@extends('backend.layouts.app')

@section('title', 'إدارة المستخدمين')

@section('breadcrumb-links')
    @include('backend.auth.user.includes.breadcrumb-links')
@endsection

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-users me-2 text-primary"></i>
                        إدارة المستخدمين
                    </h1>
                    <p class="text-muted mb-0">إدارة وتنظيم حسابات المستخدمين في النظام</p>
                </div>
                <div class="d-flex gap-2">
                    @if ($logged_in_user->hasAllAccess())
                    <a href="{{ route('admin.auth.user.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>
                        إضافة مستخدم جديد
                    </a>
                    @endif
                    <button class="btn btn-outline-secondary" onclick="exportUsers()">
                        <i class="fas fa-download me-2"></i>
                        تصدير
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">إجمالي المستخدمين</h6>
                            <h3 class="mb-0">{{ \App\Domains\Auth\Models\User::count() }}</h3>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-users fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">المستخدمين النشطين</h6>
                            <h3 class="mb-0">{{ \App\Domains\Auth\Models\User::where('active', true)->count() }}</h3>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-user-check fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">غير مفعل</h6>
                            <h3 class="mb-0">{{ \App\Domains\Auth\Models\User::where('active', false)->count() }}</h3>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-user-times fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">جدد هذا الشهر</h6>
                            <h3 class="mb-0">{{ \App\Domains\Auth\Models\User::whereMonth('created_at', now()->month)->count() }}</h3>
                        </div>
                        <div class="stats-icon">
                            <i class="fas fa-user-plus fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advanced Data Table -->
    <div class="row">
        <div class="col-12">
            <x-backend.data-table
                title="قائمة المستخدمين"
                :columns="[
                    ['key' => 'id', 'label' => '#'],
                    ['key' => 'name', 'label' => 'الاسم'],
                    ['key' => 'email', 'label' => 'البريد الإلكتروني'],
                    ['key' => 'roles', 'label' => 'الأدوار', 'type' => 'badge'],
                    ['key' => 'active', 'label' => 'الحالة', 'type' => 'badge'],
                    ['key' => 'email_verified_at', 'label' => 'تم التحقق', 'type' => 'badge'],
                    ['key' => 'created_at', 'label' => 'تاريخ التسجيل', 'type' => 'date']
                ]"
                :data="$users ?? []"
                :searchable="true"
                :sortable="true"
                :filterable="true"
                :exportable="true"
                :pagination="true"
                :actions="true"
                :bulk-actions="true"
                create-route="{{ route('admin.auth.user.create') }}"
                edit-route="{{ route('admin.auth.user.edit', '') }}"
                delete-route="{{ route('admin.auth.user.destroy', '') }}"
                view-route="{{ route('admin.auth.user.show', '') }}" />
        </div>
    </div>
</div>

<!-- Bulk Actions Modal -->
<x-backend.advanced-modal
    id="bulkActionsModal"
    title="إجراءات جماعية"
    size="modal-md"
    :centered="true">

    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle me-2"></i>
        سيتم تطبيق الإجراء المحدد على جميع المستخدمين المحددين.
    </div>

    <div class="mb-3">
        <label class="form-label">اختر الإجراء</label>
        <select class="form-select" id="bulkAction">
            <option value="">-- اختر إجراء --</option>
            <option value="activate">تفعيل المستخدمين</option>
            <option value="deactivate">إلغاء تفعيل المستخدمين</option>
            <option value="delete">حذف المستخدمين</option>
            <option value="export">تصدير البيانات</option>
        </select>
    </div>

    <div id="selectedUsersCount" class="text-muted mb-3">
        لم يتم تحديد أي مستخدمين
    </div>

    <x-slot name="footerLeft">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
    </x-slot>

    <button type="button" class="btn btn-primary" onclick="executeBulkAction()">
        تنفيذ الإجراء
    </button>
</x-backend.advanced-modal>
@endsection

@push('after-styles')
<style>
.stats-icon {
    opacity: 0.7;
}

.card {
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.bg-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.bg-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%) !important;
}

.bg-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%) !important;
}

.bg-info {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%) !important;
}

.page-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.table th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    color: #6c757d;
}

.table td {
    border: none;
    vertical-align: middle;
    padding: 1rem 0.75rem;
}

.table tbody tr {
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
}

.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
    border-radius: 6px;
}

.user-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-buttons .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    margin: 0 0.125rem;
}

@media (max-width: 768px) {
    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    .btn {
        width: 100%;
    }

    .stats-cards .col-md-3 {
        margin-bottom: 1rem;
    }
}
</style>
@endpush

@push('after-scripts')
<script>
function exportUsers() {
    const btn = event.target;
    const originalText = btn.innerHTML;

    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التصدير...';
    btn.disabled = true;

    // Create export URL
    const exportUrl = '{{ route("admin.auth.user.export") }}';

    // Create temporary link and trigger download
    const link = document.createElement('a');
    link.href = exportUrl;
    link.download = 'users_' + new Date().toISOString().split('T')[0] + '.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    // Reset button
    setTimeout(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.success('تم تصدير بيانات المستخدمين بنجاح');
        }
    }, 2000);
}

function executeBulkAction() {
    const action = document.getElementById('bulkAction').value;
    const selectedUsers = getSelectedUsers();

    if (!action) {
        alert('يرجى اختيار إجراء');
        return;
    }

    if (selectedUsers.length === 0) {
        alert('يرجى تحديد مستخدمين على الأقل');
        return;
    }

    const confirmMessage = getBulkActionConfirmMessage(action, selectedUsers.length);

    if (!confirm(confirmMessage)) {
        return;
    }

    // Show loading
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري التنفيذ...';
    btn.disabled = true;

    // Execute bulk action
    fetch('{{ route("admin.auth.user.bulk-action") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
        },
        body: JSON.stringify({
            action: action,
            users: selectedUsers
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success(data.message || 'تم تنفيذ الإجراء بنجاح');
            } else {
                alert(data.message || 'تم تنفيذ الإجراء بنجاح');
            }

            // Close modal and refresh page
            const modal = bootstrap.Modal.getInstance(document.getElementById('bulkActionsModal'));
            modal.hide();

            setTimeout(() => {
                location.reload();
            }, 1000);
        } else {
            throw new Error(data.message || 'حدث خطأ أثناء تنفيذ الإجراء');
        }
    })
    .catch(error => {
        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.error(error.message);
        } else {
            alert(error.message);
        }
    })
    .finally(() => {
        btn.innerHTML = originalText;
        btn.disabled = false;
    });
}

function getSelectedUsers() {
    const checkboxes = document.querySelectorAll('input[type="checkbox"][name="selected_users[]"]:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

function getBulkActionConfirmMessage(action, count) {
    const messages = {
        'activate': `هل أنت متأكد من تفعيل ${count} مستخدم؟`,
        'deactivate': `هل أنت متأكد من إلغاء تفعيل ${count} مستخدم؟`,
        'delete': `هل أنت متأكد من حذف ${count} مستخدم؟ هذا الإجراء لا يمكن التراجع عنه.`,
        'export': `سيتم تصدير بيانات ${count} مستخدم. هل تريد المتابعة؟`
    };

    return messages[action] || `هل أنت متأكد من تنفيذ هذا الإجراء على ${count} مستخدم؟`;
}

// Update selected users count
function updateSelectedUsersCount() {
    const selectedCount = getSelectedUsers().length;
    const countElement = document.getElementById('selectedUsersCount');

    if (selectedCount === 0) {
        countElement.textContent = 'لم يتم تحديد أي مستخدمين';
        countElement.className = 'text-muted mb-3';
    } else {
        countElement.textContent = `تم تحديد ${selectedCount} مستخدم`;
        countElement.className = 'text-primary mb-3 fw-bold';
    }
}

// Initialize page
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for checkboxes
    document.addEventListener('change', function(e) {
        if (e.target.type === 'checkbox' && e.target.name === 'selected_users[]') {
            updateSelectedUsersCount();
        }
    });

    // Animate cards on load
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';

        setTimeout(() => {
            card.style.transition = 'all 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
@endpush
