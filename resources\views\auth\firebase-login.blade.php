<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>تسجيل الدخول عبر Firebase - ميتاء تك</title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800;900&family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">

    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">

    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>

    <style>
        * {
            font-family: 'Cairo', 'Tajawal', sans-serif;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 3rem;
            width: 100%;
            max-width: 400px;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 2rem;
            background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 2rem;
        }

        .firebase-btn {
            background: linear-gradient(135deg, #8b5cf6 0%, #3b82f6 100%);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 1rem 2rem;
            width: 100%;
            font-weight: 600;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
        }

        .firebase-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(139, 92, 246, 0.3);
        }

        .firebase-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        .error-message {
            background: #fee2e2;
            border: 1px solid #fecaca;
            color: #dc2626;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            display: none;
        }

        .success-message {
            background: #dcfce7;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 1rem;
            border-radius: 8px;
            margin-top: 1rem;
            display: none;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(255,255,255,.3);
            border-radius: 50%;
            border-top-color: #fff;
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <!-- Logo -->
        <div class="logo">
            <i class="fas fa-code"></i>
        </div>

        <!-- Title -->
        <div class="text-center mb-8">
            <h1 class="text-2xl font-bold text-gray-900 mb-2">ميتاء تك</h1>
            <p class="text-gray-600">تسجيل الدخول عبر Firebase</p>
        </div>

        <!-- Firebase Login Button -->
        <button id="firebase-login-btn" class="firebase-btn">
            <i class="fab fa-google"></i>
            تسجيل الدخول عبر Google
        </button>

        <!-- Messages -->
        <div id="error-message" class="error-message"></div>
        <div id="success-message" class="success-message"></div>

        <!-- Back to Normal Login -->
        <div class="text-center mt-6">
            <a href="{{ route('login') }}" class="text-purple-600 hover:text-purple-800 text-sm">
                العودة لتسجيل الدخول العادي
            </a>
        </div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        // Import Firebase functions
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getAuth, GoogleAuthProvider, signInWithPopup } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "{{ env('VITE_FIREBASE_API_KEY') }}",
            authDomain: "{{ env('VITE_FIREBASE_AUTH_DOMAIN') }}",
            projectId: "{{ env('VITE_FIREBASE_PROJECT_ID') }}",
            storageBucket: "{{ env('VITE_FIREBASE_STORAGE_BUCKET') }}",
            messagingSenderId: "{{ env('VITE_FIREBASE_MESSAGING_SENDER_ID') }}",
            appId: "{{ env('VITE_FIREBASE_APP_ID') }}",
            measurementId: "{{ env('VITE_FIREBASE_MEASUREMENT_ID') }}"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const provider = new GoogleAuthProvider();

        // Get elements
        const loginBtn = document.getElementById('firebase-login-btn');
        const errorMessage = document.getElementById('error-message');
        const successMessage = document.getElementById('success-message');

        // Login function
        async function signInWithGoogle() {
            try {
                // Show loading
                loginBtn.innerHTML = '<div class="loading"></div> جاري تسجيل الدخول...';
                loginBtn.disabled = true;
                hideMessages();

                // Sign in with popup
                const result = await signInWithPopup(auth, provider);
                const user = result.user;

                // Show success
                showSuccess('تم تسجيل الدخول بنجاح! جاري التوجيه...');

                // Send to Laravel backend
                const response = await fetch('/auth/firebase/callback', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    },
                    body: JSON.stringify({
                        uid: user.uid,
                        name: user.displayName,
                        email: user.email,
                        avatar: user.photoURL,
                        provider: 'google'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data.redirect) {
                        window.location.href = data.redirect;
                    } else {
                        window.location.href = '/user/dashboard';
                    }
                } else {
                    throw new Error('فشل في التواصل مع الخادم');
                }

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                
                let errorText = 'حدث خطأ في تسجيل الدخول';
                if (error.code === 'auth/popup-closed-by-user') {
                    errorText = 'تم إلغاء عملية تسجيل الدخول';
                } else if (error.code === 'auth/popup-blocked') {
                    errorText = 'تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة';
                } else if (error.code === 'auth/unauthorized-domain') {
                    errorText = 'النطاق غير مصرح به. يرجى التحقق من إعدادات Firebase';
                }
                
                showError(errorText);
                
                // Reset button
                loginBtn.innerHTML = '<i class="fab fa-google"></i> تسجيل الدخول عبر Google';
                loginBtn.disabled = false;
            }
        }

        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.style.display = 'block';
            successMessage.style.display = 'none';
        }

        function showSuccess(message) {
            successMessage.textContent = message;
            successMessage.style.display = 'block';
            errorMessage.style.display = 'none';
        }

        function hideMessages() {
            errorMessage.style.display = 'none';
            successMessage.style.display = 'none';
        }

        // Add event listener
        loginBtn.addEventListener('click', signInWithGoogle);
    </script>
</body>
</html>
