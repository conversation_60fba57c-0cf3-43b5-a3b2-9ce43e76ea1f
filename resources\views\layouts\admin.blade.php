<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'لوحة التحكم - ميتاء تك')</title>

    <!-- Cairo Font for Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- Tailwind CSS -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    <!-- Meta Dashboard CSS -->
    <link rel="stylesheet" href="{{ asset('css/meta-dashboard.css') }}">
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    @yield('styles')
    
    <style>
        body {
            font-family: 'Cairo', 'Source Sans Pro', sans-serif;
        }

        /* حل بسيط وفعال للقائمة الجانبية */
        .main-sidebar {
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            left: auto !important;
            width: 250px !important;
            height: 100vh !important;
            background: #374151 !important;
            z-index: 1000 !important;
            overflow-y: auto !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            transform: none !important;
            box-shadow: -2px 0 15px rgba(0, 0, 0, 0.2) !important;
        }

        /* إجبار إظهار القائمة دائماً */
        .main-sidebar,
        body.sidebar-collapse .main-sidebar,
        body.sidebar-mini .main-sidebar {
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            transform: none !important;
            margin-right: 0 !important;
        }

        /* تحسين شريط التمرير للقائمة الجانبية */
        .main-sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .main-sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .main-sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .main-sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }
        
        .nav-sidebar .nav-link {
            color: #ecf0f1;
            border-radius: 8px;
            margin: 2px 8px;
            transition: all 0.3s ease;
        }
        
        .nav-sidebar .nav-link:hover {
            background: rgba(255, 255, 255, 0.1);
            color: #fff;
        }
        
        .nav-sidebar .nav-link.active {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .brand-link {
            background: rgba(0,0,0,0.1);
            border-bottom: 1px solid rgba(255,255,255,0.1);
            position: sticky !important;
            top: 0 !important;
            z-index: 1001 !important;
        }

        /* المحتوى الرئيسي */
        .content-wrapper {
            margin-right: 250px !important;
            margin-left: 0 !important;
            min-height: 100vh !important;
            width: calc(100% - 250px) !important;
        }

        /* الترويسة العلوية */
        .main-header {
            margin-right: 250px !important;
            margin-left: 0 !important;
            width: calc(100% - 250px) !important;
        }

        /* التصميم المتجاوب */
        @media (max-width: 768px) {
            .main-sidebar {
                transform: translateX(100%) !important;
                transition: transform 0.3s ease !important;
            }

            .main-sidebar.sidebar-open {
                transform: translateX(0) !important;
            }

            .content-wrapper,
            .main-header {
                margin-right: 0 !important;
            }
        }

        .sidebar-collapse .nav-sidebar .nav-link p {
            display: none;
        }

        .sidebar-collapse .brand-text {
            display: none;
        }

        /* إزالة دعم الشاشات الصغيرة - لوحة التحكم للشاشات الكبيرة فقط */
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.08);
        }
        
        .card-header {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border-radius: 12px 12px 0 0 !important;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            border: none;
            border-radius: 8px;
        }
        
        .btn-primary:hover {
            background: linear-gradient(45deg, #5a6fd8, #6a4190);
            transform: translateY(-1px);
        }
        
        .small-box {
            border-radius: 12px;
            overflow: hidden;
            transition: transform 0.3s ease;
        }
        
        .small-box:hover {
            transform: translateY(-2px);
        }
        
        .navbar-nav .nav-link {
            color: #495057;
        }
        
        .dropdown-menu {
            border: none;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-radius: 8px;
        }
        
        .table {
            border-radius: 8px;
            overflow: hidden;
        }
        
        .badge {
            border-radius: 6px;
        }
        
        .alert {
            border: none;
            border-radius: 8px;
        }
        
        /* Full Screen Layout - Stable and Smooth */
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
            overflow-x: hidden;
            font-family: 'Cairo', 'Source Sans Pro', sans-serif;
        }

        .wrapper {
            min-height: 100vh;
            width: 100vw;
            overflow-x: hidden;
            position: relative;
        }



        .main-header {
            width: calc(100vw - 250px) !important;
            margin-right: 250px !important;
            margin-left: 0 !important;
            position: fixed !important;
            top: 0 !important;
            z-index: 999 !important;
            background-color: #fff !important;
            border-bottom: 1px solid #dee2e6 !important;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
            transition: all 0.3s ease !important;
        }

        .content-header {
            padding-top: 70px !important;
            margin-bottom: 0 !important;
            background-color: transparent !important;
        }

        /* الحل النهائي المبسط - تثبيت القائمة الجانبية */
        .main-sidebar,
        .sidebar {
            position: fixed !important;
            top: 0 !important;
            right: 0 !important;
            left: auto !important;
            height: 100vh !important;
            z-index: 1040 !important;
            overflow-y: auto !important;
            background-color: #374151 !important;
            width: 250px !important;
            display: block !important;
            visibility: visible !important;
            opacity: 1 !important;
            transform: translateX(0) !important;
            margin-right: 0 !important;
        }

        .content-wrapper {
            margin-right: 250px !important; /* يجب أن يطابق عرض .sidebar */
            margin-left: 0 !important;
        }

        .main-header {
            margin-right: 250px !important;
            margin-left: 0 !important;
            width: calc(100% - 250px) !important;
        }

        body.sidebar-collapse .main-sidebar,
        body.sidebar-collapse .sidebar {
            transform: none !important;
            margin-right: 0 !important;
            position: fixed !important;
            right: 0 !important;
        }

        .navbar {
            padding-right: 1rem;
            padding-left: 1rem;
        }

        .main-footer {
            margin-right: 250px !important;
            margin-left: 0 !important;
            width: calc(100vw - 250px) !important;
        }

        /* Container adjustments */
        .container-fluid {
            width: 100% !important;
            max-width: 100% !important;
            padding-right: 15px !important;
            padding-left: 15px !important;
        }

        /* Cards full width */
        .card {
            width: 100% !important;
            margin-bottom: 20px;
        }

        /* Statistics cards responsive */
        .small-box {
            height: 120px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .small-box .inner {
            padding: 15px;
        }

        .small-box .icon {
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 50px;
            opacity: 0.3;
        }

        /* لوحة التحكم مخصصة للشاشات الكبيرة فقط */
        @media (max-width: 1024px) {
            body::before {
                content: "لوحة التحكم مخصصة للشاشات الكبيرة فقط. يرجى استخدام جهاز كمبيوتر أو شاشة أكبر.";
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: #2c3e50;
                color: white;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                font-size: 18px;
                font-weight: bold;
                z-index: 9999;
                padding: 20px;
                box-sizing: border-box;
            }

            .wrapper {
                display: none !important;
            }
        }

        /* Table responsive */
        .table-responsive {
            width: 100%;
            overflow-x: auto;
        }

        /* Charts container */
        .chart-container {
            width: 100%;
            height: 300px;
            position: relative;
        }



        .brand-link {
            border-bottom: 1px solid rgba(255,255,255,0.1) !important;
            padding: 0.8rem 1rem !important;
        }

        .brand-text {
            font-size: 1.1rem !important;
            font-weight: 600 !important;
        }

        .user-panel {
            border-bottom: 1px solid rgba(255,255,255,0.1) !important;
        }

        .nav-sidebar .nav-item .nav-link {
            padding: 0.7rem 1rem !important;
            margin: 0.1rem 0.5rem !important;
            border-radius: 0.375rem !important;
            transition: all 0.3s ease !important;
        }

        .nav-sidebar .nav-item .nav-link:hover {
            background-color: rgba(255,255,255,0.1) !important;
            transform: translateX(-3px) !important;
        }

        .nav-sidebar .nav-item .nav-link.active {
            background: linear-gradient(45deg, #007bff, #0056b3) !important;
            box-shadow: 0 2px 4px rgba(0,123,255,0.3) !important;
        }

        .nav-sidebar .nav-item .nav-link .nav-icon {
            font-size: 1rem !important;
            width: 1.5rem !important;
            text-align: center !important;
        }

        .nav-header {
            color: rgba(255,255,255,0.6) !important;
            font-size: 0.75rem !important;
            text-transform: uppercase !important;
            letter-spacing: 0.5px !important;
            margin-top: 1rem !important;
            padding: 0.5rem 1rem !important;
        }

        .badge {
            font-size: 0.65rem !important;
            padding: 0.25rem 0.5rem !important;
            border-radius: 10px !important;
        }

        .nav-treeview .nav-item .nav-link {
            padding-right: 2.5rem !important;
            font-size: 0.9rem !important;
        }

        .nav-treeview .nav-item .nav-link .nav-icon {
            font-size: 0.8rem !important;
        }

        /* Logout button styling */
        .nav-sidebar .nav-item:last-child .nav-link {
            margin-top: 1rem !important;
            border-top: 1px solid rgba(255,255,255,0.1) !important;
            padding-top: 1rem !important;
            color: #dc3545 !important;
        }

        .nav-sidebar .nav-item:last-child .nav-link:hover {
            background-color: rgba(220, 53, 69, 0.1) !important;
            color: #dc3545 !important;
        }

        .nav-sidebar .nav-item:last-child .nav-link .nav-icon {
            color: #dc3545 !important;
        }

        /* Prevent Layout Shifts and Jitter */
        * {
            box-sizing: border-box;
        }

        .wrapper, .main-sidebar, .content-wrapper, .main-header {
            backface-visibility: hidden;
            -webkit-backface-visibility: hidden;
            transform: translateZ(0);
            -webkit-transform: translateZ(0);
        }



        /* Smooth Page Transitions */
        .content {
            opacity: 1;
            transition: opacity 0.2s ease-in-out;
        }

        .content.loading {
            opacity: 0.7;
        }

        /* Stable Navigation */
        .nav-sidebar .nav-item .nav-link {
            transform: none !important;
            transition: background-color 0.2s ease, color 0.2s ease !important;
        }

        .nav-sidebar .nav-item .nav-link:hover {
            transform: none !important;
            background-color: rgba(255,255,255,0.1) !important;
        }

        /* Fixed Breadcrumb */
        .breadcrumb {
            background-color: transparent !important;
            margin-bottom: 0 !important;
            padding: 0 !important;
        }

        /* Stable Cards */
        .card {
            transition: box-shadow 0.2s ease !important;
            transform: none !important;
        }

        .card:hover {
            transform: none !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        }

        /* Remove Dashboard Card Hover Effects */
        .dashboard-card {
            transition: box-shadow 0.2s ease !important;
            transform: none !important;
        }

        .dashboard-card:hover {
            transform: none !important;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1) !important;
        }

        /* Stable Small Boxes */
        .small-box {
            transition: box-shadow 0.2s ease !important;
            transform: none !important;
        }

        .small-box:hover {
            transform: none !important;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1) !important;
        }

        /* Prevent Text Selection Issues */
        .nav-sidebar, .main-header, .breadcrumb {
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* Stable Footer */
        .main-footer {
            position: relative !important;
            margin-top: auto !important;
        }
    </style>

    <!-- Vite Assets -->
    @vite(['resources/css/admin.css', 'resources/js/admin.js'])

    <!-- تثبيت القائمة الجانبية - الحل المبسط -->
    <!-- لا حاجة لملفات CSS أو JS إضافية - الحل مدمج في الصفحة --></script>

    @stack('styles')
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <div class="wrapper">
        
        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button">
                        <i class="fas fa-bars"></i>
                    </a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="{{ route('admin.dashboard') }}" class="nav-link">الرئيسية</a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a href="{{ route('home') }}" class="nav-link" target="_blank">عرض الموقع</a>
                </li>
            </ul>

            <!-- Right navbar links -->
            <ul class="navbar-nav mr-auto-navbav">
                <!-- Notifications Dropdown Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <i class="far fa-bell"></i>
                        <span class="badge badge-warning navbar-badge">3</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-left">
                        <span class="dropdown-item dropdown-header">3 إشعارات</span>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item">
                            <i class="fas fa-envelope ml-2"></i> رسالة جديدة
                            <span class="float-left text-muted text-sm">منذ 3 دقائق</span>
                        </a>
                        <div class="dropdown-divider"></div>
                        <a href="#" class="dropdown-item dropdown-footer">عرض جميع الإشعارات</a>
                    </div>
                </li>
                
                <!-- User Dropdown Menu -->
                <li class="nav-item dropdown">
                    <a class="nav-link" data-toggle="dropdown" href="#">
                        <img src="{{ asset('images/zdeuj.png') }}" alt="User Avatar" class="img-size-32 img-circle ml-2">
                        <span class="d-none d-md-inline">{{ Auth::guard('admin')->user()->name ?? 'المدير' }}</span>
                    </a>
                    <div class="dropdown-menu dropdown-menu-lg dropdown-menu-left">
                        <a href="{{ route('admin.profile.edit') }}" class="dropdown-item">
                            <i class="fas fa-user ml-2"></i> الملف الشخصي
                        </a>
                        <a href="{{ route('admin.settings') }}" class="dropdown-item">
                            <i class="fas fa-cogs ml-2"></i> الإعدادات
                        </a>
                        <div class="dropdown-divider"></div>
                        <form method="POST" action="{{ route('admin.logout') }}" class="dropdown-item">
                            @csrf
                            <button type="submit" class="btn btn-link p-0 text-decoration-none text-dark">
                                <i class="fas fa-sign-out-alt ml-2"></i> تسجيل الخروج
                            </button>
                        </form>
                    </div>
                </li>
            </ul>
        </nav>

        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- Brand Logo -->
            <a href="{{ route('admin.dashboard') }}" class="brand-link">
                <img src="{{ asset('images/zdeuj.png') }}" alt="Logo" class="brand-image img-circle elevation-3" style="opacity: .8; width: 33px; height: 33px;">
                <span class="brand-text font-weight-light">ميتاء تك</span>
            </a>

            <!-- Sidebar -->
            <div class="sidebar">
                <!-- Sidebar user panel -->
                <div class="user-panel mt-3 pb-3 mb-3 d-flex">
                    <div class="image">
                        <div class="img-circle elevation-2 bg-primary d-flex align-items-center justify-content-center" style="width: 35px; height: 35px;">
                            <i class="fas fa-user text-white"></i>
                        </div>
                    </div>
                    <div class="info">
                        <a href="{{ route('admin.profile.edit') }}" class="d-block">{{ Auth::guard('admin')->user()->name ?? 'المدير' }}</a>
                    </div>
                </div>

                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        
                        <!-- Dashboard -->
                        <li class="nav-item">
                            <a href="{{ route('admin.dashboard') }}" class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <p>لوحة التحكم</p>
                            </a>
                        </li>

                        <!-- جميع الأعمال -->
                        <li class="nav-item">
                            <a href="{{ route('admin.portfolios.index') }}" class="nav-link {{ request()->routeIs('admin.portfolios.index') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-briefcase"></i>
                                <p>جميع الأعمال</p>
                                <span class="badge badge-info right">{{ \App\Models\Portfolio::count() }}</span>
                            </a>
                        </li>

                        <!-- إضافة عمل جديد -->
                        <li class="nav-item">
                            <a href="{{ route('admin.portfolios.create') }}" class="nav-link {{ request()->routeIs('admin.portfolios.create') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-plus"></i>
                                <p>إضافة عمل جديد</p>
                            </a>
                        </li>

                        <!-- Contact Messages -->
                        <li class="nav-item">
                            <a href="{{ route('admin.contacts.index') }}" class="nav-link {{ request()->routeIs('admin.contacts.*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-envelope"></i>
                                <p>الرسائل</p>
                                @php
                                    $unreadCount = \App\Models\Contact::where('is_read', false)->count();
                                @endphp
                                @if($unreadCount > 0)
                                    <span class="badge badge-danger right">{{ $unreadCount }}</span>
                                @endif
                            </a>
                        </li>

                        <!-- Orders Management -->
                        <li class="nav-item">
                            <a href="{{ route('admin.orders.index') }}" class="nav-link {{ request()->routeIs('admin.orders.*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-shopping-cart"></i>
                                <p>الطلبات</p>
                                @php
                                    $pendingOrders = \App\Models\Order::where('status', 'pending')->count();
                                @endphp
                                @if($pendingOrders > 0)
                                    <span class="badge badge-warning right">{{ $pendingOrders }}</span>
                                @endif
                            </a>
                        </li>

                        <!-- Users Management -->
                        <li class="nav-item {{ request()->routeIs('admin.users.*') || request()->routeIs('admin.roles.*') ? 'menu-open' : '' }}">
                            <a href="#" class="nav-link {{ request()->routeIs('admin.users.*') || request()->routeIs('admin.roles.*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-users"></i>
                                <p>
                                    إدارة المستخدمين
                                    <i class="right fas fa-angle-left"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a href="{{ route('admin.users.index') }}" class="nav-link {{ request()->routeIs('admin.users.index') ? 'active' : '' }}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>قائمة المستخدمين</p>
                                        <span class="badge badge-info right">{{ \App\Models\User::count() }}</span>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ route('admin.users.create') }}" class="nav-link {{ request()->routeIs('admin.users.create') ? 'active' : '' }}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>إضافة مستخدم جديد</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ route('admin.roles.index') }}" class="nav-link {{ request()->routeIs('admin.roles.*') ? 'active' : '' }}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>الأدوار والصلاحيات</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a href="{{ route('admin.permissions.index') }}" class="nav-link {{ request()->routeIs('admin.permissions.*') ? 'active' : '' }}">
                                        <i class="far fa-circle nav-icon"></i>
                                        <p>إدارة الصلاحيات</p>
                                    </a>
                                </li>
                            </ul>
                        </li>

                        <!-- User Domains Management -->
                        <li class="nav-item">
                            <a href="{{ route('admin.user-domains.index') }}" class="nav-link {{ request()->routeIs('admin.user-domains.*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-globe"></i>
                                <p>دومينات المستخدمين</p>
                                @php
                                    $expiringSoon = \App\Models\UserDomain::where('expiry_date', '<=', now()->addDays(30))
                                        ->where('expiry_date', '>', now())
                                        ->count();
                                @endphp
                                @if($expiringSoon > 0)
                                    <span class="badge badge-warning right">{{ $expiringSoon }}</span>
                                @endif
                            </a>
                        </li>

                        <!-- Settings -->
                        <li class="nav-header">الإعدادات</li>
                        
                        <li class="nav-item">
                            <a href="{{ route('admin.profile.edit') }}" class="nav-link {{ request()->routeIs('admin.profile.*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-user"></i>
                                <p>الملف الشخصي</p>
                            </a>
                        </li>

                        <li class="nav-item">
                            <a href="{{ route('admin.settings') }}" class="nav-link {{ request()->routeIs('admin.settings*') ? 'active' : '' }}">
                                <i class="nav-icon fas fa-cogs"></i>
                                <p>إعدادات النظام</p>
                            </a>
                        </li>

                        <!-- Logout -->
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                <i class="nav-icon fas fa-sign-out-alt"></i>
                                <p>تسجيل الخروج</p>
                            </a>
                            <form id="logout-form" method="POST" action="{{ route('admin.logout') }}" style="display: none;">
                                @csrf
                            </form>
                        </li>
                    </ul>
                </nav>
            </div>
        </aside>

        <!-- Content Wrapper -->
        <div class="content-wrapper">
            <!-- Content Header -->
            <div class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col-sm-6">
                            <h1 class="m-0">@yield('page-title', 'لوحة التحكم')</h1>
                        </div>
                        <div class="col-sm-6">
                            <ol class="breadcrumb float-sm-left">
                                @yield('breadcrumb')
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Main content -->
            <section class="content">
                <div class="container-fluid">
                    
                    <!-- Alerts -->
                    @if(session('success'))
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle ml-2"></i>
                            {{ session('success') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle ml-2"></i>
                            {{ session('error') }}
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    @if($errors->any())
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle ml-2"></i>
                            <ul class="mb-0">
                                @foreach($errors->all() as $error)
                                    <li>{{ $error }}</li>
                                @endforeach
                            </ul>
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </section>
        </div>

        <!-- Footer -->
        <footer class="main-footer">
            <strong>حقوق الطبع والنشر &copy; {{ date('Y') }} <a href="{{ route('home') }}">ميتاء تك</a>.</strong>
            جميع الحقوق محفوظة.
            <div class="float-left d-none d-sm-inline-block">
                <b>الإصدار</b> 1.0.0
            </div>
        </footer>
    </div>

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Bootstrap 4 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- AdminLTE App -->
    <script src="https://cdn.jsdelivr.net/npm/admin-lte@3.2/dist/js/adminlte.min.js"></script>
    
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Confirm delete actions
        $('.btn-delete').on('click', function(e) {
            e.preventDefault();
            if (confirm('هل أنت متأكد من الحذف؟')) {
                $(this).closest('form').submit();
            }
        });

        // Stable Navigation - Prevent Jitter
        // Disable AdminLTE animations that cause jitter
        if (typeof $.AdminLTE !== 'undefined') {
            $.AdminLTE.options.animationSpeed = 0;
        }

        // Prevent sidebar toggle animations
        $('[data-widget="pushmenu"]').off('click.pushmenu');

        // Smooth page transitions
        $('a[href]:not([href^="#"]):not([href^="javascript:"]):not([target="_blank"])').on('click', function(e) {
            var href = $(this).attr('href');
            if (href && href !== window.location.href) {
                $('.content').addClass('loading');
            }
        });

        // Remove loading class when page loads
        $(window).on('load', function() {
            $('.content').removeClass('loading');
        });

        // Prevent layout shifts on hover
        $('.nav-sidebar .nav-link').on('mouseenter mouseleave', function(e) {
            $(this).css('transform', 'none');
        });

        // Stable card hover effects
        $('.card, .small-box').on('mouseenter mouseleave', function(e) {
            $(this).css('transform', 'none');
        });





        // تحسين زر طي القائمة الجانبية للشاشات الكبيرة فقط
        $('[data-widget="pushmenu"]').off('click').on('click', function(e) {
            e.preventDefault();
            $('body').toggleClass('sidebar-collapse');

            // حفظ حالة القائمة في localStorage
            if ($('body').hasClass('sidebar-collapse')) {
                localStorage.setItem('sidebar-collapsed', 'true');
            } else {
                localStorage.setItem('sidebar-collapsed', 'false');
            }
        });

        // استرجاع حالة القائمة المحفوظة
        if (localStorage.getItem('sidebar-collapsed') === 'true') {
            $('body').addClass('sidebar-collapse');
        }

        // Prevent AdminLTE layout calculations
        if (typeof $.AdminLTE !== 'undefined') {
            $.AdminLTE.layout = {
                activate: function() {},
                fix: function() {},
                fixSidebar: function() {}
            };
        }

        // الحل النهائي المبسط - تثبيت القائمة الجانبية
        document.addEventListener('DOMContentLoaded', () => {
            const sidebar = document.querySelector('.main-sidebar, .sidebar');

            if (sidebar) {
                // تطبيق التثبيت الأساسي
                sidebar.style.position = 'fixed';
                sidebar.style.top = '0';
                sidebar.style.right = '0';
                sidebar.style.left = 'auto';
                sidebar.style.height = '100vh';
                sidebar.style.zIndex = '1040';
                sidebar.style.overflowY = 'auto';
                sidebar.style.width = '250px';
                sidebar.style.display = 'block';
                sidebar.style.visibility = 'visible';
                sidebar.style.opacity = '1';
                sidebar.style.transform = 'translateX(0)';
                sidebar.style.marginRight = '0';
                sidebar.style.backgroundColor = '#374151';

                // تعديل المحتوى الرئيسي
                const contentWrapper = document.querySelector('.content-wrapper');
                if (contentWrapper) {
                    contentWrapper.style.marginRight = '250px';
                    contentWrapper.style.marginLeft = '0';
                }

                // تعديل الترويسة
                const mainHeader = document.querySelector('.main-header');
                if (mainHeader) {
                    mainHeader.style.marginRight = '250px';
                    mainHeader.style.marginLeft = '0';
                    mainHeader.style.width = 'calc(100% - 250px)';
                }

                // مراقبة التغيرات وإصلاحها فوراً
                new MutationObserver(() => {
                    sidebar.style.position = 'fixed';
                    sidebar.style.right = '0';
                    sidebar.style.left = 'auto';
                    sidebar.style.width = '250px';
                    sidebar.style.height = '100vh';
                    sidebar.style.display = 'block';
                    sidebar.style.visibility = 'visible';
                    sidebar.style.opacity = '1';
                    sidebar.style.transform = 'translateX(0)';
                    sidebar.style.backgroundColor = '#374151';
                }).observe(sidebar, {
                    attributes: true,
                    attributeFilter: ['class', 'style']
                });

                console.log('تم تطبيق تثبيت القائمة الجانبية بنجاح');
            }
        });

        // تعطيل وظائف AdminLTE التي تؤثر على القائمة الجانبية
        $(document).on('click', '[data-widget="pushmenu"], .sidebar-toggle', function(e) {
            e.preventDefault();
            return false;
        });

        // تعطيل وظائف AdminLTE Layout
        if (typeof $.AdminLTE !== 'undefined' && $.AdminLTE.Layout) {
            $.AdminLTE.Layout.prototype.fix = function() {};
            $.AdminLTE.Layout.prototype.fixSidebar = function() {};
        }

        // إجبار إظهار القائمة الجانبية - حل شامل
        function forceSidebarDisplay() {
            const sidebar = document.querySelector('.main-sidebar');
            if (sidebar) {
                sidebar.style.cssText = `
                    display: block !important;
                    visibility: visible !important;
                    opacity: 1 !important;
                    transform: translateX(0) !important;
                    position: fixed !important;
                    top: 0 !important;
                    right: 0 !important;
                    left: auto !important;
                    width: 250px !important;
                    height: 100vh !important;
                    z-index: 1000 !important;
                    background: #374151 !important;
                    overflow-y: auto !important;
                    margin-right: 0 !important;
                `;

                // إزالة كلاسات الإخفاء
                sidebar.classList.remove('d-none', 'hidden');
                document.body.classList.remove('sidebar-collapse', 'sidebar-mini');
            }
        }

        // تطبيق الإصلاح فوراً وعند تحميل الصفحة
        forceSidebarDisplay();
        $(document).ready(forceSidebarDisplay);

        // مراقبة التغييرات وإصلاحها
        if (window.MutationObserver) {
            const observer = new MutationObserver(forceSidebarDisplay);
            const sidebar = document.querySelector('.main-sidebar');
            if (sidebar) {
                observer.observe(sidebar, { attributes: true, attributeFilter: ['class', 'style'] });
                observer.observe(document.body, { attributes: true, attributeFilter: ['class'] });
            }
        }

        // إصلاح دوري كل ثانية للتأكد
        setInterval(forceSidebarDisplay, 1000);


    </script>

    @stack('scripts')
</body>
</html>
