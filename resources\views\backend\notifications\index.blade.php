@extends('backend.layouts.app')

@section('title', 'إدارة الإشعارات')

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0">
                        <i class="fas fa-bell me-2 text-primary"></i>
                        إدارة الإشعارات
                    </h1>
                    <p class="text-muted mb-0">إرسال وإدارة الإشعارات للمستخدمين</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-success" onclick="createNotification()">
                        <i class="fas fa-plus me-2"></i>
                        إشعار جديد
                    </button>
                    <button class="btn btn-outline-primary" onclick="broadcastNotification()">
                        <i class="fas fa-broadcast-tower me-2"></i>
                        إرسال جماعي
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">إجمالي الإشعارات</h6>
                            <h3 class="mb-0">1,247</h3>
                        </div>
                        <div class="notification-icon">
                            <i class="fas fa-bell fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">تم إرسالها</h6>
                            <h3 class="mb-0">1,089</h3>
                        </div>
                        <div class="notification-icon">
                            <i class="fas fa-check-circle fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">في الانتظار</h6>
                            <h3 class="mb-0">158</h3>
                        </div>
                        <div class="notification-icon">
                            <i class="fas fa-clock fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="card-title text-white-50 mb-1">معدل القراءة</h6>
                            <h3 class="mb-0">87%</h3>
                        </div>
                        <div class="notification-icon">
                            <i class="fas fa-eye fa-2x opacity-75"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bolt text-warning me-2"></i>
                        إجراءات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <div class="quick-action-card" onclick="sendWelcomeNotification()">
                                <div class="action-icon bg-success">
                                    <i class="fas fa-user-plus"></i>
                                </div>
                                <div class="action-content">
                                    <h6>ترحيب بالمستخدمين الجدد</h6>
                                    <p class="text-muted mb-0">إرسال رسالة ترحيب للمستخدمين الجدد</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="quick-action-card" onclick="sendSystemUpdate()">
                                <div class="action-icon bg-info">
                                    <i class="fas fa-sync-alt"></i>
                                </div>
                                <div class="action-content">
                                    <h6>تحديث النظام</h6>
                                    <p class="text-muted mb-0">إشعار بتحديث النظام أو الصيانة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="quick-action-card" onclick="sendSecurityAlert()">
                                <div class="action-icon bg-danger">
                                    <i class="fas fa-shield-alt"></i>
                                </div>
                                <div class="action-content">
                                    <h6>تنبيه أمني</h6>
                                    <p class="text-muted mb-0">إرسال تنبيه أمني عاجل</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notifications List -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-list text-primary me-2"></i>
                            قائمة الإشعارات
                        </h5>
                        <div class="d-flex gap-2">
                            <select class="form-select form-select-sm" style="width: auto;" onchange="filterNotifications(this.value)">
                                <option value="">جميع الإشعارات</option>
                                <option value="sent">مرسلة</option>
                                <option value="pending">في الانتظار</option>
                                <option value="failed">فشلت</option>
                                <option value="scheduled">مجدولة</option>
                            </select>
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshNotifications()">
                                <i class="fas fa-sync-alt"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" class="form-check-input" onchange="toggleSelectAll(this)">
                                    </th>
                                    <th>العنوان</th>
                                    <th>المستلمين</th>
                                    <th>النوع</th>
                                    <th>الحالة</th>
                                    <th>تاريخ الإرسال</th>
                                    <th width="150">الإجراءات</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input notification-checkbox" value="notif_1">
                                    </td>
                                    <td>
                                        <div class="notification-preview">
                                            <h6 class="mb-1">مرحباً بك في النظام</h6>
                                            <small class="text-muted">رسالة ترحيب للمستخدمين الجدد...</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="recipients-info">
                                            <span class="badge bg-primary">15 مستخدم</span>
                                            <small class="text-muted d-block">مستخدمين جدد</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">ترحيب</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>
                                            مرسلة
                                        </span>
                                    </td>
                                    <td>
                                        <div>15 يناير 2024</div>
                                        <small class="text-muted">10:30 ص</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" onclick="viewNotification('notif_1')" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" onclick="duplicateNotification('notif_1')" title="نسخ">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="editNotification('notif_1')" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteNotification('notif_1')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input notification-checkbox" value="notif_2">
                                    </td>
                                    <td>
                                        <div class="notification-preview">
                                            <h6 class="mb-1">تحديث النظام المجدول</h6>
                                            <small class="text-muted">سيتم تحديث النظام غداً في الساعة 2:00 ص...</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="recipients-info">
                                            <span class="badge bg-info">جميع المستخدمين</span>
                                            <small class="text-muted d-block">247 مستخدم</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-info">نظام</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-warning">
                                            <i class="fas fa-clock me-1"></i>
                                            مجدولة
                                        </span>
                                    </td>
                                    <td>
                                        <div>16 يناير 2024</div>
                                        <small class="text-muted">08:00 ص</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" onclick="viewNotification('notif_2')" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" onclick="duplicateNotification('notif_2')" title="نسخ">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="editNotification('notif_2')" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteNotification('notif_2')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input notification-checkbox" value="notif_3">
                                    </td>
                                    <td>
                                        <div class="notification-preview">
                                            <h6 class="mb-1">تنبيه أمني عاجل</h6>
                                            <small class="text-muted">تم اكتشاف محاولة دخول مشبوهة...</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div class="recipients-info">
                                            <span class="badge bg-danger">المديرين</span>
                                            <small class="text-muted d-block">5 مديرين</small>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-danger">أمني</span>
                                    </td>
                                    <td>
                                        <span class="badge bg-success">
                                            <i class="fas fa-check me-1"></i>
                                            مرسلة
                                        </span>
                                    </td>
                                    <td>
                                        <div>14 يناير 2024</div>
                                        <small class="text-muted">11:45 م</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm">
                                            <button class="btn btn-outline-info" onclick="viewNotification('notif_3')" title="عرض">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-primary" onclick="duplicateNotification('notif_3')" title="نسخ">
                                                <i class="fas fa-copy"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="editNotification('notif_3')" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger" onclick="deleteNotification('notif_3')" title="حذف">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Bulk Actions Footer -->
                <div class="card-footer bg-light border-0" id="bulkNotificationActions" style="display: none;">
                    <div class="d-flex justify-content-between align-items-center">
                        <span class="text-muted" id="selectedNotificationsCount">0 إشعار محدد</span>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-success" onclick="resendSelected()">
                                <i class="fas fa-paper-plane me-1"></i>
                                إعادة إرسال
                            </button>
                            <button class="btn btn-outline-danger" onclick="deleteSelected()">
                                <i class="fas fa-trash me-1"></i>
                                حذف المحدد
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Notification Modal -->
<x-backend.advanced-modal 
    id="createNotificationModal"
    title="إنشاء إشعار جديد"
    size="modal-xl"
    :centered="true"
    :steps="[
        ['title' => 'المحتوى', 'description' => 'كتابة محتوى الإشعار'],
        ['title' => 'المستلمين', 'description' => 'اختيار المستلمين'],
        ['title' => 'الإعدادات', 'description' => 'إعدادات الإرسال'],
        ['title' => 'المراجعة', 'description' => 'مراجعة قبل الإرسال']
    ]">
    
    <x-slot name="step1">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">عنوان الإشعار</label>
                    <input type="text" class="form-control" name="title" placeholder="أدخل عنوان الإشعار">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">نوع الإشعار</label>
                    <select class="form-select" name="type">
                        <option value="info">معلومات</option>
                        <option value="success">نجاح</option>
                        <option value="warning">تحذير</option>
                        <option value="error">خطأ</option>
                    </select>
                </div>
            </div>
            <div class="col-12">
                <div class="mb-3">
                    <label class="form-label">محتوى الإشعار</label>
                    <textarea class="form-control" name="content" rows="5" placeholder="اكتب محتوى الإشعار هنا..."></textarea>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">رابط الإجراء (اختياري)</label>
                    <input type="url" class="form-control" name="action_url" placeholder="https://example.com">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">نص الإجراء</label>
                    <input type="text" class="form-control" name="action_text" placeholder="عرض التفاصيل">
                </div>
            </div>
        </div>
    </x-slot>
    
    <x-slot name="step2">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">نوع المستلمين</label>
                    <select class="form-select" name="recipient_type" onchange="toggleRecipientOptions(this.value)">
                        <option value="all">جميع المستخدمين</option>
                        <option value="role">حسب الدور</option>
                        <option value="specific">مستخدمين محددين</option>
                        <option value="new">المستخدمين الجدد</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6" id="roleSelection" style="display: none;">
                <div class="mb-3">
                    <label class="form-label">اختر الأدوار</label>
                    <select class="form-select" name="roles[]" multiple>
                        <option value="admin">مدير</option>
                        <option value="moderator">مشرف</option>
                        <option value="user">مستخدم</option>
                    </select>
                </div>
            </div>
            <div class="col-12" id="specificUsers" style="display: none;">
                <div class="mb-3">
                    <label class="form-label">اختر المستخدمين</label>
                    <select class="form-select" name="users[]" multiple>
                        <option value="1">أحمد محمد</option>
                        <option value="2">فاطمة علي</option>
                        <option value="3">محمد حسن</option>
                    </select>
                </div>
            </div>
        </div>
    </x-slot>
    
    <x-slot name="step3">
        <div class="row">
            <div class="col-md-6">
                <div class="mb-3">
                    <label class="form-label">وقت الإرسال</label>
                    <select class="form-select" name="send_time" onchange="toggleScheduleOptions(this.value)">
                        <option value="now">الآن</option>
                        <option value="scheduled">مجدول</option>
                    </select>
                </div>
            </div>
            <div class="col-md-6" id="scheduleDateTime" style="display: none;">
                <div class="mb-3">
                    <label class="form-label">تاريخ ووقت الإرسال</label>
                    <input type="datetime-local" class="form-control" name="scheduled_at">
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="email_notification" id="emailNotif">
                        <label class="form-check-label" for="emailNotif">
                            إرسال عبر البريد الإلكتروني
                        </label>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" name="sms_notification" id="smsNotif">
                        <label class="form-check-label" for="smsNotif">
                            إرسال عبر الرسائل النصية
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </x-slot>
    
    <x-slot name="step4">
        <div class="notification-preview-card">
            <h6 class="mb-3">معاينة الإشعار</h6>
            <div class="alert alert-info" id="notificationPreview">
                <div class="d-flex align-items-start">
                    <i class="fas fa-info-circle me-3 mt-1"></i>
                    <div>
                        <h6 class="alert-heading" id="previewTitle">عنوان الإشعار</h6>
                        <p class="mb-0" id="previewContent">محتوى الإشعار سيظهر هنا...</p>
                    </div>
                </div>
            </div>
            
            <div class="notification-summary mt-4">
                <h6>ملخص الإرسال</h6>
                <ul class="list-unstyled">
                    <li><strong>المستلمين:</strong> <span id="summaryRecipients">--</span></li>
                    <li><strong>وقت الإرسال:</strong> <span id="summarySendTime">--</span></li>
                    <li><strong>القنوات:</strong> <span id="summaryChannels">--</span></li>
                </ul>
            </div>
        </div>
    </x-slot>
</x-backend.advanced-modal>
@endsection

@push('after-styles')
<style>
.notification-icon {
    opacity: 0.7;
}

.quick-action-card {
    background: #fff;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    padding: 1.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
    display: flex;
    align-items: center;
    gap: 1rem;
}

.quick-action-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.1);
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.action-content h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #374151;
}

.action-content p {
    margin: 0;
    font-size: 0.875rem;
}

.notification-preview h6 {
    font-size: 0.9rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.25rem;
}

.notification-preview small {
    color: #6b7280;
    line-height: 1.4;
}

.recipients-info .badge {
    font-size: 0.75rem;
    margin-bottom: 0.25rem;
}

.recipients-info small {
    font-size: 0.7rem;
}

.notification-preview-card {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 1.5rem;
}

.notification-summary {
    background: white;
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.notification-summary ul li {
    padding: 0.25rem 0;
    border-bottom: 1px solid #f1f3f4;
}

.notification-summary ul li:last-child {
    border-bottom: none;
}

.table th {
    background: #f8f9fa;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.75rem;
    letter-spacing: 0.5px;
    color: #6c757d;
}

.table td {
    border: none;
    vertical-align: middle;
}

.table tbody tr {
    border-bottom: 1px solid #f1f3f4;
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

.notification-checkbox:checked {
    background-color: #667eea;
    border-color: #667eea;
}

.btn-group .btn {
    padding: 0.375rem 0.5rem;
    font-size: 0.75rem;
}

.badge {
    font-size: 0.75rem;
    padding: 0.35em 0.65em;
}

#bulkNotificationActions {
    animation: slideUp 0.3s ease;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Status badges */
.badge.bg-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%) !important;
}

.badge.bg-warning {
    background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%) !important;
}

.badge.bg-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
}

.badge.bg-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #06b6d4 0%, #0891b2 100%) !important;
}

/* Responsive */
@media (max-width: 768px) {
    .quick-action-card {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }

    .action-icon {
        margin-bottom: 1rem;
    }

    .btn-group {
        flex-direction: column;
        width: 100%;
    }

    .btn-group .btn {
        border-radius: 6px !important;
        margin-bottom: 0.25rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .d-flex.gap-2 {
        flex-direction: column;
        gap: 0.5rem !important;
    }

    .notification-preview h6 {
        font-size: 0.8rem;
    }

    .notification-preview small {
        font-size: 0.7rem;
    }
}

/* Animation for notification sending */
.sending-animation {
    animation: sendPulse 2s infinite;
}

@keyframes sendPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Success animation */
.success-animation {
    animation: successBounce 0.6s ease-in-out;
}

@keyframes successBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}
</style>
@endpush

@push('after-scripts')
<script>
let selectedNotifications = [];

document.addEventListener('DOMContentLoaded', function() {
    initializeNotificationManager();
    setupEventListeners();
});

function initializeNotificationManager() {
    // Setup checkbox event listeners
    document.addEventListener('change', function(e) {
        if (e.target.classList.contains('notification-checkbox')) {
            updateSelectedNotifications();
        }
    });

    // Setup form change listeners for preview
    setupPreviewUpdates();
}

function setupEventListeners() {
    // Auto-refresh notifications every 60 seconds
    setInterval(() => {
        refreshNotifications();
    }, 60000);
}

function setupPreviewUpdates() {
    // Update preview when form fields change
    const modal = document.getElementById('createNotificationModal');
    if (modal) {
        modal.addEventListener('input', updateNotificationPreview);
        modal.addEventListener('change', updateNotificationPreview);
    }
}

function createNotification() {
    const modal = new bootstrap.Modal(document.getElementById('createNotificationModal'));
    modal.show();
}

function broadcastNotification() {
    if (confirm('هل تريد إرسال إشعار جماعي لجميع المستخدمين؟')) {
        // Pre-fill broadcast notification
        createNotification();

        // Set default values for broadcast
        setTimeout(() => {
            document.querySelector('[name="recipient_type"]').value = 'all';
            document.querySelector('[name="title"]').value = 'إشعار عام';
            document.querySelector('[name="type"]').value = 'info';
        }, 100);
    }
}

function sendWelcomeNotification() {
    const welcomeData = {
        title: 'مرحباً بك في النظام',
        content: 'نرحب بك في منصتنا ونتمنى لك تجربة ممتعة ومفيدة.',
        type: 'success',
        recipient_type: 'new',
        send_time: 'now'
    };

    sendQuickNotification(welcomeData, 'ترحيب');
}

function sendSystemUpdate() {
    const updateData = {
        title: 'تحديث النظام',
        content: 'سيتم تحديث النظام غداً في الساعة 2:00 صباحاً. قد تواجه انقطاع مؤقت في الخدمة.',
        type: 'info',
        recipient_type: 'all',
        send_time: 'scheduled'
    };

    sendQuickNotification(updateData, 'تحديث');
}

function sendSecurityAlert() {
    const alertData = {
        title: 'تنبيه أمني',
        content: 'تم اكتشاف نشاط مشبوه في حسابك. يرجى مراجعة إعدادات الأمان.',
        type: 'error',
        recipient_type: 'role',
        roles: ['admin'],
        send_time: 'now'
    };

    sendQuickNotification(alertData, 'تنبيه أمني');
}

function sendQuickNotification(data, typeName) {
    if (confirm(`هل تريد إرسال ${typeName} الآن؟`)) {
        showSendingProgress();

        // Simulate sending
        setTimeout(() => {
            hideSendingProgress();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success(`تم إرسال ${typeName} بنجاح`);
            }

            // Refresh notifications list
            refreshNotifications();
        }, 2000);
    }
}

function showSendingProgress() {
    // Show a temporary notification about sending
    const progressNotif = document.createElement('div');
    progressNotif.id = 'sendingProgress';
    progressNotif.className = 'position-fixed top-0 end-0 m-3 alert alert-info sending-animation';
    progressNotif.style.zIndex = '9999';
    progressNotif.innerHTML = `
        <i class="fas fa-paper-plane me-2"></i>
        جاري إرسال الإشعار...
    `;

    document.body.appendChild(progressNotif);
}

function hideSendingProgress() {
    const progressNotif = document.getElementById('sendingProgress');
    if (progressNotif) {
        progressNotif.remove();
    }
}

function toggleRecipientOptions(type) {
    const roleSelection = document.getElementById('roleSelection');
    const specificUsers = document.getElementById('specificUsers');

    // Hide all options first
    roleSelection.style.display = 'none';
    specificUsers.style.display = 'none';

    // Show relevant option
    if (type === 'role') {
        roleSelection.style.display = 'block';
    } else if (type === 'specific') {
        specificUsers.style.display = 'block';
    }
}

function toggleScheduleOptions(sendTime) {
    const scheduleDateTime = document.getElementById('scheduleDateTime');

    if (sendTime === 'scheduled') {
        scheduleDateTime.style.display = 'block';
        // Set minimum date to now
        const now = new Date();
        now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
        document.querySelector('[name="scheduled_at"]').min = now.toISOString().slice(0, 16);
    } else {
        scheduleDateTime.style.display = 'none';
    }
}

function updateNotificationPreview() {
    const title = document.querySelector('[name="title"]').value || 'عنوان الإشعار';
    const content = document.querySelector('[name="content"]').value || 'محتوى الإشعار سيظهر هنا...';
    const type = document.querySelector('[name="type"]').value;
    const recipientType = document.querySelector('[name="recipient_type"]').value;
    const sendTime = document.querySelector('[name="send_time"]').value;
    const emailNotif = document.querySelector('[name="email_notification"]').checked;
    const smsNotif = document.querySelector('[name="sms_notification"]').checked;

    // Update preview
    document.getElementById('previewTitle').textContent = title;
    document.getElementById('previewContent').textContent = content;

    // Update alert class based on type
    const preview = document.getElementById('notificationPreview');
    preview.className = `alert alert-${type === 'error' ? 'danger' : type}`;

    // Update icon based on type
    const iconMap = {
        'info': 'fa-info-circle',
        'success': 'fa-check-circle',
        'warning': 'fa-exclamation-triangle',
        'error': 'fa-times-circle'
    };
    preview.querySelector('i').className = `fas ${iconMap[type]} me-3 mt-1`;

    // Update summary
    const recipientLabels = {
        'all': 'جميع المستخدمين',
        'role': 'حسب الدور المحدد',
        'specific': 'مستخدمين محددين',
        'new': 'المستخدمين الجدد'
    };

    document.getElementById('summaryRecipients').textContent = recipientLabels[recipientType];
    document.getElementById('summarySendTime').textContent = sendTime === 'now' ? 'فوراً' : 'مجدول';

    const channels = [];
    if (emailNotif) channels.push('البريد الإلكتروني');
    if (smsNotif) channels.push('الرسائل النصية');
    channels.push('الإشعارات الداخلية');

    document.getElementById('summaryChannels').textContent = channels.join(', ');
}

function viewNotification(notificationId) {
    // Simulate loading notification details
    if (typeof NotificationSystem !== 'undefined') {
        NotificationSystem.info('عرض تفاصيل الإشعار...');
    }

    // In a real implementation, you would open a modal with full notification details
    console.log('Viewing notification:', notificationId);
}

function editNotification(notificationId) {
    // Open edit modal with pre-filled data
    createNotification();

    // Simulate loading notification data
    setTimeout(() => {
        // Pre-fill form with existing data
        document.querySelector('[name="title"]').value = 'مرحباً بك في النظام';
        document.querySelector('[name="content"]').value = 'رسالة ترحيب للمستخدمين الجدد...';
        document.querySelector('[name="type"]').value = 'success';
        updateNotificationPreview();
    }, 100);
}

function duplicateNotification(notificationId) {
    if (confirm('هل تريد إنشاء نسخة من هذا الإشعار؟')) {
        editNotification(notificationId);

        if (typeof NotificationSystem !== 'undefined') {
            NotificationSystem.info('تم إنشاء نسخة من الإشعار');
        }
    }
}

function deleteNotification(notificationId) {
    if (confirm('هل أنت متأكد من حذف هذا الإشعار؟')) {
        const row = event.target.closest('tr');

        // Animate removal
        row.style.transition = 'all 0.3s ease';
        row.style.opacity = '0';
        row.style.transform = 'translateX(100px)';

        setTimeout(() => {
            row.remove();
            updateSelectedNotifications();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم حذف الإشعار');
            }
        }, 300);
    }
}

function toggleSelectAll(checkbox) {
    const notificationCheckboxes = document.querySelectorAll('.notification-checkbox');
    notificationCheckboxes.forEach(cb => {
        cb.checked = checkbox.checked;
    });
    updateSelectedNotifications();
}

function updateSelectedNotifications() {
    const checkedBoxes = document.querySelectorAll('.notification-checkbox:checked');
    selectedNotifications = Array.from(checkedBoxes).map(cb => cb.value);

    const selectedCount = document.getElementById('selectedNotificationsCount');
    const bulkActions = document.getElementById('bulkNotificationActions');

    if (selectedNotifications.length > 0) {
        selectedCount.textContent = `${selectedNotifications.length} إشعار محدد`;
        bulkActions.style.display = 'block';
    } else {
        bulkActions.style.display = 'none';
    }
}

function resendSelected() {
    if (selectedNotifications.length === 0) return;

    if (confirm(`هل تريد إعادة إرسال ${selectedNotifications.length} إشعار؟`)) {
        showSendingProgress();

        // Simulate resending
        setTimeout(() => {
            hideSendingProgress();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success(`تم إعادة إرسال ${selectedNotifications.length} إشعار`);
            }

            // Clear selection
            selectedNotifications = [];
            updateSelectedNotifications();

            // Uncheck all checkboxes
            document.querySelectorAll('.notification-checkbox').forEach(cb => cb.checked = false);
            document.querySelector('input[type="checkbox"]').checked = false;
        }, 2000);
    }
}

function deleteSelected() {
    if (selectedNotifications.length === 0) return;

    if (confirm(`هل أنت متأكد من حذف ${selectedNotifications.length} إشعار؟`)) {
        const rows = selectedNotifications.map(id =>
            document.querySelector(`input[value="${id}"]`).closest('tr')
        );

        rows.forEach((row, index) => {
            setTimeout(() => {
                row.style.transition = 'all 0.3s ease';
                row.style.opacity = '0';
                row.style.transform = 'translateX(100px)';

                setTimeout(() => {
                    row.remove();
                }, 300);
            }, index * 100);
        });

        setTimeout(() => {
            selectedNotifications = [];
            updateSelectedNotifications();

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم حذف الإشعارات المحددة');
            }
        }, rows.length * 100 + 300);
    }
}

function filterNotifications(status) {
    const rows = document.querySelectorAll('tbody tr');

    rows.forEach(row => {
        const statusBadge = row.querySelector('.badge');
        const notificationStatus = statusBadge.textContent.trim();

        if (!status ||
            (status === 'sent' && notificationStatus === 'مرسلة') ||
            (status === 'pending' && notificationStatus === 'في الانتظار') ||
            (status === 'failed' && notificationStatus === 'فشلت') ||
            (status === 'scheduled' && notificationStatus === 'مجدولة')) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

function refreshNotifications() {
    const btn = event?.target || document.querySelector('[onclick="refreshNotifications()"]');
    if (btn) {
        const originalHTML = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        btn.disabled = true;

        setTimeout(() => {
            btn.innerHTML = originalHTML;
            btn.disabled = false;

            if (typeof NotificationSystem !== 'undefined') {
                NotificationSystem.success('تم تحديث قائمة الإشعارات');
            }
        }, 1500);
    }
}

// Auto-save draft functionality
let draftSaveTimeout;

function setupAutoSaveDraft() {
    const formInputs = document.querySelectorAll('#createNotificationModal input, #createNotificationModal textarea, #createNotificationModal select');

    formInputs.forEach(input => {
        input.addEventListener('input', () => {
            clearTimeout(draftSaveTimeout);
            draftSaveTimeout = setTimeout(() => {
                saveDraft();
            }, 2000);
        });
    });
}

function saveDraft() {
    const formData = {
        title: document.querySelector('[name="title"]').value,
        content: document.querySelector('[name="content"]').value,
        type: document.querySelector('[name="type"]').value,
        recipient_type: document.querySelector('[name="recipient_type"]').value
    };

    localStorage.setItem('notification_draft', JSON.stringify(formData));

    // Show subtle indication
    const indicator = document.createElement('div');
    indicator.className = 'position-fixed bottom-0 end-0 m-3 alert alert-success alert-sm';
    indicator.style.zIndex = '9999';
    indicator.innerHTML = '<i class="fas fa-save me-2"></i>تم حفظ المسودة';

    document.body.appendChild(indicator);

    setTimeout(() => {
        indicator.remove();
    }, 2000);
}

function loadDraft() {
    const draft = localStorage.getItem('notification_draft');
    if (draft) {
        const data = JSON.parse(draft);

        Object.keys(data).forEach(key => {
            const input = document.querySelector(`[name="${key}"]`);
            if (input) {
                input.value = data[key];
            }
        });

        updateNotificationPreview();
    }
}

// Initialize auto-save when modal opens
document.getElementById('createNotificationModal').addEventListener('shown.bs.modal', function() {
    setupAutoSaveDraft();
    loadDraft();
});
</script>
@endpush
