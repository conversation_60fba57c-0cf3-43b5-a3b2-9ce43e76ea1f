@extends('layouts.admin')

@section('title', 'تعديل المستخدم - ' . $user->name)

@section('content')
<div class="content-wrapper">
    <!-- Content Header -->
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">تعديل المستخدم</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">لوحة التحكم</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.users.index') }}">إدارة المستخدمين</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('admin.users.show', $user) }}">{{ $user->name }}</a></li>
                        <li class="breadcrumb-item active">تعديل</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            
            <div class="row">
                <div class="col-md-12">
                    <div class="card card-warning">
                        <div class="card-header">
                            <h3 class="card-title">تعديل معلومات المستخدم</h3>
                        </div>
                        
                        <form method="POST" action="{{ route('admin.users.update', $user) }}" enctype="multipart/form-data">
                            @csrf
                            @method('PUT')
                            <div class="card-body">
                                
                                <!-- Current Avatar -->
                                @if($user->avatar)
                                <div class="form-group text-center">
                                    <label>الصورة الحالية</label>
                                    <div>
                                        <img src="{{ $user->avatar_url }}" alt="Current Avatar" 
                                             class="img-circle" style="width: 100px; height: 100px;">
                                    </div>
                                </div>
                                @endif

                                <!-- Basic Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="name">الاسم الكامل <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('name') is-invalid @enderror" 
                                                   id="name" name="name" value="{{ old('name', $user->name) }}" required>
                                            @error('name')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="email">البريد الإلكتروني <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control @error('email') is-invalid @enderror" 
                                                   id="email" name="email" value="{{ old('email', $user->email) }}" required>
                                            @error('email')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <!-- Contact Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="phone">رقم الهاتف</label>
                                            <input type="text" class="form-control @error('phone') is-invalid @enderror" 
                                                   id="phone" name="phone" value="{{ old('phone', $user->phone) }}">
                                            @error('phone')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="company">الشركة</label>
                                            <input type="text" class="form-control @error('company') is-invalid @enderror" 
                                                   id="company" name="company" value="{{ old('company', $user->company) }}">
                                            @error('company')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="website">الموقع الإلكتروني</label>
                                            <input type="url" class="form-control @error('website') is-invalid @enderror" 
                                                   id="website" name="website" value="{{ old('website', $user->website) }}">
                                            @error('website')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="location">المدينة</label>
                                            <input type="text" class="form-control @error('location') is-invalid @enderror" 
                                                   id="location" name="location" value="{{ old('location', $user->location) }}">
                                            @error('location')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="address">العنوان</label>
                                    <textarea class="form-control @error('address') is-invalid @enderror" 
                                              id="address" name="address" rows="3">{{ old('address', $user->address) }}</textarea>
                                    @error('address')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Personal Information -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="birth_date">تاريخ الميلاد</label>
                                            <input type="date" class="form-control @error('birth_date') is-invalid @enderror" 
                                                   id="birth_date" name="birth_date" value="{{ old('birth_date', $user->birth_date) }}">
                                            @error('birth_date')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="gender">الجنس</label>
                                            <select class="form-control @error('gender') is-invalid @enderror" 
                                                    id="gender" name="gender">
                                                <option value="">اختر الجنس</option>
                                                <option value="male" {{ old('gender', $user->gender) === 'male' ? 'selected' : '' }}>ذكر</option>
                                                <option value="female" {{ old('gender', $user->gender) === 'female' ? 'selected' : '' }}>أنثى</option>
                                            </select>
                                            @error('gender')
                                                <span class="invalid-feedback">{{ $message }}</span>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="bio">نبذة شخصية</label>
                                    <textarea class="form-control @error('bio') is-invalid @enderror" 
                                              id="bio" name="bio" rows="4">{{ old('bio', $user->bio) }}</textarea>
                                    @error('bio')
                                        <span class="invalid-feedback">{{ $message }}</span>
                                    @enderror
                                </div>

                                <!-- Avatar -->
                                <div class="form-group">
                                    <label for="avatar">تغيير الصورة الشخصية</label>
                                    <div class="input-group">
                                        <div class="custom-file">
                                            <input type="file" class="custom-file-input @error('avatar') is-invalid @enderror" 
                                                   id="avatar" name="avatar" accept="image/*">
                                            <label class="custom-file-label" for="avatar">اختر صورة جديدة</label>
                                        </div>
                                    </div>
                                    @error('avatar')
                                        <span class="invalid-feedback d-block">{{ $message }}</span>
                                    @enderror
                                    <small class="form-text text-muted">
                                        اتركه فارغاً للاحتفاظ بالصورة الحالية. الحد الأقصى: 2 ميجابايت. الصيغ المدعومة: JPG, PNG, GIF
                                    </small>
                                </div>

                                <!-- Notification Settings -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h5 class="card-title">إعدادات الإشعارات</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" 
                                                           id="email_notifications" name="email_notifications" value="1" 
                                                           {{ old('email_notifications', $user->email_notifications) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="email_notifications">
                                                        إشعارات البريد الإلكتروني
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" 
                                                           id="sms_notifications" name="sms_notifications" value="1"
                                                           {{ old('sms_notifications', $user->sms_notifications) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="sms_notifications">
                                                        إشعارات الرسائل النصية
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" 
                                                           id="marketing_emails" name="marketing_emails" value="1"
                                                           {{ old('marketing_emails', $user->marketing_emails) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="marketing_emails">
                                                        رسائل التسويق
                                                    </label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check">
                                                    <input type="checkbox" class="form-check-input" 
                                                           id="order_updates" name="order_updates" value="1"
                                                           {{ old('order_updates', $user->order_updates) ? 'checked' : '' }}>
                                                    <label class="form-check-label" for="order_updates">
                                                        تحديثات الطلبات
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Account Status -->
                                <div class="card mt-4">
                                    <div class="card-header">
                                        <h5 class="card-title">حالة الحساب</h5>
                                    </div>
                                    <div class="card-body">
                                        <div class="row">
                                            <div class="col-md-6">
                                                <p><strong>حالة التفعيل:</strong> 
                                                    @if($user->email_verified_at)
                                                        <span class="badge badge-success">مفعل</span>
                                                    @else
                                                        <span class="badge badge-warning">غير مفعل</span>
                                                    @endif
                                                </p>
                                            </div>
                                            <div class="col-md-6">
                                                <p><strong>تاريخ التسجيل:</strong> {{ $user->created_at->format('Y-m-d H:i:s') }}</p>
                                            </div>
                                            @if($user->last_login_at)
                                            <div class="col-md-6">
                                                <p><strong>آخر دخول:</strong> {{ $user->last_login_at->format('Y-m-d H:i:s') }}</p>
                                            </div>
                                            @endif
                                        </div>
                                    </div>
                                </div>

                            </div>

                            <div class="card-footer">
                                <button type="submit" class="btn btn-warning">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                                <a href="{{ route('admin.users.show', $user) }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                                <button type="button" class="btn btn-{{ $user->email_verified_at ? 'secondary' : 'success' }}" 
                                        onclick="toggleVerification({{ $user->id }})">
                                    <i class="fas fa-{{ $user->email_verified_at ? 'user-times' : 'user-check' }}"></i>
                                    {{ $user->email_verified_at ? 'إلغاء التفعيل' : 'تفعيل الحساب' }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

        </div>
    </section>
</div>
@endsection

@push('scripts')
<script>
$(document).ready(function() {
    // Custom file input
    $('.custom-file-input').on('change', function() {
        let fileName = $(this).val().split('\\').pop();
        $(this).siblings('.custom-file-label').addClass('selected').html(fileName);
    });
});

function toggleVerification(userId) {
    if (confirm('هل أنت متأكد من تغيير حالة تفعيل هذا المستخدم؟')) {
        $.ajax({
            url: `{{ route('admin.users.index') }}/${userId}/toggle-verification`,
            method: 'POST',
            data: {
                _token: '{{ csrf_token() }}'
            },
            success: function(response) {
                location.reload();
            },
            error: function(xhr) {
                alert('حدث خطأ أثناء تغيير حالة التفعيل');
            }
        });
    }
}
</script>
@endpush
