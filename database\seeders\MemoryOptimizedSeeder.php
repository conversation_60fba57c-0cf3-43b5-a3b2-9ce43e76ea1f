<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class MemoryOptimizedSeeder extends Seeder
{
    public function run(): void
    {
        ini_set('memory_limit', '2G'); // زيادة حد الذاكرة
        
        echo "🚀 بدء إنشاء مليون مقال...\n";
        
        try {
            // تحسينات قاعدة البيانات
            DB::statement('SET FOREIGN_KEY_CHECKS=0');
            DB::statement('SET UNIQUE_CHECKS=0');
            DB::statement('SET AUTOCOMMIT=0');
            
            // مسح البيانات الموجودة
            DB::statement('TRUNCATE TABLE blog_posts');
            
            echo "✅ تم تحضير قاعدة البيانات\n";
            
            $totalPosts = 1000000;
            $batchSize = 10000; // دفعات أصغر لتوفير الذاكرة
            $batches = ceil($totalPosts / $batchSize);
            
            // بيانات مبسطة
            $categories = ['البرمجة', 'التصميم', 'التقنية', 'الأمان', 'تحليلات'];
            $authors = ['أحمد محمد', 'سارة أحمد', 'محمد عبدالله', 'فاطمة سالم', 'خالد عمر'];
            $techs = ['React', 'Laravel', 'Python', 'JavaScript', 'PHP'];
            
            for ($batch = 0; $batch < $batches; $batch++) {
                $currentBatchSize = min($batchSize, $totalPosts - ($batch * $batchSize));
                $posts = [];
                
                for ($i = 0; $i < $currentBatchSize; $i++) {
                    $postId = ($batch * $batchSize) + $i + 1;
                    $categoryIndex = $postId % 5;
                    $authorIndex = $postId % 5;
                    $techIndex = $postId % 5;
                    
                    $posts[] = [
                        'title' => "دليل {$techs[$techIndex]} - المقال {$postId}",
                        'slug' => "guide-{$techs[$techIndex]}-{$postId}",
                        'excerpt' => "تعلم {$techs[$techIndex]} في {$categories[$categoryIndex]}",
                        'content' => "<h2>مقدمة</h2><p>دليل {$techs[$techIndex]}</p><h2>المحتوى</h2><p>محتوى تقني</p>",
                        'image' => 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=1200',
                        'category' => $categories[$categoryIndex],
                        'author' => $authors[$authorIndex],
                        'read_time' => (($postId % 15) + 3) . ' دقيقة',
                        'is_featured' => ($postId % 100 == 0) ? 1 : 0,
                        'is_published' => 1,
                        'views' => $postId % 5000,
                        'tags' => json_encode(['تقنية', 'برمجة', $categories[$categoryIndex]]),
                        'meta_data' => json_encode(['difficulty' => ['مبتدئ', 'متوسط', 'متقدم'][$postId % 3]]),
                        'created_at' => date('Y-m-d H:i:s', strtotime('-' . ($postId % 365) . ' days')),
                        'updated_at' => date('Y-m-d H:i:s')
                    ];
                }
                
                // إدراج الدفعة
                DB::table('blog_posts')->insert($posts);
                DB::statement('COMMIT');
                
                $completed = min(($batch + 1) * $batchSize, $totalPosts);
                $percentage = round(($completed / $totalPosts) * 100, 1);
                
                echo "⚡ الدفعة " . ($batch + 1) . "/{$batches} - " . number_format($completed) . " مقال ({$percentage}%)\n";
                
                // تنظيف الذاكرة
                unset($posts);
                
                // استراحة قصيرة كل 10 دفعات
                if ($batch % 10 == 0 && $batch > 0) {
                    usleep(50000); // 0.05 ثانية
                    gc_collect_cycles(); // تنظيف الذاكرة
                }
            }
            
            // إعادة تفعيل القيود
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
            DB::statement('SET UNIQUE_CHECKS=1');
            DB::statement('SET AUTOCOMMIT=1');
            
            echo "🎉 تم إنشاء مليون مقال بنجاح!\n";
            
            // التحقق من العدد النهائي
            $actualCount = DB::table('blog_posts')->count();
            echo "✅ العدد النهائي: " . number_format($actualCount) . " مقال\n";
            
        } catch (\Exception $e) {
            echo "❌ خطأ: " . $e->getMessage() . "\n";
            
            // إعادة تفعيل القيود في حالة الخطأ
            DB::statement('SET FOREIGN_KEY_CHECKS=1');
            DB::statement('SET UNIQUE_CHECKS=1');
            DB::statement('SET AUTOCOMMIT=1');
            
            throw $e;
        }
    }
}
