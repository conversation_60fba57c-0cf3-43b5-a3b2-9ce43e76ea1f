<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Domains\Auth\Models\User;
use Carbon\Carbon;

class ReportsController extends Controller
{
    /**
     * Display the reports page.
     */
    public function index()
    {
        return view('backend.reports.index');
    }

    /**
     * Export reports.
     */
    public function export(Request $request)
    {
        try {
            $type = $request->input('type', 'overview');
            $startDate = $request->input('start_date', now()->subDays(30));
            $endDate = $request->input('end_date', now());
            
            $data = $this->generateReportData($type, $startDate, $endDate);
            
            $filename = "report_{$type}_" . now()->format('Y_m_d_H_i_s') . '.json';
            
            return response()->json($data)
                ->header('Content-Disposition', "attachment; filename={$filename}");
                
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تصدير التقرير: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate report.
     */
    public function generate(Request $request)
    {
        try {
            $type = $request->input('type', 'overview');
            $startDate = $request->input('start_date', now()->subDays(30));
            $endDate = $request->input('end_date', now());
            
            $data = $this->generateReportData($type, $startDate, $endDate);
            
            return response()->json([
                'success' => true,
                'data' => $data,
                'message' => 'تم إنشاء التقرير بنجاح'
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء إنشاء التقرير: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get statistics.
     */
    public function getStats()
    {
        try {
            $stats = Cache::remember('dashboard_stats', 300, function () {
                return [
                    'total_visits' => $this->getTotalVisits(),
                    'active_users' => $this->getActiveUsers(),
                    'conversion_rate' => $this->getConversionRate(),
                    'avg_session_time' => $this->getAverageSessionTime(),
                    'new_users_today' => $this->getNewUsersToday(),
                    'revenue_today' => $this->getRevenueToday(),
                    'bounce_rate' => $this->getBounceRate(),
                    'page_views' => $this->getPageViews()
                ];
            });
            
            return response()->json([
                'success' => true,
                'stats' => $stats
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب الإحصائيات: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get chart data.
     */
    public function getChartData($type, Request $request)
    {
        try {
            $period = $request->input('period', 7);
            $data = [];
            
            switch ($type) {
                case 'traffic':
                    $data = $this->getTrafficChartData($period);
                    break;
                case 'users':
                    $data = $this->getUsersChartData($period);
                    break;
                case 'revenue':
                    $data = $this->getRevenueChartData($period);
                    break;
                case 'performance':
                    $data = $this->getPerformanceChartData($period);
                    break;
                default:
                    throw new \Exception('نوع الرسم البياني غير مدعوم');
            }
            
            return response()->json([
                'success' => true,
                'data' => $data
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء جلب بيانات الرسم البياني: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Export system data.
     */
    public function exportSystemData()
    {
        try {
            $data = [
                'users' => User::with('roles')->get(),
                'system_info' => $this->getSystemInfo(),
                'statistics' => $this->getStats()->getData()->stats,
                'export_date' => now()->toISOString()
            ];
            
            $filename = 'system_data_' . now()->format('Y_m_d_H_i_s') . '.json';
            
            return response()->json($data)
                ->header('Content-Disposition', "attachment; filename={$filename}");
                
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'حدث خطأ أثناء تصدير بيانات النظام: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate report data based on type.
     */
    private function generateReportData($type, $startDate, $endDate)
    {
        $start = Carbon::parse($startDate);
        $end = Carbon::parse($endDate);
        
        switch ($type) {
            case 'users':
                return $this->generateUsersReport($start, $end);
            case 'activity':
                return $this->generateActivityReport($start, $end);
            case 'performance':
                return $this->generatePerformanceReport($start, $end);
            default:
                return $this->generateOverviewReport($start, $end);
        }
    }

    /**
     * Generate users report.
     */
    private function generateUsersReport($startDate, $endDate)
    {
        return [
            'total_users' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'active_users' => User::where('active', true)->whereBetween('created_at', [$startDate, $endDate])->count(),
            'new_registrations' => User::whereBetween('created_at', [$startDate, $endDate])->count(),
            'user_growth' => $this->calculateUserGrowth($startDate, $endDate),
            'users_by_role' => $this->getUsersByRole($startDate, $endDate),
            'registration_trend' => $this->getRegistrationTrend($startDate, $endDate)
        ];
    }

    /**
     * Generate activity report.
     */
    private function generateActivityReport($startDate, $endDate)
    {
        return [
            'total_activities' => $this->getTotalActivities($startDate, $endDate),
            'activities_by_type' => $this->getActivitiesByType($startDate, $endDate),
            'most_active_users' => $this->getMostActiveUsers($startDate, $endDate),
            'activity_timeline' => $this->getActivityTimeline($startDate, $endDate),
            'peak_hours' => $this->getPeakHours($startDate, $endDate)
        ];
    }

    /**
     * Generate performance report.
     */
    private function generatePerformanceReport($startDate, $endDate)
    {
        return [
            'avg_response_time' => $this->getAverageResponseTime($startDate, $endDate),
            'error_rate' => $this->getErrorRate($startDate, $endDate),
            'memory_usage' => $this->getMemoryUsage(),
            'cpu_usage' => $this->getCpuUsage(),
            'database_performance' => $this->getDatabasePerformance($startDate, $endDate),
            'cache_hit_rate' => $this->getCacheHitRate($startDate, $endDate)
        ];
    }

    /**
     * Generate overview report.
     */
    private function generateOverviewReport($startDate, $endDate)
    {
        return [
            'summary' => [
                'total_users' => User::count(),
                'active_users' => User::where('active', true)->count(),
                'total_visits' => $this->getTotalVisits(),
                'conversion_rate' => $this->getConversionRate()
            ],
            'trends' => [
                'user_growth' => $this->calculateUserGrowth($startDate, $endDate),
                'activity_trend' => $this->getActivityTrend($startDate, $endDate),
                'performance_trend' => $this->getPerformanceTrend($startDate, $endDate)
            ],
            'top_metrics' => [
                'most_visited_pages' => $this->getMostVisitedPages($startDate, $endDate),
                'top_users' => $this->getTopUsers($startDate, $endDate),
                'popular_features' => $this->getPopularFeatures($startDate, $endDate)
            ]
        ];
    }

    // Helper methods for statistics
    private function getTotalVisits() { return rand(10000, 20000); }
    private function getActiveUsers() { return User::where('active', true)->count(); }
    private function getConversionRate() { return round(rand(250, 450) / 100, 1); }
    private function getAverageSessionTime() { return rand(180, 300); }
    private function getNewUsersToday() { return User::whereDate('created_at', today())->count(); }
    private function getRevenueToday() { return rand(1000, 5000); }
    private function getBounceRate() { return round(rand(30, 70), 1); }
    private function getPageViews() { return rand(50000, 100000); }

    // Chart data methods
    private function getTrafficChartData($period)
    {
        $data = [];
        for ($i = $period - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'visits' => rand(800, 1500),
                'unique_visitors' => rand(600, 1200),
                'page_views' => rand(2000, 4000)
            ];
        }
        return $data;
    }

    private function getUsersChartData($period)
    {
        $data = [];
        for ($i = $period - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'new_users' => User::whereDate('created_at', $date)->count(),
                'active_users' => rand(50, 200),
                'returning_users' => rand(100, 300)
            ];
        }
        return $data;
    }

    private function getRevenueChartData($period)
    {
        $data = [];
        for ($i = $period - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'revenue' => rand(500, 2000),
                'orders' => rand(10, 50),
                'avg_order_value' => rand(50, 150)
            ];
        }
        return $data;
    }

    private function getPerformanceChartData($period)
    {
        $data = [];
        for ($i = $period - 1; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $data[] = [
                'date' => $date->format('Y-m-d'),
                'response_time' => rand(100, 500),
                'cpu_usage' => rand(20, 80),
                'memory_usage' => rand(40, 90),
                'error_rate' => rand(0, 5)
            ];
        }
        return $data;
    }

    // Additional helper methods
    private function calculateUserGrowth($startDate, $endDate) { return rand(5, 25); }
    private function getUsersByRole($startDate, $endDate) { return []; }
    private function getRegistrationTrend($startDate, $endDate) { return []; }
    private function getTotalActivities($startDate, $endDate) { return rand(1000, 5000); }
    private function getActivitiesByType($startDate, $endDate) { return []; }
    private function getMostActiveUsers($startDate, $endDate) { return []; }
    private function getActivityTimeline($startDate, $endDate) { return []; }
    private function getPeakHours($startDate, $endDate) { return []; }
    private function getAverageResponseTime($startDate, $endDate) { return rand(100, 300); }
    private function getErrorRate($startDate, $endDate) { return rand(1, 5); }
    private function getMemoryUsage() { return rand(40, 80); }
    private function getCpuUsage() { return rand(20, 60); }
    private function getDatabasePerformance($startDate, $endDate) { return []; }
    private function getCacheHitRate($startDate, $endDate) { return rand(80, 95); }
    private function getActivityTrend($startDate, $endDate) { return []; }
    private function getPerformanceTrend($startDate, $endDate) { return []; }
    private function getMostVisitedPages($startDate, $endDate) { return []; }
    private function getTopUsers($startDate, $endDate) { return []; }
    private function getPopularFeatures($startDate, $endDate) { return []; }
    
    private function getSystemInfo()
    {
        return [
            'php_version' => PHP_VERSION,
            'laravel_version' => app()->version(),
            'server_time' => now()->toISOString(),
            'timezone' => config('app.timezone'),
            'environment' => app()->environment()
        ];
    }
}
