{{-- قالب جدول البيانات التفاعلي المتقدم --}}
@props([
    'title' => 'جدول البيانات',
    'columns' => [],
    'data' => [],
    'searchable' => true,
    'sortable' => true,
    'filterable' => true,
    'exportable' => true,
    'pagination' => true,
    'actions' => true,
    'bulkActions' => true,
    'createRoute' => null,
    'editRoute' => null,
    'deleteRoute' => null,
    'viewRoute' => null,
    'perPage' => [10, 25, 50, 100],
    'defaultPerPage' => 25
])

<div class="data-table-container" x-data="dataTable()" x-init="init()">
    <!-- Header Section -->
    <div class="card shadow-sm border-0 mb-4">
        <div class="card-header bg-gradient-primary text-white">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-table me-2"></i>
                        {{ $title }}
                    </h5>
                </div>
                <div class="col-md-6 text-end">
                    @if($createRoute)
                        <a href="{{ $createRoute }}" class="btn btn-light btn-sm">
                            <i class="fas fa-plus me-1"></i>
                            إضافة جديد
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card-body border-bottom">
            <div class="row g-3">
                <!-- Search -->
                @if($searchable)
                <div class="col-md-4">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" 
                               class="form-control" 
                               placeholder="البحث..." 
                               x-model="search"
                               x-on:input.debounce.300ms="filterData()">
                    </div>
                </div>
                @endif

                <!-- Per Page -->
                @if($pagination)
                <div class="col-md-2">
                    <select class="form-select" x-model="perPage" x-on:change="filterData()">
                        @foreach($perPage as $option)
                            <option value="{{ $option }}">{{ $option }}</option>
                        @endforeach
                    </select>
                </div>
                @endif

                <!-- Status Filter -->
                @if($filterable)
                <div class="col-md-3">
                    <select class="form-select" x-model="statusFilter" x-on:change="filterData()">
                        <option value="">جميع الحالات</option>
                        <option value="active">نشط</option>
                        <option value="inactive">غير نشط</option>
                    </select>
                </div>
                @endif

                <!-- Export -->
                @if($exportable)
                <div class="col-md-3">
                    <div class="btn-group w-100">
                        <button class="btn btn-outline-success btn-sm" x-on:click="exportData('excel')">
                            <i class="fas fa-file-excel me-1"></i>
                            Excel
                        </button>
                        <button class="btn btn-outline-danger btn-sm" x-on:click="exportData('pdf')">
                            <i class="fas fa-file-pdf me-1"></i>
                            PDF
                        </button>
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Bulk Actions -->
        @if($bulkActions)
        <div class="card-body border-bottom" x-show="selectedItems.length > 0" x-transition>
            <div class="d-flex align-items-center gap-3">
                <span class="text-muted">
                    تم تحديد <strong x-text="selectedItems.length"></strong> عنصر
                </span>
                <div class="btn-group">
                    <button class="btn btn-sm btn-outline-danger" x-on:click="bulkDelete()">
                        <i class="fas fa-trash me-1"></i>
                        حذف المحدد
                    </button>
                    <button class="btn btn-sm btn-outline-warning" x-on:click="bulkDeactivate()">
                        <i class="fas fa-ban me-1"></i>
                        إلغاء تفعيل
                    </button>
                </div>
            </div>
        </div>
        @endif

        <!-- Table -->
        <div class="table-responsive">
            <table class="table table-hover mb-0">
                <thead class="table-light">
                    <tr>
                        @if($bulkActions)
                        <th width="50">
                            <input type="checkbox" 
                                   class="form-check-input" 
                                   x-on:change="toggleAll($event.target.checked)">
                        </th>
                        @endif

                        @foreach($columns as $column)
                        <th class="{{ $sortable ? 'sortable' : '' }}" 
                            x-on:click="{{ $sortable ? "sort('{$column['key']}')" : '' }}">
                            {{ $column['label'] }}
                            @if($sortable)
                            <i class="fas fa-sort ms-1" 
                               :class="{
                                   'fa-sort-up': sortField === '{{ $column['key'] }}' && sortDirection === 'asc',
                                   'fa-sort-down': sortField === '{{ $column['key'] }}' && sortDirection === 'desc'
                               }"></i>
                            @endif
                        </th>
                        @endforeach

                        @if($actions)
                        <th width="150" class="text-center">الإجراءات</th>
                        @endif
                    </tr>
                </thead>
                <tbody>
                    <template x-for="(item, index) in paginatedData" :key="item.id">
                        <tr class="table-row" :class="{ 'table-active': selectedItems.includes(item.id) }">
                            @if($bulkActions)
                            <td>
                                <input type="checkbox" 
                                       class="form-check-input" 
                                       :value="item.id"
                                       x-on:change="toggleItem(item.id, $event.target.checked)">
                            </td>
                            @endif

                            @foreach($columns as $column)
                            <td>
                                @if(isset($column['type']) && $column['type'] === 'image')
                                <img :src="item.{{ $column['key'] }}" 
                                     class="rounded-circle" 
                                     width="40" height="40"
                                     :alt="item.name">
                                @elseif(isset($column['type']) && $column['type'] === 'badge')
                                <span class="badge" 
                                      :class="getBadgeClass(item.{{ $column['key'] }})"
                                      x-text="item.{{ $column['key'] }}"></span>
                                @elseif(isset($column['type']) && $column['type'] === 'date')
                                <span x-text="formatDate(item.{{ $column['key'] }})"></span>
                                @else
                                <span x-text="item.{{ $column['key'] }}"></span>
                                @endif
                            </td>
                            @endforeach

                            @if($actions)
                            <td class="text-center">
                                <div class="btn-group btn-group-sm">
                                    @if($viewRoute)
                                    <a :href="'{{ $viewRoute }}/' + item.id" 
                                       class="btn btn-outline-info btn-sm" 
                                       title="عرض">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @endif
                                    
                                    @if($editRoute)
                                    <a :href="'{{ $editRoute }}/' + item.id" 
                                       class="btn btn-outline-warning btn-sm" 
                                       title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @endif
                                    
                                    @if($deleteRoute)
                                    <button class="btn btn-outline-danger btn-sm" 
                                            title="حذف"
                                            x-on:click="deleteItem(item.id)">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                    @endif
                                </div>
                            </td>
                            @endif
                        </tr>
                    </template>
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        @if($pagination)
        <div class="card-footer">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <span class="text-muted">
                        عرض <span x-text="startIndex"></span> إلى <span x-text="endIndex"></span> 
                        من <span x-text="filteredData.length"></span> نتيجة
                    </span>
                </div>
                <div class="col-md-6">
                    <nav>
                        <ul class="pagination pagination-sm justify-content-end mb-0">
                            <li class="page-item" :class="{ disabled: currentPage === 1 }">
                                <button class="page-link" x-on:click="goToPage(currentPage - 1)">السابق</button>
                            </li>
                            
                            <template x-for="page in visiblePages" :key="page">
                                <li class="page-item" :class="{ active: page === currentPage }">
                                    <button class="page-link" x-on:click="goToPage(page)" x-text="page"></button>
                                </li>
                            </template>
                            
                            <li class="page-item" :class="{ disabled: currentPage === totalPages }">
                                <button class="page-link" x-on:click="goToPage(currentPage + 1)">التالي</button>
                            </li>
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
function dataTable() {
    return {
        data: @json($data),
        filteredData: [],
        paginatedData: [],
        selectedItems: [],
        search: '',
        statusFilter: '',
        sortField: '',
        sortDirection: 'asc',
        currentPage: 1,
        perPage: {{ $defaultPerPage }},
        
        init() {
            this.filteredData = [...this.data];
            this.filterData();
        },
        
        filterData() {
            let filtered = [...this.data];
            
            // Search filter
            if (this.search) {
                filtered = filtered.filter(item => {
                    return Object.values(item).some(value => 
                        String(value).toLowerCase().includes(this.search.toLowerCase())
                    );
                });
            }
            
            // Status filter
            if (this.statusFilter) {
                filtered = filtered.filter(item => item.status === this.statusFilter);
            }
            
            // Sort
            if (this.sortField) {
                filtered.sort((a, b) => {
                    let aVal = a[this.sortField];
                    let bVal = b[this.sortField];
                    
                    if (this.sortDirection === 'asc') {
                        return aVal > bVal ? 1 : -1;
                    } else {
                        return aVal < bVal ? 1 : -1;
                    }
                });
            }
            
            this.filteredData = filtered;
            this.currentPage = 1;
            this.paginate();
        },
        
        paginate() {
            const start = (this.currentPage - 1) * this.perPage;
            const end = start + this.perPage;
            this.paginatedData = this.filteredData.slice(start, end);
        },
        
        sort(field) {
            if (this.sortField === field) {
                this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
            } else {
                this.sortField = field;
                this.sortDirection = 'asc';
            }
            this.filterData();
        },
        
        toggleAll(checked) {
            if (checked) {
                this.selectedItems = this.paginatedData.map(item => item.id);
            } else {
                this.selectedItems = [];
            }
        },
        
        toggleItem(id, checked) {
            if (checked) {
                this.selectedItems.push(id);
            } else {
                this.selectedItems = this.selectedItems.filter(item => item !== id);
            }
        },
        
        goToPage(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
                this.paginate();
            }
        },
        
        get totalPages() {
            return Math.ceil(this.filteredData.length / this.perPage);
        },
        
        get startIndex() {
            return (this.currentPage - 1) * this.perPage + 1;
        },
        
        get endIndex() {
            return Math.min(this.currentPage * this.perPage, this.filteredData.length);
        },
        
        get visiblePages() {
            const pages = [];
            const start = Math.max(1, this.currentPage - 2);
            const end = Math.min(this.totalPages, this.currentPage + 2);
            
            for (let i = start; i <= end; i++) {
                pages.push(i);
            }
            return pages;
        },
        
        getBadgeClass(status) {
            const classes = {
                'active': 'bg-success',
                'inactive': 'bg-secondary',
                'pending': 'bg-warning',
                'blocked': 'bg-danger'
            };
            return classes[status] || 'bg-secondary';
        },
        
        formatDate(date) {
            return new Date(date).toLocaleDateString('ar-SA');
        },
        
        deleteItem(id) {
            if (confirm('هل أنت متأكد من حذف هذا العنصر؟')) {
                // إرسال طلب حذف
                fetch(`{{ $deleteRoute }}/${id}`, {
                    method: 'DELETE',
                    headers: {
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    }
                }).then(() => {
                    this.data = this.data.filter(item => item.id !== id);
                    this.filterData();
                });
            }
        },
        
        bulkDelete() {
            if (confirm(`هل أنت متأكد من حذف ${this.selectedItems.length} عنصر؟`)) {
                // إرسال طلب حذف جماعي
                fetch('{{ $deleteRoute }}/bulk', {
                    method: 'DELETE',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').content
                    },
                    body: JSON.stringify({ ids: this.selectedItems })
                }).then(() => {
                    this.data = this.data.filter(item => !this.selectedItems.includes(item.id));
                    this.selectedItems = [];
                    this.filterData();
                });
            }
        },
        
        exportData(format) {
            const url = `{{ route('admin.export') }}?format=${format}&search=${this.search}&status=${this.statusFilter}`;
            window.open(url, '_blank');
        }
    }
}
</script>

<style>
.data-table-container .sortable {
    cursor: pointer;
    user-select: none;
}

.data-table-container .sortable:hover {
    background-color: rgba(0,0,0,0.05);
}

.data-table-container .table-row {
    transition: all 0.2s ease;
}

.data-table-container .table-row:hover {
    background-color: rgba(0,123,255,0.1);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}
</style>
