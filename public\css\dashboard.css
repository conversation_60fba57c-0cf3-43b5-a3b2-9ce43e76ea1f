/* Dashboard CSS - ميتاء تك */

/* Base Styles */
* {
    font-family: 'Cairo', sans-serif;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    margin: 0;
    padding: 0;
    background-color: #f8fafc;
}

/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

.sidebar {
    width: 250px;
    background: white;
    border-left: 1px solid #e2e8f0;
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
}

.main-content {
    flex: 1;
    margin-right: 250px;
    background: #f8fafc;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    text-align: center;
}

.sidebar-logo {
    max-width: 120px;
    height: auto;
}

.sidebar-nav {
    padding: 1rem 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.2s ease;
    border-radius: 0;
}

.nav-link:hover {
    background: #f7fafc;
    color: #7c3aed;
}

.nav-link.active {
    background: linear-gradient(135deg, #7c3aed 0%, #3b82f6 100%);
    color: white;
}

.nav-link i {
    margin-left: 0.75rem;
    width: 20px;
    text-align: center;
}

/* Enhanced Sidebar Items */
.sidebar-item {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #4a5568;
    text-decoration: none;
    transition: all 0.3s ease;
    border-radius: 8px;
    margin: 2px 8px;
    font-weight: 500;
    font-size: 14px;
    position: relative;
}

.sidebar-item:hover {
    background: #f8f9fa;
    color: #673ab7;
    transform: translateX(-2px);
}

.sidebar-item.active {
    background: linear-gradient(135deg, #673ab7 0%, #8b5cf6 100%);
    color: white;
    box-shadow: 0 4px 12px rgba(103, 58, 183, 0.3);
}

.sidebar-item i {
    margin-left: 12px;
    width: 20px;
    text-align: center;
    font-size: 16px;
}

/* Dropdown Styles */
.sidebar-dropdown {
    margin: 2px 8px;
}

.dropdown-menu {
    max-height: 0;
    overflow: hidden;
    transition: all 0.3s ease;
    background: transparent;
    border-radius: 8px;
    margin: 4px 0 8px 20px;
    border-left: 2px solid #673ab7;
    padding-left: 12px;
}

.dropdown-menu.show {
    max-height: 300px;
    padding: 8px 0;
}

.dropdown-item {
    display: flex;
    align-items: center;
    padding: 6px 12px;
    color: #6b7280;
    text-decoration: none;
    transition: all 0.2s ease;
    font-size: 13px;
    border-radius: 6px;
    margin: 2px 0;
    position: relative;
}

.dropdown-item:hover {
    background: #f3f4f6;
    color: #673ab7;
    transform: translateX(-2px);
}

.dropdown-item.active {
    background: #673ab7;
    color: white;
}

/* تحسين تصميم القائمة المنسدلة لتطابق الصورة */
.dropdown-item::before {
    content: '';
    position: absolute;
    right: -14px;
    top: 50%;
    transform: translateY(-50%);
    width: 4px;
    height: 4px;
    background: #673ab7;
    border-radius: 50%;
    opacity: 0.6;
}

.chevron-icon {
    transition: transform 0.3s ease;
    font-size: 12px;
}

.sidebar-item.expanded .chevron-icon {
    transform: rotate(180deg);
}

/* تحسين تصميم السهم */
.sidebar-item .chevron-icon {
    margin-right: auto;
    margin-left: 8px;
}

/* Notification Badge */
.notification-badge {
    background: #ff5733;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin-right: auto;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

/* Fixed Sidebar */
.fixed-sidebar {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    overflow-y: auto;
    z-index: 1000;
    scrollbar-width: thin;
    scrollbar-color: #d1d5db #f3f4f6;
}

.fixed-sidebar::-webkit-scrollbar {
    width: 6px;
}

.fixed-sidebar::-webkit-scrollbar-track {
    background: #f3f4f6;
}

.fixed-sidebar::-webkit-scrollbar-thumb {
    background: #d1d5db;
    border-radius: 3px;
}

.fixed-sidebar::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
        z-index: 2000;
    }

    .sidebar.open {
        transform: translateX(0);
        box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
    }

    .main-content {
        margin-right: 0;
        width: 100%;
    }
}

/* Content Styles */
.content-header {
    background: white;
    padding: 1.5rem 2rem;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.content-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.breadcrumb {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: #718096;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

.breadcrumb a {
    color: #718096;
    text-decoration: none;
}

.breadcrumb a:hover {
    color: #7c3aed;
}

/* Cards */
.card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.card-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e2e8f0;
    background: #f7fafc;
}

.card-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #2d3748;
    margin: 0;
}

.card-body {
    padding: 1.5rem;
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.875rem;
}

.btn-primary {
    background: #7c3aed;
    color: white;
}

.btn-primary:hover {
    background: #6d28d9;
}

.btn-secondary {
    background: #e2e8f0;
    color: #4a5568;
}

.btn-secondary:hover {
    background: #cbd5e0;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th,
.table td {
    padding: 0.75rem;
    text-align: right;
    border-bottom: 1px solid #e2e8f0;
}

.table th {
    background: #f7fafc;
    font-weight: 600;
    color: #4a5568;
}

/* Status Badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 500;
}

.status-active {
    background: #c6f6d5;
    color: #22543d;
}

.status-inactive {
    background: #fed7d7;
    color: #c53030;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-right: 0;
    }
}

/* Utilities */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }
.mb-4 { margin-bottom: 1rem; }
.mb-6 { margin-bottom: 1.5rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
