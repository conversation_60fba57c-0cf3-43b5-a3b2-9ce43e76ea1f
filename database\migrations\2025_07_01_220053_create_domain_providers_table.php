<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('domain_providers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Namecheap, GoDaddy, Cloudflare, etc.
            $table->string('type'); // namecheap, godaddy, cloudflare, etc.
            $table->string('api_endpoint')->nullable();
            $table->string('api_key')->nullable();
            $table->string('api_secret')->nullable();
            $table->json('api_config')->nullable(); // Provider-specific configuration
            $table->string('whois_server')->nullable();
            $table->decimal('commission_rate', 5, 2)->default(0); // Commission percentage
            $table->boolean('supports_registration')->default(true);
            $table->boolean('supports_transfer')->default(true);
            $table->boolean('supports_renewal')->default(true);
            $table->boolean('is_active')->default(true);
            $table->json('supported_tlds')->nullable(); // Supported TLDs
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('domain_providers');
    }
};
