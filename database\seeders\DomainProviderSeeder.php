<?php

namespace Database\Seeders;

use App\Models\DomainProvider;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class DomainProviderSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $providers = [
            [
                'name' => 'Namecheap',
                'type' => 'namecheap',
                'api_endpoint' => 'https://api.namecheap.com',
                'api_key' => 'your_namecheap_api_key',
                'api_secret' => 'your_namecheap_api_secret',
                'api_config' => [
                    'api_user' => 'your_api_user',
                    'username' => 'your_username',
                    'sandbox' => true,
                ],
                'whois_server' => 'whois.namecheap.com',
                'commission_rate' => 25.00,
                'supports_registration' => true,
                'supports_transfer' => true,
                'supports_renewal' => true,
                'is_active' => true,
                'supported_tlds' => [
                    'com', 'net', 'org', 'info', 'biz', 'us', 'co', 'io', 'tech', 'online'
                ],
                'notes' => 'Primary domain registrar with competitive pricing',
            ],
            [
                'name' => 'GoDaddy',
                'type' => 'godaddy',
                'api_endpoint' => 'https://api.godaddy.com',
                'api_key' => 'your_godaddy_api_key',
                'api_secret' => 'your_godaddy_api_secret',
                'api_config' => [
                    'environment' => 'production', // or 'sandbox'
                    'version' => 'v1',
                ],
                'whois_server' => 'whois.godaddy.com',
                'commission_rate' => 20.00,
                'supports_registration' => true,
                'supports_transfer' => true,
                'supports_renewal' => true,
                'is_active' => true,
                'supported_tlds' => [
                    'com', 'net', 'org', 'info', 'biz', 'co', 'io', 'me', 'tv', 'cc'
                ],
                'notes' => 'Large registrar with extensive TLD support',
            ],
            [
                'name' => 'Cloudflare Registrar',
                'type' => 'cloudflare',
                'api_endpoint' => 'https://api.cloudflare.com',
                'api_key' => 'your_cloudflare_api_key',
                'api_secret' => null,
                'api_config' => [
                    'account_id' => 'your_account_id',
                    'zone_id' => 'your_zone_id',
                ],
                'whois_server' => 'whois.cloudflare.com',
                'commission_rate' => 15.00,
                'supports_registration' => true,
                'supports_transfer' => true,
                'supports_renewal' => true,
                'is_active' => false, // Not active by default
                'supported_tlds' => [
                    'com', 'net', 'org', 'info', 'biz'
                ],
                'notes' => 'At-cost pricing with advanced DNS features',
            ],
            [
                'name' => 'SaudiNIC (.sa domains)',
                'type' => 'saudnic',
                'api_endpoint' => 'https://nic.sa/api',
                'api_key' => 'your_saudnic_api_key',
                'api_secret' => 'your_saudnic_api_secret',
                'api_config' => [
                    'registrar_id' => 'your_registrar_id',
                    'requires_local_presence' => true,
                ],
                'whois_server' => 'whois.nic.sa',
                'commission_rate' => 30.00,
                'supports_registration' => true,
                'supports_transfer' => true,
                'supports_renewal' => true,
                'is_active' => true,
                'supported_tlds' => [
                    'sa', 'com.sa', 'net.sa', 'org.sa', 'gov.sa', 'edu.sa'
                ],
                'notes' => 'Official registrar for Saudi Arabia domains',
            ],
            [
                'name' => 'Porkbun',
                'type' => 'porkbun',
                'api_endpoint' => 'https://porkbun.com/api/json/v3',
                'api_key' => 'your_porkbun_api_key',
                'api_secret' => 'your_porkbun_api_secret',
                'api_config' => [
                    'environment' => 'live',
                ],
                'whois_server' => 'whois.porkbun.com',
                'commission_rate' => 35.00,
                'supports_registration' => true,
                'supports_transfer' => true,
                'supports_renewal' => true,
                'is_active' => false,
                'supported_tlds' => [
                    'com', 'net', 'org', 'dev', 'app', 'tech', 'online', 'store'
                ],
                'notes' => 'Affordable registrar with good customer service',
            ],
        ];

        foreach ($providers as $provider) {
            DomainProvider::updateOrCreate(
                ['type' => $provider['type']],
                $provider
            );
        }
    }
}
