{{-- قالب البطاقات الإحصائية المتقدمة --}}
@props([
    'cards' => [],
    'animated' => true,
    'realtime' => false,
    'chartEnabled' => true,
    'compareEnabled' => true
])

<div class="stats-cards-container" x-data="statsCards()" x-init="init()">
    <div class="row g-4">
        @foreach($cards as $index => $card)
        <div class="col-xl-3 col-md-6">
            <div class="stats-card {{ $animated ? 'animated-card' : '' }}" 
                 data-card-index="{{ $index }}"
                 x-data="{ 
                     value: {{ $card['value'] ?? 0 }}, 
                     previousValue: {{ $card['previous_value'] ?? 0 }},
                     isLoading: false 
                 }"
                 x-init="animateValue()">
                
                <div class="card border-0 shadow-sm h-100 position-relative overflow-hidden">
                    <!-- Background Pattern -->
                    <div class="card-bg-pattern" style="background: {{ $card['gradient'] ?? 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)' }}"></div>
                    
                    <!-- Loading Overlay -->
                    <div class="loading-overlay" x-show="isLoading" x-transition>
                        <div class="spinner-border text-white" role="status">
                            <span class="visually-hidden">جاري التحميل...</span>
                        </div>
                    </div>
                    
                    <div class="card-body position-relative">
                        <!-- Header -->
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div class="stats-icon">
                                <i class="{{ $card['icon'] ?? 'fas fa-chart-line' }}"></i>
                            </div>
                            
                            @if($realtime)
                            <div class="realtime-indicator">
                                <span class="badge bg-success bg-opacity-20 text-success">
                                    <i class="fas fa-circle pulse-dot me-1"></i>
                                    مباشر
                                </span>
                            </div>
                            @endif
                        </div>
                        
                        <!-- Main Value -->
                        <div class="stats-value mb-2">
                            <h2 class="mb-0 text-white fw-bold" x-text="formatNumber(value)">
                                {{ $card['value'] ?? 0 }}
                            </h2>
                        </div>
                        
                        <!-- Title -->
                        <div class="stats-title mb-3">
                            <h6 class="text-white-50 mb-0">{{ $card['title'] ?? 'إحصائية' }}</h6>
                        </div>
                        
                        <!-- Change Indicator -->
                        @if($compareEnabled && isset($card['change']))
                        <div class="stats-change">
                            <span class="badge {{ $card['change'] >= 0 ? 'bg-success' : 'bg-danger' }} bg-opacity-20 
                                         {{ $card['change'] >= 0 ? 'text-success' : 'text-danger' }}">
                                <i class="fas {{ $card['change'] >= 0 ? 'fa-arrow-up' : 'fa-arrow-down' }} me-1"></i>
                                {{ abs($card['change']) }}%
                                <span class="ms-1">{{ $card['change_period'] ?? 'من الشهر الماضي' }}</span>
                            </span>
                        </div>
                        @endif
                        
                        <!-- Mini Chart -->
                        @if($chartEnabled && isset($card['chart_data']))
                        <div class="mini-chart mt-3">
                            <canvas id="chart-{{ $index }}" width="100" height="30"></canvas>
                        </div>
                        @endif
                        
                        <!-- Additional Info -->
                        @if(isset($card['additional_info']))
                        <div class="additional-info mt-3">
                            <small class="text-white-50">
                                {{ $card['additional_info'] }}
                            </small>
                        </div>
                        @endif
                        
                        <!-- Action Button -->
                        @if(isset($card['action_url']))
                        <div class="stats-action mt-3">
                            <a href="{{ $card['action_url'] }}" 
                               class="btn btn-light btn-sm text-decoration-none">
                                {{ $card['action_text'] ?? 'عرض التفاصيل' }}
                                <i class="fas fa-arrow-left ms-1"></i>
                            </a>
                        </div>
                        @endif
                    </div>
                    
                    <!-- Hover Effect -->
                    <div class="card-hover-effect"></div>
                </div>
            </div>
        </div>
        @endforeach
    </div>
    
    <!-- Summary Row -->
    @if(count($cards) > 1)
    <div class="row mt-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="summary-item">
                                <h5 class="text-primary mb-1" x-text="totalValue">0</h5>
                                <small class="text-muted">إجمالي القيم</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-item">
                                <h5 class="text-success mb-1" x-text="averageGrowth + '%'">0%</h5>
                                <small class="text-muted">متوسط النمو</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-item">
                                <h5 class="text-info mb-1" x-text="highestValue">0</h5>
                                <small class="text-muted">أعلى قيمة</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="summary-item">
                                <h5 class="text-warning mb-1" x-text="lastUpdated">--</h5>
                                <small class="text-muted">آخر تحديث</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif
</div>

<script>
function statsCards() {
    return {
        cards: @json($cards),
        totalValue: 0,
        averageGrowth: 0,
        highestValue: 0,
        lastUpdated: '',
        updateInterval: null,
        
        init() {
            this.calculateSummary();
            this.initCharts();
            
            @if($realtime)
            this.startRealTimeUpdates();
            @endif
            
            @if($animated)
            this.initAnimations();
            @endif
        },
        
        calculateSummary() {
            this.totalValue = this.cards.reduce((sum, card) => sum + (card.value || 0), 0);
            this.highestValue = Math.max(...this.cards.map(card => card.value || 0));
            
            const growthValues = this.cards
                .filter(card => card.change !== undefined)
                .map(card => card.change);
            
            if (growthValues.length > 0) {
                this.averageGrowth = (growthValues.reduce((sum, val) => sum + val, 0) / growthValues.length).toFixed(1);
            }
            
            this.lastUpdated = new Date().toLocaleTimeString('ar-SA');
        },
        
        initCharts() {
            this.cards.forEach((card, index) => {
                if (card.chart_data) {
                    this.createMiniChart(index, card.chart_data);
                }
            });
        },
        
        createMiniChart(index, data) {
            const canvas = document.getElementById(`chart-${index}`);
            if (!canvas) return;
            
            const ctx = canvas.getContext('2d');
            const gradient = ctx.createLinearGradient(0, 0, 0, 30);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
            gradient.addColorStop(1, 'rgba(255, 255, 255, 0.1)');
            
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: data.labels || [],
                    datasets: [{
                        data: data.values || [],
                        borderColor: 'rgba(255, 255, 255, 0.8)',
                        backgroundColor: gradient,
                        borderWidth: 2,
                        fill: true,
                        tension: 0.4,
                        pointRadius: 0,
                        pointHoverRadius: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                        tooltip: { enabled: false }
                    },
                    scales: {
                        x: { display: false },
                        y: { display: false }
                    },
                    elements: {
                        point: { radius: 0 }
                    }
                }
            });
        },
        
        initAnimations() {
            const cards = document.querySelectorAll('.animated-card');
            
            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            });
            
            cards.forEach(card => observer.observe(card));
        },
        
        startRealTimeUpdates() {
            this.updateInterval = setInterval(() => {
                this.fetchUpdatedData();
            }, 30000); // Update every 30 seconds
        },
        
        async fetchUpdatedData() {
            try {
                const response = await fetch('/admin/stats/realtime');
                const data = await response.json();
                
                if (data.success) {
                    this.updateCards(data.cards);
                    this.calculateSummary();
                }
            } catch (error) {
                console.error('Error fetching realtime data:', error);
            }
        },
        
        updateCards(newData) {
            newData.forEach((newCard, index) => {
                if (this.cards[index]) {
                    const cardElement = document.querySelector(`[data-card-index="${index}"]`);
                    const alpineData = Alpine.$data(cardElement);
                    
                    alpineData.previousValue = alpineData.value;
                    alpineData.value = newCard.value;
                    alpineData.animateValue();
                    
                    this.cards[index] = { ...this.cards[index], ...newCard };
                }
            });
        },
        
        animateValue() {
            const duration = 2000;
            const startValue = this.previousValue || 0;
            const endValue = this.value;
            const startTime = Date.now();
            
            const animate = () => {
                const elapsed = Date.now() - startTime;
                const progress = Math.min(elapsed / duration, 1);
                
                const easeOutQuart = 1 - Math.pow(1 - progress, 4);
                const currentValue = startValue + (endValue - startValue) * easeOutQuart;
                
                this.value = Math.round(currentValue);
                
                if (progress < 1) {
                    requestAnimationFrame(animate);
                }
            };
            
            if (startValue !== endValue) {
                animate();
            }
        },
        
        formatNumber(num) {
            if (num >= 1000000) {
                return (num / 1000000).toFixed(1) + 'م';
            } else if (num >= 1000) {
                return (num / 1000).toFixed(1) + 'ك';
            }
            return num.toLocaleString('ar-SA');
        }
    }
}
</script>

<style>
.stats-cards-container .stats-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stats-cards-container .stats-card:hover {
    transform: translateY(-5px);
}

.stats-cards-container .card {
    border-radius: 15px;
    overflow: hidden;
}

.stats-cards-container .card-bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0.9;
}

.stats-cards-container .card-bg-pattern::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
}

.stats-cards-container .stats-icon {
    width: 60px;
    height: 60px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stats-cards-container .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.stats-cards-container .pulse-dot {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.stats-cards-container .realtime-indicator {
    position: absolute;
    top: 15px;
    left: 15px;
}

.stats-cards-container .mini-chart {
    height: 30px;
    margin-top: 15px;
}

.stats-cards-container .card-hover-effect {
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.stats-cards-container .stats-card:hover .card-hover-effect {
    left: 100%;
}

.stats-cards-container .animated-card {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.stats-cards-container .animated-card.animate-in {
    opacity: 1;
    transform: translateY(0);
}

.stats-cards-container .summary-item {
    padding: 15px;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.02);
    margin: 0 5px;
}

.stats-cards-container .summary-item:hover {
    background: rgba(0, 0, 0, 0.05);
    transform: translateY(-2px);
    transition: all 0.3s ease;
}
</style>
