<?php

namespace App\Services;

use App\Models\HostingProvider;
use App\Models\Order;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HostingProviderService
{
    protected $provider;

    public function __construct(HostingProvider $provider)
    {
        $this->provider = $provider;
    }

    /**
     * Create hosting account with provider
     */
    public function createHostingAccount(Order $order)
    {
        try {
            switch ($this->provider->type) {
                case 'hostinger_reseller':
                    return $this->createHostingerAccount($order);
                case 'digitalocean':
                    return $this->createDigitalOceanDroplet($order);
                case 'cpanel_whm':
                    return $this->createCPanelAccount($order);
                default:
                    throw new \Exception('Unsupported provider type: ' . $this->provider->type);
            }
        } catch (\Exception $e) {
            Log::error('Failed to create hosting account', [
                'provider' => $this->provider->name,
                'order_id' => $order->id,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * Create account with Hostinger Reseller API
     */
    private function createHostingerAccount(Order $order)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->provider->api_key,
            'Content-Type' => 'application/json',
        ])->post($this->provider->api_endpoint . '/accounts', [
            'domain' => $order->domain_name,
            'username' => $this->generateUsername($order->domain_name),
            'password' => $this->generatePassword(),
            'email' => $order->customer_email,
            'plan' => $order->hostingPlan->name_en,
            'quota' => $this->getQuotaFromPlan($order->hostingPlan),
        ]);

        if ($response->successful()) {
            return [
                'success' => true,
                'account_id' => $response->json('account_id'),
                'username' => $response->json('username'),
                'password' => $response->json('password'),
                'control_panel_url' => $response->json('control_panel_url'),
            ];
        }

        throw new \Exception('Hostinger API Error: ' . $response->body());
    }

    /**
     * Create DigitalOcean Droplet
     */
    private function createDigitalOceanDroplet(Order $order)
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $this->provider->api_key,
            'Content-Type' => 'application/json',
        ])->post('https://api.digitalocean.com/v2/droplets', [
            'name' => $this->sanitizeName($order->domain_name),
            'region' => 'fra1', // Frankfurt
            'size' => $this->getDropletSize($order->hostingPlan),
            'image' => 'ubuntu-22-04-x64',
            'ssh_keys' => [],
            'backups' => true,
            'ipv6' => true,
            'monitoring' => true,
            'tags' => ['codecraft-client', 'order-' . $order->id],
        ]);

        if ($response->successful()) {
            $droplet = $response->json('droplet');
            return [
                'success' => true,
                'droplet_id' => $droplet['id'],
                'ip_address' => $droplet['networks']['v4'][0]['ip_address'] ?? null,
                'status' => $droplet['status'],
            ];
        }

        throw new \Exception('DigitalOcean API Error: ' . $response->body());
    }

    /**
     * Create cPanel account via WHM API
     */
    private function createCPanelAccount(Order $order)
    {
        $username = $this->generateUsername($order->domain_name);
        $password = $this->generatePassword();

        $response = Http::withHeaders([
            'Authorization' => 'WHM ' . $this->provider->api_key,
        ])->post($this->provider->api_endpoint . '/json-api/createacct', [
            'username' => $username,
            'password' => $password,
            'domain' => $order->domain_name,
            'contactemail' => $order->customer_email,
            'plan' => $order->hostingPlan->name_en,
            'quota' => $this->getQuotaFromPlan($order->hostingPlan),
        ]);

        if ($response->successful()) {
            $result = $response->json();
            if ($result['metadata']['result'] == 1) {
                return [
                    'success' => true,
                    'username' => $username,
                    'password' => $password,
                    'control_panel_url' => 'https://' . $order->domain_name . ':2083',
                ];
            }
        }

        throw new \Exception('WHM API Error: ' . $response->body());
    }

    /**
     * Suspend hosting account
     */
    public function suspendAccount(Order $order)
    {
        switch ($this->provider->type) {
            case 'hostinger_reseller':
                return $this->suspendHostingerAccount($order);
            case 'cpanel_whm':
                return $this->suspendCPanelAccount($order);
            default:
                throw new \Exception('Suspend not supported for provider type: ' . $this->provider->type);
        }
    }

    /**
     * Unsuspend hosting account
     */
    public function unsuspendAccount(Order $order)
    {
        switch ($this->provider->type) {
            case 'hostinger_reseller':
                return $this->unsuspendHostingerAccount($order);
            case 'cpanel_whm':
                return $this->unsuspendCPanelAccount($order);
            default:
                throw new \Exception('Unsuspend not supported for provider type: ' . $this->provider->type);
        }
    }

    /**
     * Delete hosting account
     */
    public function deleteAccount(Order $order)
    {
        switch ($this->provider->type) {
            case 'hostinger_reseller':
                return $this->deleteHostingerAccount($order);
            case 'digitalocean':
                return $this->deleteDigitalOceanDroplet($order);
            case 'cpanel_whm':
                return $this->deleteCPanelAccount($order);
            default:
                throw new \Exception('Delete not supported for provider type: ' . $this->provider->type);
        }
    }

    /**
     * Generate username from domain
     */
    private function generateUsername($domain)
    {
        $username = preg_replace('/[^a-zA-Z0-9]/', '', str_replace('.', '', $domain));
        return substr(strtolower($username), 0, 8) . rand(100, 999);
    }

    /**
     * Generate secure password
     */
    private function generatePassword($length = 12)
    {
        $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
        return substr(str_shuffle($chars), 0, $length);
    }

    /**
     * Get quota from hosting plan
     */
    private function getQuotaFromPlan($plan)
    {
        // Extract quota from plan storage (e.g., "50GB" -> 50000)
        $storage = $plan->storage;
        if (stripos($storage, 'unlimited') !== false) {
            return 0; // 0 means unlimited in most hosting panels
        }
        
        $quota = (int) filter_var($storage, FILTER_SANITIZE_NUMBER_INT);
        if (stripos($storage, 'GB') !== false) {
            return $quota * 1024; // Convert GB to MB
        }
        
        return $quota;
    }

    /**
     * Get DigitalOcean droplet size based on hosting plan
     */
    private function getDropletSize($plan)
    {
        switch ($plan->type) {
            case 'basic':
                return 's-1vcpu-1gb';
            case 'business':
                return 's-2vcpu-2gb';
            case 'pro':
                return 's-4vcpu-8gb';
            default:
                return 's-1vcpu-1gb';
        }
    }

    /**
     * Sanitize name for DigitalOcean
     */
    private function sanitizeName($name)
    {
        return preg_replace('/[^a-zA-Z0-9-]/', '-', strtolower($name));
    }

    // Additional methods for suspend, unsuspend, delete operations...
    private function suspendHostingerAccount(Order $order)
    {
        // Implementation for Hostinger suspend
    }

    private function suspendCPanelAccount(Order $order)
    {
        // Implementation for cPanel suspend
    }

    private function unsuspendHostingerAccount(Order $order)
    {
        // Implementation for Hostinger unsuspend
    }

    private function unsuspendCPanelAccount(Order $order)
    {
        // Implementation for cPanel unsuspend
    }

    private function deleteHostingerAccount(Order $order)
    {
        // Implementation for Hostinger delete
    }

    private function deleteDigitalOceanDroplet(Order $order)
    {
        // Implementation for DigitalOcean delete
    }

    private function deleteCPanelAccount(Order $order)
    {
        // Implementation for cPanel delete
    }
}
