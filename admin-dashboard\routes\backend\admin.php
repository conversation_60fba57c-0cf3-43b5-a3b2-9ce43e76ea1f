<?php

use App\Http\Controllers\Backend\DashboardController;
use App\Http\Controllers\Backend\SettingsController;
use App\Http\Controllers\Backend\ReportsController;
use App\Http\Controllers\Backend\FilesController;
use App\Http\Controllers\Backend\ActivityLogController;
use App\Http\Controllers\Backend\BackupsController;
use App\Http\Controllers\Backend\NotificationsController;
use Tabuna\Breadcrumbs\Trail;

// All route names are prefixed with 'admin.'.
Route::redirect('/', '/admin/dashboard', 301);

// Dashboard Routes
Route::get('dashboard', [DashboardController::class, 'index'])
    ->name('dashboard')
    ->breadcrumbs(function (Trail $trail) {
        $trail->push(__('Home'), route('admin.dashboard'));
    });

Route::get('dashboard/stats', [DashboardController::class, 'getStats'])->name('dashboard.stats');
Route::post('cache/clear', [DashboardController::class, 'clearCache'])->name('cache.clear');

// Settings Routes
Route::prefix('settings')->name('settings.')->group(function () {
    Route::get('/', [SettingsController::class, 'index'])->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('إعدادات النظام', route('admin.settings.index'));
        });
    Route::post('update', [SettingsController::class, 'update'])->name('update');
    Route::post('test-email', [SettingsController::class, 'testEmail'])->name('test-email');
    Route::post('cache/clear-all', [SettingsController::class, 'clearAllCache'])->name('cache.clear-all');
    Route::post('logs/clear', [SettingsController::class, 'clearLogs'])->name('logs.clear');
    Route::post('database/optimize', [SettingsController::class, 'optimizeDatabase'])->name('database.optimize');
});

// Reports Routes
Route::prefix('reports')->name('reports.')->group(function () {
    Route::get('/', [ReportsController::class, 'index'])->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('التقارير والإحصائيات', route('admin.reports.index'));
        });
    Route::get('export', [ReportsController::class, 'export'])->name('export');
    Route::post('generate', [ReportsController::class, 'generate'])->name('generate');
    Route::get('stats', [ReportsController::class, 'getStats'])->name('stats');
    Route::get('chart-data/{type}', [ReportsController::class, 'getChartData'])->name('chart-data');
});

// Files Management Routes
Route::prefix('files')->name('files.')->group(function () {
    Route::get('/', [FilesController::class, 'index'])->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('إدارة الملفات', route('admin.files.index'));
        });
    Route::post('upload', [FilesController::class, 'upload'])->name('upload');
    Route::get('download/{file}', [FilesController::class, 'download'])->name('download');
    Route::delete('delete/{file}', [FilesController::class, 'delete'])->name('delete');
    Route::post('create-folder', [FilesController::class, 'createFolder'])->name('create-folder');
    Route::post('rename', [FilesController::class, 'rename'])->name('rename');
    Route::post('move', [FilesController::class, 'move'])->name('move');
    Route::post('copy', [FilesController::class, 'copy'])->name('copy');
    Route::get('browse/{path?}', [FilesController::class, 'browse'])->name('browse')->where('path', '.*');
    Route::post('bulk-action', [FilesController::class, 'bulkAction'])->name('bulk-action');
});

// Activity Log Routes
Route::prefix('activity-log')->name('activity-log.')->group(function () {
    Route::get('/', [ActivityLogController::class, 'index'])->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('سجل النشاط', route('admin.activity-log.index'));
        });
    Route::get('export', [ActivityLogController::class, 'export'])->name('export');
    Route::post('clear-old', [ActivityLogController::class, 'clearOld'])->name('clear-old');
    Route::get('check-new', [ActivityLogController::class, 'checkNew'])->name('check-new');
    Route::get('filter', [ActivityLogController::class, 'filter'])->name('filter');
    Route::get('details/{id}', [ActivityLogController::class, 'details'])->name('details');
});

// Backups Routes
Route::prefix('backups')->name('backups.')->group(function () {
    Route::get('/', [BackupsController::class, 'index'])->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('النسخ الاحتياطية', route('admin.backups.index'));
        });
    Route::post('create', [BackupsController::class, 'create'])->name('create');
    Route::get('download/{backup}', [BackupsController::class, 'download'])->name('download');
    Route::post('restore/{backup}', [BackupsController::class, 'restore'])->name('restore');
    Route::delete('delete/{backup}', [BackupsController::class, 'delete'])->name('delete');
    Route::post('verify/{backup}', [BackupsController::class, 'verify'])->name('verify');
    Route::get('status', [BackupsController::class, 'status'])->name('status');
    Route::post('schedule', [BackupsController::class, 'schedule'])->name('schedule');
    Route::post('bulk-action', [BackupsController::class, 'bulkAction'])->name('bulk-action');
});

// Notifications Routes
Route::prefix('notifications')->name('notifications.')->group(function () {
    Route::get('/', [NotificationsController::class, 'index'])->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('إدارة الإشعارات', route('admin.notifications.index'));
        });
    Route::post('create', [NotificationsController::class, 'create'])->name('create');
    Route::post('send', [NotificationsController::class, 'send'])->name('send');
    Route::post('broadcast', [NotificationsController::class, 'broadcast'])->name('broadcast');
    Route::get('view/{notification}', [NotificationsController::class, 'view'])->name('view');
    Route::put('edit/{notification}', [NotificationsController::class, 'edit'])->name('edit');
    Route::delete('delete/{notification}', [NotificationsController::class, 'delete'])->name('delete');
    Route::post('duplicate/{notification}', [NotificationsController::class, 'duplicate'])->name('duplicate');
    Route::post('resend/{notification}', [NotificationsController::class, 'resend'])->name('resend');
    Route::post('bulk-action', [NotificationsController::class, 'bulkAction'])->name('bulk-action');
    Route::get('templates', [NotificationsController::class, 'templates'])->name('templates');
});

// Enhanced User Management Routes
Route::prefix('auth/user')->name('auth.user.')->group(function () {
    Route::get('export', [\App\Http\Controllers\Backend\Auth\User\UserController::class, 'export'])->name('export');
    Route::post('bulk-action', [\App\Http\Controllers\Backend\Auth\User\UserController::class, 'bulkAction'])->name('bulk-action');
    Route::get('activity/{user}', [\App\Http\Controllers\Backend\Auth\User\UserController::class, 'activity'])->name('activity');
    Route::post('impersonate/{user}', [\App\Http\Controllers\Backend\Auth\User\UserController::class, 'impersonate'])->name('impersonate');
    Route::post('import', [\App\Http\Controllers\Backend\Auth\User\UserController::class, 'import'])->name('import');
});

// API Routes for AJAX calls
Route::prefix('api')->name('api.')->group(function () {
    Route::get('system-info', [DashboardController::class, 'getSystemInfo'])->name('system-info');
    Route::get('performance-metrics', [DashboardController::class, 'getPerformanceMetrics'])->name('performance-metrics');
    Route::get('recent-activity', [ActivityLogController::class, 'getRecentActivity'])->name('recent-activity');
    Route::get('storage-usage', [FilesController::class, 'getStorageUsage'])->name('storage-usage');
    Route::get('backup-status', [BackupsController::class, 'getBackupStatus'])->name('backup-status');
    Route::get('notification-stats', [NotificationsController::class, 'getStats'])->name('notification-stats');
});

// System Maintenance Routes
Route::prefix('maintenance')->name('maintenance.')->group(function () {
    Route::get('/', function () {
        return view('backend.maintenance.index');
    })->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('صيانة النظام', route('admin.maintenance.index'));
        });
    Route::post('enable', [SettingsController::class, 'enableMaintenance'])->name('enable');
    Route::post('disable', [SettingsController::class, 'disableMaintenance'])->name('disable');
    Route::get('status', [SettingsController::class, 'maintenanceStatus'])->name('status');
});

// Health Check Routes
Route::prefix('health')->name('health.')->group(function () {
    Route::get('/', function () {
        return view('backend.health.index');
    })->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('فحص النظام', route('admin.health.index'));
        });
    Route::get('check', [DashboardController::class, 'healthCheck'])->name('check');
    Route::get('database', [DashboardController::class, 'databaseHealth'])->name('database');
    Route::get('storage', [DashboardController::class, 'storageHealth'])->name('storage');
    Route::get('services', [DashboardController::class, 'servicesHealth'])->name('services');
});

// Security Routes
Route::prefix('security')->name('security.')->group(function () {
    Route::get('/', function () {
        return view('backend.security.index');
    })->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('الأمان', route('admin.security.index'));
        });
    Route::get('audit', [ActivityLogController::class, 'securityAudit'])->name('audit');
    Route::get('failed-logins', [ActivityLogController::class, 'failedLogins'])->name('failed-logins');
    Route::post('block-ip', [SettingsController::class, 'blockIP'])->name('block-ip');
    Route::post('unblock-ip', [SettingsController::class, 'unblockIP'])->name('unblock-ip');
    Route::get('blocked-ips', [SettingsController::class, 'getBlockedIPs'])->name('blocked-ips');
});

// Tools Routes
Route::prefix('tools')->name('tools.')->group(function () {
    Route::get('/', function () {
        return view('backend.tools.index');
    })->name('index')
        ->breadcrumbs(function (Trail $trail) {
            $trail->parent('admin.dashboard')
                  ->push('أدوات النظام', route('admin.tools.index'));
        });
    Route::post('artisan', [SettingsController::class, 'runArtisanCommand'])->name('artisan');
    Route::get('phpinfo', [SettingsController::class, 'phpInfo'])->name('phpinfo');
    Route::get('server-info', [SettingsController::class, 'serverInfo'])->name('server-info');
    Route::post('test-email', [SettingsController::class, 'testEmailConfiguration'])->name('test-email');
    Route::post('test-database', [SettingsController::class, 'testDatabaseConnection'])->name('test-database');
});

// Quick Actions (for dashboard widgets)
Route::post('quick/clear-cache', [DashboardController::class, 'quickClearCache'])->name('quick.clear-cache');
Route::post('quick/backup-database', [BackupsController::class, 'quickBackupDatabase'])->name('quick.backup-database');
Route::post('quick/send-test-notification', [NotificationsController::class, 'quickTestNotification'])->name('quick.test-notification');

// Real-time updates
Route::get('realtime/dashboard-stats', [DashboardController::class, 'realtimeStats'])->name('realtime.dashboard-stats');
Route::get('realtime/system-status', [DashboardController::class, 'realtimeSystemStatus'])->name('realtime.system-status');
Route::get('realtime/activity-feed', [ActivityLogController::class, 'realtimeActivityFeed'])->name('realtime.activity-feed');
