<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Domain extends Model
{
    protected $fillable = [
        'extension',
        'tld_category',
        'description',
        'registration_price',
        'renewal_price',
        'transfer_price',
        'setup_fee',
        'min_years',
        'max_years',
        'is_premium',
        'requires_verification',
        'auto_renewal_available',
        'features',
        'pricing_tiers',
        'domain_provider_id',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'registration_price' => 'decimal:2',
        'renewal_price' => 'decimal:2',
        'transfer_price' => 'decimal:2',
        'setup_fee' => 'decimal:2',
        'is_premium' => 'boolean',
        'requires_verification' => 'boolean',
        'auto_renewal_available' => 'boolean',
        'features' => 'array',
        'pricing_tiers' => 'array',
        'is_active' => 'boolean',
    ];

    public function domainProvider(): BelongsTo
    {
        return $this->belongsTo(DomainProvider::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function getFormattedExtensionAttribute(): string
    {
        return '.' . ltrim($this->extension, '.');
    }

    public function getCategoryColorAttribute(): string
    {
        return match($this->tld_category) {
            'popular' => 'bg-blue-500',
            'country' => 'bg-green-500',
            'new' => 'bg-purple-500',
            'premium' => 'bg-yellow-500',
            default => 'bg-gray-500'
        };
    }

    public function getCategoryNameAttribute(): string
    {
        return match($this->tld_category) {
            'popular' => 'شائع',
            'country' => 'دولي',
            'new' => 'جديد',
            'premium' => 'مميز',
            default => 'عام'
        };
    }
}
