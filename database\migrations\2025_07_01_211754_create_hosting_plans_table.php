<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('hosting_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name_ar');
            $table->string('name_en');
            $table->text('description_ar');
            $table->text('description_en');
            $table->enum('type', ['basic', 'business', 'pro']);
            $table->string('storage'); // e.g., "5GB", "50GB", "Unlimited"
            $table->integer('websites_limit'); // -1 for unlimited
            $table->integer('email_accounts'); // -1 for unlimited
            $table->boolean('ssl_certificate')->default(true);
            $table->boolean('support_24_7')->default(true);
            $table->decimal('monthly_price', 8, 2);
            $table->decimal('yearly_price', 8, 2);
            $table->json('features')->nullable(); // Additional features as JSON
            $table->boolean('is_popular')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('hosting_plans');
    }
};
