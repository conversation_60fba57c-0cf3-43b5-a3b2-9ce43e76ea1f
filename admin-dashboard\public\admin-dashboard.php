<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>لوحة التحكم الإدارية - النظام الكامل</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --primary-color: #667eea;
            --secondary-color: #764ba2;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            transition: all 0.3s ease;
        }

        /* Dark Mode Styles */
        body.dark-mode {
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
        }

        body.dark-mode .main-wrapper {
            background: #1f2937;
            color: #e5e7eb;
        }

        body.dark-mode .card {
            background: #374151;
            color: #e5e7eb;
        }

        body.dark-mode .topbar {
            background: #374151;
            border-bottom-color: #4b5563;
        }

        body.dark-mode .form-control,
        body.dark-mode .form-select {
            background: #4b5563;
            border-color: #6b7280;
            color: #e5e7eb;
        }

        body.dark-mode .form-control:focus,
        body.dark-mode .form-select:focus {
            background: #4b5563;
            border-color: #667eea;
            color: #e5e7eb;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        body.dark-mode .list-group-item {
            background: #4b5563;
            border-color: #6b7280;
            color: #e5e7eb;
        }

        body.dark-mode .quick-action-card {
            background: #4b5563;
            border-color: #6b7280;
            color: #e5e7eb;
        }

        body.dark-mode .quick-action-card:hover {
            border-color: #667eea;
        }

        body.dark-mode .text-muted {
            color: #9ca3af !important;
        }

        body.dark-mode .card-header {
            background: #4b5563;
            border-bottom-color: #6b7280;
        }

        body.dark-mode .dropdown-menu {
            background: #374151;
            border-color: #6b7280;
        }

        body.dark-mode .dropdown-item {
            color: #e5e7eb;
        }

        body.dark-mode .dropdown-item:hover {
            background: #4b5563;
            color: #fff;
        }

        body.dark-mode .btn-outline-secondary {
            border-color: #6b7280;
            color: #e5e7eb;
        }

        body.dark-mode .btn-outline-secondary:hover {
            background: #6b7280;
            border-color: #6b7280;
            color: #fff;
        }

        body.dark-mode .sidebar {
            background: linear-gradient(180deg, #111827 0%, #0f172a 100%);
        }

        body.dark-mode .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.05);
        }

        body.dark-mode .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
        }

        body.dark-mode .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.4);
        }

        body.dark-mode .stat-card {
            background: linear-gradient(135deg, #4b5563 0%, #374151 100%) !important;
        }

        body.dark-mode .stat-card.success {
            background: linear-gradient(135deg, #065f46 0%, #047857 100%) !important;
        }

        body.dark-mode .stat-card.warning {
            background: linear-gradient(135deg, #92400e 0%, #b45309 100%) !important;
        }

        body.dark-mode .stat-card.danger {
            background: linear-gradient(135deg, #991b1b 0%, #b91c1c 100%) !important;
        }

        body.dark-mode .stat-card.info {
            background: linear-gradient(135deg, #0c4a6e 0%, #0369a1 100%) !important;
        }
        
        .main-wrapper {
            background: white;
            min-height: 100vh;
            margin: 20px;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .sidebar {
            background: linear-gradient(180deg, #1f2937 0%, #111827 100%);
            min-height: 100vh;
            height: 100vh;
            width: 280px;
            position: fixed;
            right: 0;
            top: 0;
            z-index: 1000;
            transition: all 0.3s ease;
            overflow-y: auto;
            overflow-x: hidden;
            display: flex;
            flex-direction: column;
        }

        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
        
        .sidebar-header {
            padding: 2rem 1.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            flex-shrink: 0;
        }

        .sidebar-nav {
            flex: 1;
            padding: 1rem 0;
            overflow-y: auto;
        }
        
        .sidebar-brand {
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
            text-decoration: none;
        }
        
        .nav-section {
            margin-bottom: 1.5rem;
        }

        .nav-section-title {
            color: rgba(255,255,255,0.6);
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            padding: 0.5rem 1rem;
            margin-bottom: 0.5rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            display: flex;
            align-items: center;
        }

        .nav-link {
            color: rgba(255,255,255,0.8);
            padding: 0.75rem 1rem 0.75rem 2rem;
            border-radius: 0 25px 25px 0;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            margin: 0.25rem 0 0.25rem 1rem;
            position: relative;
            font-size: 0.9rem;
        }

        .nav-link::before {
            content: '';
            position: absolute;
            left: -1rem;
            top: 50%;
            transform: translateY(-50%);
            width: 3px;
            height: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 2px;
            transition: height 0.3s ease;
        }

        .nav-link:hover,
        .nav-link.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-5px);
        }

        .nav-link:hover::before,
        .nav-link.active::before {
            height: 20px;
        }

        .nav-link.active {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.2) 0%, rgba(118, 75, 162, 0.2) 100%);
            border-left: 3px solid #667eea;
        }

        .nav-link i {
            margin-left: 0.75rem;
            width: 20px;
            text-align: center;
            font-size: 0.9rem;
        }

        .nav-section:last-child {
            margin-bottom: 2rem;
        }

        /* Ripple effect for nav links */
        .nav-link {
            position: relative;
            overflow: hidden;
        }

        .ripple {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.3);
            transform: scale(0);
            animation: ripple-animation 0.6s linear;
            pointer-events: none;
        }

        @keyframes ripple-animation {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        /* Smooth transitions for all interactive elements */
        .nav-link,
        .nav-section-title,
        .sidebar {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        /* Enhanced focus states for accessibility */
        .nav-link:focus {
            outline: 2px solid #667eea;
            outline-offset: 2px;
        }

        /* Sidebar collapse animation */
        .sidebar.collapsed {
            width: 60px;
        }

        .sidebar.collapsed .nav-section-title,
        .sidebar.collapsed .nav-link span {
            opacity: 0;
            visibility: hidden;
        }

        .sidebar.collapsed .nav-link {
            justify-content: center;
            padding: 0.75rem;
        }

        /* Loading state for nav items */
        .nav-link.loading {
            opacity: 0.6;
            pointer-events: none;
        }

        .nav-link.loading::after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 16px;
            height: 16px;
            margin: -8px 0 0 -8px;
            border: 2px solid transparent;
            border-top: 2px solid rgba(255, 255, 255, 0.8);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        /* Dropdown menu styles */
        .nav-dropdown {
            position: relative;
        }

        .nav-dropdown-toggle {
            display: flex;
            align-items: center;
            justify-content: space-between;
            cursor: pointer;
        }

        .nav-dropdown-toggle .dropdown-arrow {
            transition: transform 0.3s ease;
            font-size: 0.8rem;
        }

        .nav-dropdown.open .dropdown-arrow {
            transform: rotate(90deg);
        }

        .nav-dropdown-menu {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
            background: rgba(0, 0, 0, 0.2);
            margin: 0.25rem 0;
            border-radius: 8px;
        }

        .nav-dropdown.open .nav-dropdown-menu {
            max-height: 500px;
        }

        .nav-dropdown-item {
            color: rgba(255,255,255,0.7);
            padding: 0.5rem 1rem 0.5rem 3rem;
            border-radius: 6px;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            margin: 0.125rem 0.5rem;
            font-size: 0.85rem;
            position: relative;
        }

        .nav-dropdown-item::before {
            content: '';
            position: absolute;
            left: 1.5rem;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            background: rgba(255,255,255,0.4);
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .nav-dropdown-item:hover,
        .nav-dropdown-item.active {
            background: rgba(255,255,255,0.1);
            color: white;
            transform: translateX(-3px);
        }

        .nav-dropdown-item:hover::before,
        .nav-dropdown-item.active::before {
            background: #667eea;
            width: 8px;
            height: 8px;
        }

        .nav-dropdown-item i {
            margin-left: 0.5rem;
            width: 16px;
            text-align: center;
            font-size: 0.8rem;
        }

        /* Website Analytics Swiper Styles */
        .website-analytics-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 2rem;
            margin-bottom: 2rem;
            color: white;
            overflow: hidden;
            position: relative;
        }

        .website-analytics-card::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }

        .website-analytics-text-bg {
            background: rgba(255, 255, 255, 0.2);
            padding: 0.25rem 0.75rem;
            border-radius: 20px;
            font-weight: 600;
            backdrop-filter: blur(10px);
        }

        .analytics-swiper {
            padding: 1rem 0;
        }

        .analytics-swiper .swiper-slide {
            background: transparent;
        }

        .analytics-swiper .swiper-pagination-bullet {
            background: rgba(255, 255, 255, 0.5);
            opacity: 1;
        }

        .analytics-swiper .swiper-pagination-bullet-active {
            background: white;
        }

        .card-website-analytics-img {
            filter: drop-shadow(0 10px 20px rgba(0,0,0,0.2));
            animation: bounce 3s ease-in-out infinite;
        }

        @keyframes bounce {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .analytics-metric {
            transition: all 0.3s ease;
        }

        .analytics-metric:hover {
            transform: scale(1.05);
        }

        /* Responsive adjustments for analytics */
        @media (max-width: 768px) {
            .website-analytics-card {
                padding: 1.5rem;
                margin-bottom: 1.5rem;
            }

            .analytics-swiper {
                padding: 0.5rem 0;
            }

            .card-website-analytics-img {
                font-size: 4rem !important;
            }

            .website-analytics-text-bg {
                padding: 0.2rem 0.5rem;
                font-size: 0.9rem;
            }

            .analytics-metric p {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 480px) {
            .website-analytics-card {
                padding: 1rem;
            }

            .card-website-analytics-img {
                font-size: 3rem !important;
            }

            .analytics-metric {
                margin-bottom: 0.5rem !important;
            }
        }
        
        .main-content {
            margin-right: 280px;
            padding: 2rem;
            transition: all 0.3s ease;
        }
        
        .topbar {
            background: white;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e5e7eb;
            margin: -2rem -2rem 2rem -2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.05);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            box-shadow: 0 8px 25px rgba(0,0,0,0.1);
            transform: translateY(-2px);
        }
        
        .stat-card {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }
        
        .stat-card.success {
            background: linear-gradient(135deg, var(--success-color) 0%, #059669 100%);
        }
        
        .stat-card.warning {
            background: linear-gradient(135deg, var(--warning-color) 0%, #d97706 100%);
        }
        
        .stat-card.danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #dc2626 100%);
        }
        
        .stat-card.info {
            background: linear-gradient(135deg, var(--info-color) 0%, #0891b2 100%);
        }
        
        .btn {
            border-radius: 8px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            border: none;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(102, 126, 234, 0.3);
        }
        
        .quick-action-card {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 1.5rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        
        .quick-action-card:hover {
            border-color: var(--primary-color);
            transform: translateY(-2px);
            box-shadow: 0 8px 15px rgba(0,0,0,0.1);
        }
        
        .action-icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }
        
        .notification-container {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
        }
        
        .notification {
            background: white;
            border-radius: 8px;
            padding: 1rem 1.5rem;
            margin-bottom: 0.5rem;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            border-right: 4px solid;
            animation: slideIn 0.3s ease;
        }
        
        .notification.success { border-right-color: var(--success-color); }
        .notification.error { border-right-color: var(--danger-color); }
        .notification.warning { border-right-color: var(--warning-color); }
        .notification.info { border-right-color: var(--info-color); }
        
        @keyframes slideIn {
            from { transform: translateX(-100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }

        /* Support Tracker Styles */
        .support-tracker {
            position: relative;
            display: inline-block;
        }

        .support-percentage {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .support-stats .badge {
            width: 8px;
            height: 8px;
            padding: 0;
        }

        /* Profit Chart Styles */
        .profit-stat h3 {
            font-size: 2rem;
            font-weight: 700;
        }

        .profit-detail {
            padding: 0.5rem;
        }

        .profit-detail .badge {
            width: 8px;
            height: 8px;
            padding: 0;
        }

        /* Chart Container Styles */
        .chart-container {
            position: relative;
            height: 200px;
        }

        /* Support Tracker Animation */
        .support-tracker canvas {
            animation: rotateIn 1s ease-in-out;
        }

        @keyframes rotateIn {
            from {
                transform: rotate(-180deg);
                opacity: 0;
            }
            to {
                transform: rotate(0deg);
                opacity: 1;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(100%);
                width: 100%;
                max-width: 320px;
            }
            .sidebar.show {
                transform: translateX(0);
            }
            .main-content {
                margin-right: 0;
                padding: 1rem;
            }
            .main-wrapper {
                margin: 5px;
            }

            .profit-stat h3 {
                font-size: 1.5rem;
            }

            .support-percentage h2 {
                font-size: 1.5rem;
            }

            .nav-section-title {
                font-size: 0.7rem;
                padding: 0.4rem 1rem;
            }

            .nav-link {
                padding: 0.6rem 1rem 0.6rem 1.5rem;
                font-size: 0.85rem;
            }

            .sidebar-header {
                padding: 1.5rem 1rem;
            }

            .sidebar-brand {
                font-size: 1.3rem;
            }
        }

        @media (max-width: 480px) {
            .sidebar {
                width: 100%;
                max-width: none;
            }

            .topbar {
                padding: 0.75rem 1rem;
            }

            .main-content {
                padding: 0.75rem;
            }
        }
    </style>
</head>
<body>
    <div class="main-wrapper">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="#" class="sidebar-brand">
                    <i class="fas fa-crown me-2"></i>
                    لوحة التحكم
                </a>
            </div>
            
            <div class="sidebar-nav">
                <!-- لوحة التحكم الرئيسية -->
                <div class="nav-section">
                    <a href="#dashboard" class="nav-link active" onclick="showSection('dashboard')">
                        <i class="fas fa-tachometer-alt"></i>
                        لوحة التحكم
                    </a>
                    <a href="#system-status" class="nav-link" onclick="showSection('system-status')">
                        <i class="fas fa-heartbeat"></i>
                        حالة النظام
                    </a>
                </div>

                <!-- إدارة الملفات -->
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <div class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                            <div>
                                <i class="fas fa-folder"></i>
                                إدارة الملفات
                            </div>
                            <i class="fas fa-chevron-left dropdown-arrow"></i>
                        </div>
                        <div class="nav-dropdown-menu">
                            <a href="#files" class="nav-dropdown-item" onclick="showSection('files')">
                                <i class="fas fa-folder-open"></i>
                                مدير الملفات
                            </a>
                            <a href="#file-upload" class="nav-dropdown-item" onclick="showSection('file-upload')">
                                <i class="fas fa-upload"></i>
                                رفع الملفات
                            </a>
                            <a href="#storage-usage" class="nav-dropdown-item" onclick="showSection('storage-usage')">
                                <i class="fas fa-hdd"></i>
                                استخدام التخزين
                            </a>
                        </div>
                    </div>
                </div>

                <!-- إدارة المستخدمين -->
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <div class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                            <div>
                                <i class="fas fa-users"></i>
                                إدارة المستخدمين
                            </div>
                            <i class="fas fa-chevron-left dropdown-arrow"></i>
                        </div>
                        <div class="nav-dropdown-menu">
                            <a href="#users" class="nav-dropdown-item" onclick="showSection('users')">
                                <i class="fas fa-user"></i>
                                جميع المستخدمين
                            </a>
                            <a href="#user-roles" class="nav-dropdown-item" onclick="showSection('user-roles')">
                                <i class="fas fa-user-shield"></i>
                                الأدوار والصلاحيات
                            </a>
                            <a href="#user-activity" class="nav-dropdown-item" onclick="showSection('user-activity')">
                                <i class="fas fa-user-clock"></i>
                                نشاط المستخدمين
                            </a>
                            <a href="#user-import" class="nav-dropdown-item" onclick="showSection('user-import')">
                                <i class="fas fa-file-import"></i>
                                استيراد المستخدمين
                            </a>
                        </div>
                    </div>
                </div>

                <!-- التقارير والإحصائيات -->
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <div class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                            <div>
                                <i class="fas fa-chart-bar"></i>
                                التقارير والإحصائيات
                            </div>
                            <i class="fas fa-chevron-left dropdown-arrow"></i>
                        </div>
                        <div class="nav-dropdown-menu">
                            <a href="#reports" class="nav-dropdown-item" onclick="showSection('reports')">
                                <i class="fas fa-chart-line"></i>
                                التقارير العامة
                            </a>
                            <a href="#performance-metrics" class="nav-dropdown-item" onclick="showSection('performance-metrics')">
                                <i class="fas fa-tachometer-alt"></i>
                                مقاييس الأداء
                            </a>
                            <a href="#export-reports" class="nav-dropdown-item" onclick="showSection('export-reports')">
                                <i class="fas fa-file-export"></i>
                                تصدير التقارير
                            </a>
                        </div>
                    </div>
                </div>

                <!-- سجل النشاط -->
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <div class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                            <div>
                                <i class="fas fa-history"></i>
                                سجل النشاط
                            </div>
                            <i class="fas fa-chevron-left dropdown-arrow"></i>
                        </div>
                        <div class="nav-dropdown-menu">
                            <a href="#activity-log" class="nav-dropdown-item" onclick="showSection('activity-log')">
                                <i class="fas fa-list-alt"></i>
                                سجل الأنشطة
                            </a>
                            <a href="#recent-activity" class="nav-dropdown-item" onclick="showSection('recent-activity')">
                                <i class="fas fa-clock"></i>
                                النشاط الأخير
                            </a>
                            <a href="#activity-export" class="nav-dropdown-item" onclick="showSection('activity-export')">
                                <i class="fas fa-download"></i>
                                تصدير السجلات
                            </a>
                        </div>
                    </div>
                </div>

                <!-- النسخ الاحتياطية -->
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <div class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                            <div>
                                <i class="fas fa-database"></i>
                                النسخ الاحتياطية
                            </div>
                            <i class="fas fa-chevron-left dropdown-arrow"></i>
                        </div>
                        <div class="nav-dropdown-menu">
                            <a href="#backups" class="nav-dropdown-item" onclick="showSection('backups')">
                                <i class="fas fa-save"></i>
                                إدارة النسخ الاحتياطية
                            </a>
                            <a href="#backup-schedule" class="nav-dropdown-item" onclick="showSection('backup-schedule')">
                                <i class="fas fa-calendar-alt"></i>
                                جدولة النسخ
                            </a>
                            <a href="#backup-restore" class="nav-dropdown-item" onclick="showSection('backup-restore')">
                                <i class="fas fa-undo"></i>
                                استعادة النسخ
                            </a>
                        </div>
                    </div>
                </div>

                <!-- إدارة الإشعارات -->
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <div class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                            <div>
                                <i class="fas fa-bell"></i>
                                إدارة الإشعارات
                            </div>
                            <i class="fas fa-chevron-left dropdown-arrow"></i>
                        </div>
                        <div class="nav-dropdown-menu">
                            <a href="#notifications" class="nav-dropdown-item" onclick="showSection('notifications')">
                                <i class="fas fa-envelope"></i>
                                جميع الإشعارات
                            </a>
                            <a href="#notification-templates" class="nav-dropdown-item" onclick="showSection('notification-templates')">
                                <i class="fas fa-file-alt"></i>
                                قوالب الإشعارات
                            </a>
                            <a href="#broadcast-notifications" class="nav-dropdown-item" onclick="showSection('broadcast-notifications')">
                                <i class="fas fa-broadcast-tower"></i>
                                الإشعارات المباشرة
                            </a>
                        </div>
                    </div>
                </div>

                <!-- إعدادات النظام -->
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <div class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                            <div>
                                <i class="fas fa-cogs"></i>
                                إعدادات النظام
                            </div>
                            <i class="fas fa-chevron-left dropdown-arrow"></i>
                        </div>
                        <div class="nav-dropdown-menu">
                            <a href="#settings" class="nav-dropdown-item" onclick="showSection('settings')">
                                <i class="fas fa-cog"></i>
                                الإعدادات العامة
                            </a>
                            <a href="#email-settings" class="nav-dropdown-item" onclick="showSection('email-settings')">
                                <i class="fas fa-envelope-open"></i>
                                إعدادات البريد
                            </a>
                            <a href="#cache-management" class="nav-dropdown-item" onclick="showSection('cache-management')">
                                <i class="fas fa-memory"></i>
                                إدارة الذاكرة المؤقتة
                            </a>
                        </div>
                    </div>
                </div>

                <!-- الأمان والحماية -->
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <div class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                            <div>
                                <i class="fas fa-shield-alt"></i>
                                الأمان والحماية
                            </div>
                            <i class="fas fa-chevron-left dropdown-arrow"></i>
                        </div>
                        <div class="nav-dropdown-menu">
                            <a href="#security" class="nav-dropdown-item" onclick="showSection('security')">
                                <i class="fas fa-lock"></i>
                                إعدادات الأمان
                            </a>
                            <a href="#failed-logins" class="nav-dropdown-item" onclick="showSection('failed-logins')">
                                <i class="fas fa-exclamation-triangle"></i>
                                محاولات الدخول الفاشلة
                            </a>
                            <a href="#blocked-ips" class="nav-dropdown-item" onclick="showSection('blocked-ips')">
                                <i class="fas fa-ban"></i>
                                عناوين IP المحظورة
                            </a>
                            <a href="#security-audit" class="nav-dropdown-item" onclick="showSection('security-audit')">
                                <i class="fas fa-search"></i>
                                تدقيق الأمان
                            </a>
                        </div>
                    </div>
                </div>

                <!-- صيانة وأدوات النظام -->
                <div class="nav-section">
                    <div class="nav-dropdown">
                        <div class="nav-link nav-dropdown-toggle" onclick="toggleDropdown(this)">
                            <div>
                                <i class="fas fa-tools"></i>
                                صيانة وأدوات النظام
                            </div>
                            <i class="fas fa-chevron-left dropdown-arrow"></i>
                        </div>
                        <div class="nav-dropdown-menu">
                            <a href="#maintenance" class="nav-dropdown-item" onclick="showSection('maintenance')">
                                <i class="fas fa-wrench"></i>
                                وضع الصيانة
                            </a>
                            <a href="#health-check" class="nav-dropdown-item" onclick="showSection('health-check')">
                                <i class="fas fa-stethoscope"></i>
                                فحص صحة النظام
                            </a>
                            <a href="#database-optimization" class="nav-dropdown-item" onclick="showSection('database-optimization')">
                                <i class="fas fa-database"></i>
                                تحسين قاعدة البيانات
                            </a>
                            <a href="#system-tools" class="nav-dropdown-item" onclick="showSection('system-tools')">
                                <i class="fas fa-terminal"></i>
                                أدوات النظام
                            </a>
                            <a href="#artisan-commands" class="nav-dropdown-item" onclick="showSection('artisan-commands')">
                                <i class="fas fa-code"></i>
                                أوامر Artisan
                            </a>
                            <a href="#server-info" class="nav-dropdown-item" onclick="showSection('server-info')">
                                <i class="fas fa-server"></i>
                                معلومات الخادم
                            </a>
                            <a href="#phpinfo" class="nav-dropdown-item" onclick="showSection('phpinfo')">
                                <i class="fab fa-php"></i>
                                معلومات PHP
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </nav>
        
        <!-- Main Content -->
        <main class="main-content">
            <!-- Top Bar -->
            <div class="topbar">
                <div>
                    <h1 id="pageTitle">لوحة التحكم الرئيسية</h1>
                    <small class="text-muted">مرحباً بك في نظام الإدارة المتطور</small>
                </div>
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-outline-secondary btn-sm" onclick="toggleDarkMode()" id="darkModeToggle" title="تبديل الوضع المظلم">
                        <i class="fas fa-moon" id="darkModeIcon"></i>
                    </button>
                    <button class="btn btn-outline-primary btn-sm d-md-none" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <div class="dropdown">
                        <button class="btn btn-primary btn-sm dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-2"></i>
                            المدير
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#"><i class="fas fa-user me-2"></i>الملف الشخصي</a></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-cog me-2"></i>الإعدادات</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#"><i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج</a></li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <!-- Dashboard Section -->
            <div id="dashboard" class="content-section">
                <!-- Website Analytics Swiper -->
                <div class="website-analytics-card">
                    <div class="swiper analytics-swiper">
                        <div class="swiper-wrapper">
                            <!-- Slide 1: تحليلات الموقع -->
                            <div class="swiper-slide">
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="text-white mb-0">تحليلات الموقع</h5>
                                        <small>إجمالي معدل التحويل 28.5%</small>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-lg-7 col-md-9 col-12 order-2 order-md-1">
                                            <h6 class="text-white mt-0 mt-md-3 mb-4">حركة المرور</h6>
                                            <div class="row">
                                                <div class="col-6">
                                                    <ul class="list-unstyled mb-0">
                                                        <li class="d-flex mb-4 align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">28%</p>
                                                            <p class="mb-0">الجلسات</p>
                                                        </li>
                                                        <li class="d-flex align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">1.2 ألف</p>
                                                            <p class="mb-0">العملاء المحتملين</p>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="col-6">
                                                    <ul class="list-unstyled mb-0">
                                                        <li class="d-flex mb-4 align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">3.1 ألف</p>
                                                            <p class="mb-0">مشاهدات الصفحة</p>
                                                        </li>
                                                        <li class="d-flex align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">12%</p>
                                                            <p class="mb-0">التحويلات</p>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-5 col-md-3 col-12 order-1 order-md-2 my-4 my-md-0 text-center">
                                            <i class="fas fa-chart-line fa-8x card-website-analytics-img" style="color: rgba(255,255,255,0.8);"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Slide 2: إحصائيات المبيعات -->
                            <div class="swiper-slide">
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="text-white mb-0">إحصائيات المبيعات</h5>
                                        <small>نمو شهري بنسبة 15.3%</small>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-lg-7 col-md-9 col-12 order-2 order-md-1">
                                            <h6 class="text-white mt-0 mt-md-3 mb-4">الأداء المالي</h6>
                                            <div class="row">
                                                <div class="col-6">
                                                    <ul class="list-unstyled mb-0">
                                                        <li class="d-flex mb-4 align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">$24.5k</p>
                                                            <p class="mb-0">إجمالي المبيعات</p>
                                                        </li>
                                                        <li class="d-flex align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">156</p>
                                                            <p class="mb-0">الطلبات الجديدة</p>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="col-6">
                                                    <ul class="list-unstyled mb-0">
                                                        <li class="d-flex mb-4 align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">89%</p>
                                                            <p class="mb-0">رضا العملاء</p>
                                                        </li>
                                                        <li class="d-flex align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">+23%</p>
                                                            <p class="mb-0">النمو الشهري</p>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-5 col-md-3 col-12 order-1 order-md-2 my-4 my-md-0 text-center">
                                            <i class="fas fa-chart-pie fa-8x card-website-analytics-img" style="color: rgba(255,255,255,0.8);"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Slide 3: أداء النظام -->
                            <div class="swiper-slide">
                                <div class="row">
                                    <div class="col-12">
                                        <h5 class="text-white mb-0">أداء النظام</h5>
                                        <small>حالة ممتازة - 99.9% وقت التشغيل</small>
                                    </div>
                                    <div class="row mt-3">
                                        <div class="col-lg-7 col-md-9 col-12 order-2 order-md-1">
                                            <h6 class="text-white mt-0 mt-md-3 mb-4">مؤشرات الأداء</h6>
                                            <div class="row">
                                                <div class="col-6">
                                                    <ul class="list-unstyled mb-0">
                                                        <li class="d-flex mb-4 align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">1.2s</p>
                                                            <p class="mb-0">زمن التحميل</p>
                                                        </li>
                                                        <li class="d-flex align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">45GB</p>
                                                            <p class="mb-0">مساحة متاحة</p>
                                                        </li>
                                                    </ul>
                                                </div>
                                                <div class="col-6">
                                                    <ul class="list-unstyled mb-0">
                                                        <li class="d-flex mb-4 align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">99.9%</p>
                                                            <p class="mb-0">وقت التشغيل</p>
                                                        </li>
                                                        <li class="d-flex align-items-center analytics-metric">
                                                            <p class="mb-0 fw-medium me-2 website-analytics-text-bg">2.1GB</p>
                                                            <p class="mb-0">استخدام الذاكرة</p>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-5 col-md-3 col-12 order-1 order-md-2 my-4 my-md-0 text-center">
                                            <i class="fas fa-server fa-8x card-website-analytics-img" style="color: rgba(255,255,255,0.8);"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="swiper-pagination"></div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">إجمالي المستخدمين</h6>
                                        <h3 class="mb-0" id="totalUsers">1,247</h3>
                                        <small class="text-white-50">+12% من الشهر الماضي</small>
                                    </div>
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">المبيعات اليوم</h6>
                                        <h3 class="mb-0" id="todaySales">$12,450</h3>
                                        <small class="text-white-50">+8% من أمس</small>
                                    </div>
                                    <i class="fas fa-dollar-sign fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">الطلبات المعلقة</h6>
                                        <h3 class="mb-0" id="pendingOrders">23</h3>
                                        <small class="text-white-50">تحتاج مراجعة</small>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card danger">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">التنبيهات</h6>
                                        <h3 class="mb-0" id="alerts">5</h3>
                                        <small class="text-white-50">تحتاج انتباه</small>
                                    </div>
                                    <i class="fas fa-exclamation-triangle fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-bolt text-warning me-2"></i>
                                    إجراءات سريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <div class="quick-action-card" onclick="clearCache()">
                                            <div class="action-icon">
                                                <i class="fas fa-broom"></i>
                                            </div>
                                            <h6>مسح الذاكرة المؤقتة</h6>
                                            <small class="text-muted">تحسين الأداء</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="quick-action-card" onclick="createBackup()">
                                            <div class="action-icon">
                                                <i class="fas fa-download"></i>
                                            </div>
                                            <h6>نسخة احتياطية</h6>
                                            <small class="text-muted">حماية البيانات</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="quick-action-card" onclick="sendNotification()">
                                            <div class="action-icon">
                                                <i class="fas fa-paper-plane"></i>
                                            </div>
                                            <h6>إرسال إشعار</h6>
                                            <small class="text-muted">تواصل مع المستخدمين</small>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="quick-action-card" onclick="viewReports()">
                                            <div class="action-icon">
                                                <i class="fas fa-chart-line"></i>
                                            </div>
                                            <h6>عرض التقارير</h6>
                                            <small class="text-muted">تحليل البيانات</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Charts Row 1 -->
                <div class="row mb-4">
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">تقارير الأرباح</h5>
                                <small class="text-muted">نظرة عامة على الأرباح الأسبوعية</small>
                            </div>
                            <div class="card-body">
                                <div class="row mb-3">
                                    <div class="col-md-4 text-center">
                                        <div class="profit-stat">
                                            <h3 class="text-primary mb-1">468 دولاراً</h3>
                                            <small class="text-success">+4.2%</small>
                                            <div class="text-muted small">لقد ارتفعت عن هذا الأسبوع مقارنة بالأسبوع الماضي</div>
                                        </div>
                                    </div>
                                    <div class="col-md-8">
                                        <canvas id="profitChart" height="80"></canvas>
                                    </div>
                                </div>
                                <div class="row text-center">
                                    <div class="col-4">
                                        <div class="profit-detail">
                                            <div class="d-flex align-items-center justify-content-center mb-1">
                                                <span class="badge bg-danger rounded-circle me-2" style="width: 8px; height: 8px;"></span>
                                                <small class="text-muted">النفقات</small>
                                            </div>
                                            <h6 class="mb-0">74.19 دولاراً</h6>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="profit-detail">
                                            <div class="d-flex align-items-center justify-content-center mb-1">
                                                <span class="badge bg-info rounded-circle me-2" style="width: 8px; height: 8px;"></span>
                                                <small class="text-muted">ربح</small>
                                            </div>
                                            <h6 class="mb-0">256.34 دولاراً</h6>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="profit-detail">
                                            <div class="d-flex align-items-center justify-content-center mb-1">
                                                <span class="badge bg-secondary rounded-circle me-2" style="width: 8px; height: 8px;"></span>
                                                <small class="text-muted">الأرباح</small>
                                            </div>
                                            <h6 class="mb-0">545.69 دولاراً</h6>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5 class="card-title mb-0">متتبع الدعم</h5>
                                <small class="text-muted">آخر 7 أيام</small>
                            </div>
                            <div class="card-body text-center">
                                <div class="support-tracker mb-4">
                                    <canvas id="supportChart" width="200" height="200"></canvas>
                                    <div class="support-percentage">
                                        <h2 class="text-primary mb-0">85%</h2>
                                        <small class="text-muted">Completed Task</small>
                                    </div>
                                </div>
                                <div class="support-stats">
                                    <div class="row text-center">
                                        <div class="col-12 mb-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary rounded-circle me-2" style="width: 8px; height: 8px;"></span>
                                                    <small>تذاكر جديدة</small>
                                                </div>
                                                <div>
                                                    <strong>142</strong>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12 mb-3">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-success rounded-circle me-2" style="width: 8px; height: 8px;"></span>
                                                    <small>تذاكر مفتوحة</small>
                                                </div>
                                                <div>
                                                    <strong>28</strong>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-12">
                                            <div class="d-flex justify-content-between align-items-center">
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-warning rounded-circle me-2" style="width: 8px; height: 8px;"></span>
                                                    <small>وقت الاستجابة</small>
                                                </div>
                                                <div>
                                                    <strong>يوم واحد</strong>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-4">
                                    <h4 class="text-primary mb-1">164</h4>
                                    <small class="text-muted">إجمالي التذاكر</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Row 2 -->
                <div class="row">
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">إحصائيات المبيعات</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="salesChart" height="100"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">توزيع المستخدمين</h5>
                            </div>
                            <div class="card-body">
                                <canvas id="usersChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">النشاط الأخير</h5>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item border-0 px-0">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-user-plus text-success"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">مستخدم جديد</h6>
                                                <small class="text-muted">انضم أحمد محمد للنظام</small>
                                            </div>
                                            <small class="text-muted">منذ 5 دقائق</small>
                                        </div>
                                    </div>
                                    <div class="list-group-item border-0 px-0">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-shopping-cart text-primary"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">طلب جديد</h6>
                                                <small class="text-muted">طلب #1234 بقيمة $250</small>
                                            </div>
                                            <small class="text-muted">منذ 15 دقيقة</small>
                                        </div>
                                    </div>
                                    <div class="list-group-item border-0 px-0">
                                        <div class="d-flex align-items-center">
                                            <div class="flex-shrink-0">
                                                <i class="fas fa-cog text-warning"></i>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h6 class="mb-1">تحديث النظام</h6>
                                                <small class="text-muted">تم تحديث الإعدادات</small>
                                            </div>
                                            <small class="text-muted">منذ 30 دقيقة</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">حالة النظام</h5>
                            </div>
                            <div class="card-body">
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="text-center">
                                            <div class="h4 text-success mb-1">99.9%</div>
                                            <small class="text-muted">وقت التشغيل</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center">
                                            <div class="h4 text-info mb-1">2.1GB</div>
                                            <small class="text-muted">استخدام الذاكرة</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center">
                                            <div class="h4 text-warning mb-1">45%</div>
                                            <small class="text-muted">استخدام المعالج</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="text-center">
                                            <div class="h4 text-primary mb-1">156GB</div>
                                            <small class="text-muted">مساحة التخزين</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Other sections will be added here -->
            <!-- Settings Section -->
            <div id="settings" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">الإعدادات العامة</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">اسم الموقع</label>
                                    <input type="text" class="form-control" value="موقعي الرائع">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">البريد الإلكتروني</label>
                                    <input type="email" class="form-control" value="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">رقم الهاتف</label>
                                    <input type="tel" class="form-control" value="+966501234567">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">المنطقة الزمنية</label>
                                    <select class="form-select">
                                        <option>Asia/Riyadh</option>
                                        <option>Asia/Dubai</option>
                                        <option>Europe/London</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">اللغة الافتراضية</label>
                                    <select class="form-select">
                                        <option>العربية</option>
                                        <option>English</option>
                                        <option>Français</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="maintenance" checked>
                                        <label class="form-check-label" for="maintenance">
                                            تفعيل وضع الصيانة
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button class="btn btn-primary" onclick="saveSettings()">حفظ الإعدادات</button>
                    </div>
                </div>
            </div>

            <!-- Users Management Section -->
            <div id="users" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">إدارة المستخدمين</h5>
                        <button class="btn btn-primary btn-sm">
                            <i class="fas fa-plus me-2"></i>إضافة مستخدم جديد
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>الاسم</th>
                                        <th>البريد الإلكتروني</th>
                                        <th>الدور</th>
                                        <th>تاريخ التسجيل</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>أحمد محمد</td>
                                        <td><EMAIL></td>
                                        <td><span class="badge bg-primary">مدير</span></td>
                                        <td>2024-01-15</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">تعديل</button>
                                            <button class="btn btn-sm btn-outline-danger">حذف</button>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td>فاطمة علي</td>
                                        <td><EMAIL></td>
                                        <td><span class="badge bg-secondary">محرر</span></td>
                                        <td>2024-02-10</td>
                                        <td><span class="badge bg-success">نشط</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">تعديل</button>
                                            <button class="btn btn-sm btn-outline-danger">حذف</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Posts Management Section -->
            <div id="posts" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">إدارة المقالات والصفحات</h5>
                        <div>
                            <button class="btn btn-success btn-sm me-2">
                                <i class="fas fa-plus me-2"></i>مقال جديد
                            </button>
                            <button class="btn btn-primary btn-sm">
                                <i class="fas fa-file me-2"></i>صفحة جديدة
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="البحث في المقالات...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select">
                                    <option>جميع التصنيفات</option>
                                    <option>أخبار</option>
                                    <option>مقالات</option>
                                    <option>دروس</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select">
                                    <option>جميع الحالات</option>
                                    <option>منشور</option>
                                    <option>مسودة</option>
                                    <option>معلق</option>
                                </select>
                            </div>
                        </div>
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>العنوان</th>
                                        <th>الكاتب</th>
                                        <th>التصنيف</th>
                                        <th>تاريخ النشر</th>
                                        <th>الحالة</th>
                                        <th>الإجراءات</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>مقدمة في تطوير المواقع</td>
                                        <td>أحمد محمد</td>
                                        <td>دروس</td>
                                        <td>2024-03-15</td>
                                        <td><span class="badge bg-success">منشور</span></td>
                                        <td>
                                            <button class="btn btn-sm btn-outline-primary">تعديل</button>
                                            <button class="btn btn-sm btn-outline-info">عرض</button>
                                            <button class="btn btn-sm btn-outline-danger">حذف</button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Analytics Section -->
            <div id="analytics" class="content-section" style="display: none;">
                <div class="row">
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card info">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">زوار اليوم</h6>
                                        <h3 class="mb-0">2,847</h3>
                                        <small class="text-white-50">+15% من أمس</small>
                                    </div>
                                    <i class="fas fa-users fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card success">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">مشاهدات الصفحات</h6>
                                        <h3 class="mb-0">12,450</h3>
                                        <small class="text-white-50">+8% من أمس</small>
                                    </div>
                                    <i class="fas fa-eye fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card warning">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">معدل الارتداد</h6>
                                        <h3 class="mb-0">23%</h3>
                                        <small class="text-white-50">-5% تحسن</small>
                                    </div>
                                    <i class="fas fa-chart-line fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-4">
                        <div class="card stat-card">
                            <div class="card-body">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <h6 class="card-title text-white-50 mb-1">متوسط الجلسة</h6>
                                        <h3 class="mb-0">4:32</h3>
                                        <small class="text-white-50">+12% من أمس</small>
                                    </div>
                                    <i class="fas fa-clock fa-2x opacity-75"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">تحليلات الموقع التفصيلية</h5>
                    </div>
                    <div class="card-body">
                        <canvas id="analyticsChart" height="100"></canvas>
                    </div>
                </div>
            </div>
            
            <!-- Media Library Section -->
            <div id="media" class="content-section" style="display: none;">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">مكتبة الوسائط</h5>
                        <button class="btn btn-primary btn-sm" onclick="uploadMedia()">
                            <i class="fas fa-upload me-2"></i>رفع ملفات جديدة
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <input type="text" class="form-control" placeholder="البحث في الملفات...">
                            </div>
                            <div class="col-md-3">
                                <select class="form-select">
                                    <option>جميع الأنواع</option>
                                    <option>صور</option>
                                    <option>فيديو</option>
                                    <option>مستندات</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-2 mb-3">
                                <div class="card">
                                    <img src="https://via.placeholder.com/150x100" class="card-img-top" alt="صورة">
                                    <div class="card-body p-2">
                                        <small class="text-muted">image1.jpg</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <div class="card">
                                    <img src="https://via.placeholder.com/150x100" class="card-img-top" alt="صورة">
                                    <div class="card-body p-2">
                                        <small class="text-muted">image2.jpg</small>
                                    </div>
                                </div>
                            </div>
                            <!-- المزيد من الصور -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Security Section -->
            <div id="security" class="content-section" style="display: none;">
                <div class="row">
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">إعدادات الأمان</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="twoFactor" checked>
                                        <label class="form-check-label" for="twoFactor">
                                            تفعيل المصادقة الثنائية
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="loginNotifications" checked>
                                        <label class="form-check-label" for="loginNotifications">
                                            إشعارات تسجيل الدخول
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">مدة انتهاء الجلسة (بالدقائق)</label>
                                    <input type="number" class="form-control" value="30">
                                </div>
                                <button class="btn btn-primary">حفظ إعدادات الأمان</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">محاولات تسجيل الدخول الأخيرة</h5>
                            </div>
                            <div class="card-body">
                                <div class="list-group list-group-flush">
                                    <div class="list-group-item border-0 px-0">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>192.168.1.100</strong>
                                                <br><small class="text-muted">Chrome - Windows</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-success">نجح</span>
                                                <br><small class="text-muted">منذ 5 دقائق</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="list-group-item border-0 px-0">
                                        <div class="d-flex justify-content-between">
                                            <div>
                                                <strong>192.168.1.105</strong>
                                                <br><small class="text-muted">Firefox - Mac</small>
                                            </div>
                                            <div class="text-end">
                                                <span class="badge bg-danger">فشل</span>
                                                <br><small class="text-muted">منذ ساعة</small>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Maintenance Section -->
            <div id="maintenance" class="content-section" style="display: none;">
                <div class="row">
                    <div class="col-md-4 mb-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-broom fa-3x text-primary mb-3"></i>
                                <h5>تنظيف الملفات المؤقتة</h5>
                                <p class="text-muted">مسح الملفات المؤقتة وتحسين الأداء</p>
                                <button class="btn btn-primary" onclick="clearTempFiles()">تنظيف الآن</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-database fa-3x text-success mb-3"></i>
                                <h5>تحسين قاعدة البيانات</h5>
                                <p class="text-muted">تحسين وضغط جداول قاعدة البيانات</p>
                                <button class="btn btn-success" onclick="optimizeDatabase()">تحسين الآن</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="fas fa-sync-alt fa-3x text-warning mb-3"></i>
                                <h5>إعادة تشغيل النظام</h5>
                                <p class="text-muted">إعادة تشغيل الخدمات والتطبيق</p>
                                <button class="btn btn-warning" onclick="restartSystem()">إعادة التشغيل</button>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">معلومات النظام</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>إصدار PHP:</strong></td>
                                        <td>8.2.12</td>
                                    </tr>
                                    <tr>
                                        <td><strong>إصدار MySQL:</strong></td>
                                        <td>8.0.35</td>
                                    </tr>
                                    <tr>
                                        <td><strong>مساحة القرص المتاحة:</strong></td>
                                        <td>45.2 GB</td>
                                    </tr>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <table class="table table-borderless">
                                    <tr>
                                        <td><strong>استخدام الذاكرة:</strong></td>
                                        <td>2.1 GB / 8 GB</td>
                                    </tr>
                                    <tr>
                                        <td><strong>وقت التشغيل:</strong></td>
                                        <td>15 يوم، 4 ساعات</td>
                                    </tr>
                                    <tr>
                                        <td><strong>آخر نسخة احتياطية:</strong></td>
                                        <td>2024-03-15 14:30</td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Database Management Section -->
            <div id="database" class="content-section" style="display: none;">
                <div class="row">
                    <div class="col-md-8 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">جداول قاعدة البيانات</h5>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>اسم الجدول</th>
                                                <th>عدد الصفوف</th>
                                                <th>الحجم</th>
                                                <th>آخر تحديث</th>
                                                <th>الإجراءات</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>users</td>
                                                <td>1,247</td>
                                                <td>2.3 MB</td>
                                                <td>2024-03-15</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary">تحسين</button>
                                                    <button class="btn btn-sm btn-outline-info">عرض</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>posts</td>
                                                <td>856</td>
                                                <td>5.7 MB</td>
                                                <td>2024-03-14</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary">تحسين</button>
                                                    <button class="btn btn-sm btn-outline-info">عرض</button>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td>comments</td>
                                                <td>3,421</td>
                                                <td>1.8 MB</td>
                                                <td>2024-03-15</td>
                                                <td>
                                                    <button class="btn btn-sm btn-outline-primary">تحسين</button>
                                                    <button class="btn btn-sm btn-outline-info">عرض</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="card-title mb-0">إحصائيات قاعدة البيانات</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>إجمالي الجداول:</span>
                                        <strong>24</strong>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>إجمالي الصفوف:</span>
                                        <strong>15,847</strong>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex justify-content-between">
                                        <span>حجم قاعدة البيانات:</span>
                                        <strong>45.2 MB</strong>
                                    </div>
                                </div>
                                <hr>
                                <button class="btn btn-primary btn-sm w-100 mb-2" onclick="backupDatabase()">
                                    <i class="fas fa-download me-2"></i>نسخ احتياطي
                                </button>
                                <button class="btn btn-success btn-sm w-100 mb-2" onclick="optimizeAllTables()">
                                    <i class="fas fa-tools me-2"></i>تحسين الكل
                                </button>
                                <button class="btn btn-warning btn-sm w-100" onclick="repairDatabase()">
                                    <i class="fas fa-wrench me-2"></i>إصلاح
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@10/swiper-bundle.min.js"></script>
    <script>
        // Navigation functionality
        function showSection(sectionId) {
            // Hide all sections
            document.querySelectorAll('.content-section').forEach(section => {
                section.style.display = 'none';
            });

            // Remove active class from all nav links and dropdown items
            document.querySelectorAll('.nav-link, .nav-dropdown-item').forEach(link => {
                link.classList.remove('active');
            });

            // Show selected section
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.style.display = 'block';
            }

            // Add active class to clicked nav link or dropdown item
            const activeLink = document.querySelector(`[onclick="showSection('${sectionId}')"]`);
            if (activeLink) {
                activeLink.classList.add('active');

                // If it's a dropdown item, ensure its parent dropdown is open
                const parentDropdown = activeLink.closest('.nav-dropdown');
                if (parentDropdown) {
                    parentDropdown.classList.add('open');
                }
            }

            // Update page title
            const titles = {
                'dashboard': 'لوحة التحكم الرئيسية',
                'system-status': 'حالة النظام',
                'files': 'مدير الملفات',
                'file-upload': 'رفع الملفات',
                'storage-usage': 'استخدام التخزين',
                'users': 'إدارة المستخدمين',
                'user-roles': 'الأدوار والصلاحيات',
                'user-activity': 'نشاط المستخدمين',
                'user-import': 'استيراد المستخدمين',
                'reports': 'التقارير العامة',
                'performance-metrics': 'مقاييس الأداء',
                'export-reports': 'تصدير التقارير',
                'activity-log': 'سجل الأنشطة',
                'recent-activity': 'النشاط الأخير',
                'activity-export': 'تصدير السجلات',
                'backups': 'إدارة النسخ الاحتياطية',
                'backup-schedule': 'جدولة النسخ الاحتياطية',
                'backup-restore': 'استعادة النسخ الاحتياطية',
                'notifications': 'إدارة الإشعارات',
                'notification-templates': 'قوالب الإشعارات',
                'broadcast-notifications': 'الإشعارات المباشرة',
                'settings': 'الإعدادات العامة',
                'email-settings': 'إعدادات البريد الإلكتروني',
                'cache-management': 'إدارة الذاكرة المؤقتة',
                'security': 'إعدادات الأمان',
                'failed-logins': 'محاولات الدخول الفاشلة',
                'blocked-ips': 'عناوين IP المحظورة',
                'security-audit': 'تدقيق الأمان',
                'maintenance': 'وضع الصيانة',
                'health-check': 'فحص صحة النظام',
                'database-optimization': 'تحسين قاعدة البيانات',
                'system-tools': 'أدوات النظام',
                'artisan-commands': 'أوامر Artisan',
                'server-info': 'معلومات الخادم',
                'phpinfo': 'معلومات PHP'
            };
            document.getElementById('pageTitle').textContent = titles[sectionId] || 'لوحة التحكم';

            // Auto-hide sidebar on mobile after selection
            if (window.innerWidth <= 768) {
                setTimeout(() => {
                    const sidebar = document.getElementById('sidebar');
                    if (sidebar) {
                        sidebar.classList.remove('show');
                    }
                }, 300);
            }
        }
        
        // Sidebar toggle for mobile
        function toggleSidebar() {
            document.getElementById('sidebar').classList.toggle('show');
        }

        // Dark Mode Toggle
        function toggleDarkMode() {
            const body = document.body;
            const darkModeIcon = document.getElementById('darkModeIcon');
            const isDarkMode = body.classList.contains('dark-mode');

            if (isDarkMode) {
                body.classList.remove('dark-mode');
                darkModeIcon.className = 'fas fa-moon';
                localStorage.setItem('darkMode', 'false');
                showNotification('تم تفعيل الوضع العادي', 'info');
            } else {
                body.classList.add('dark-mode');
                darkModeIcon.className = 'fas fa-sun';
                localStorage.setItem('darkMode', 'true');
                showNotification('تم تفعيل الوضع المظلم', 'info');
            }
        }

        // Initialize Dark Mode from localStorage
        function initializeDarkMode() {
            const savedDarkMode = localStorage.getItem('darkMode');
            const darkModeIcon = document.getElementById('darkModeIcon');

            if (savedDarkMode === 'true') {
                document.body.classList.add('dark-mode');
                darkModeIcon.className = 'fas fa-sun';
            } else {
                document.body.classList.remove('dark-mode');
                darkModeIcon.className = 'fas fa-moon';
            }
        }
        
        // Enhanced notification system with SweetAlert2
        function showNotification(message, type = 'info', duration = 3000) {
            const icons = {
                'success': 'success',
                'error': 'error',
                'warning': 'warning',
                'info': 'info'
            };

            const colors = {
                'success': '#10b981',
                'error': '#ef4444',
                'warning': '#f59e0b',
                'info': '#06b6d4'
            };

            Swal.fire({
                icon: icons[type] || 'info',
                title: message,
                toast: true,
                position: 'top-end',
                showConfirmButton: false,
                timer: duration,
                timerProgressBar: true,
                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937',
                iconColor: colors[type] || '#06b6d4',
                didOpen: (toast) => {
                    toast.addEventListener('mouseenter', Swal.stopTimer)
                    toast.addEventListener('mouseleave', Swal.resumeTimer)
                }
            });
        }
        
        // Enhanced quick actions with SweetAlert2
        function clearCache() {
            Swal.fire({
                title: 'مسح الذاكرة المؤقتة',
                text: 'هل أنت متأكد من رغبتك في مسح الذاكرة المؤقتة؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، امسح',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#6b7280',
                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
            }).then((result) => {
                if (result.isConfirmed) {
                    // Show loading
                    Swal.fire({
                        title: 'جاري المسح...',
                        html: 'يرجى الانتظار أثناء مسح الذاكرة المؤقتة',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading()
                        },
                        background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                        color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                    });

                    setTimeout(() => {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم بنجاح!',
                            text: 'تم مسح الذاكرة المؤقتة بنجاح',
                            timer: 2000,
                            showConfirmButton: false,
                            background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                            color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                        });
                    }, 2000);
                }
            });
        }
        
        function createBackup() {
            Swal.fire({
                title: 'إنشاء نسخة احتياطية',
                text: 'هل تريد إنشاء نسخة احتياطية جديدة من البيانات؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، أنشئ النسخة',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#10b981',
                cancelButtonColor: '#6b7280',
                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
            }).then((result) => {
                if (result.isConfirmed) {
                    let timerInterval;
                    Swal.fire({
                        title: 'جاري إنشاء النسخة الاحتياطية',
                        html: 'سيتم الانتهاء خلال <b></b> ثانية',
                        timer: 3000,
                        timerProgressBar: true,
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                            const b = Swal.getHtmlContainer().querySelector('b');
                            timerInterval = setInterval(() => {
                                b.textContent = Math.ceil(Swal.getTimerLeft() / 1000);
                            }, 100);
                        },
                        willClose: () => {
                            clearInterval(timerInterval);
                        },
                        background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                        color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                    }).then((result) => {
                        if (result.dismiss === Swal.DismissReason.timer) {
                            Swal.fire({
                                icon: 'success',
                                title: 'تم إنشاء النسخة الاحتياطية!',
                                text: 'تم حفظ النسخة الاحتياطية بنجاح في المجلد المخصص',
                                confirmButtonText: 'ممتاز',
                                confirmButtonColor: '#10b981',
                                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                            });
                        }
                    });
                }
            });
        }
        
        function sendNotification() {
            Swal.fire({
                title: 'إرسال إشعار جماعي',
                html: `
                    <div class="text-start" style="direction: rtl;">
                        <div class="mb-3">
                            <label class="form-label">عنوان الإشعار:</label>
                            <input type="text" id="notificationTitle" class="form-control" placeholder="أدخل عنوان الإشعار">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">نص الإشعار:</label>
                            <textarea id="notificationMessage" class="form-control" rows="3" placeholder="أدخل نص الإشعار"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المستقبلون:</label>
                            <select id="notificationTarget" class="form-select">
                                <option value="all">جميع المستخدمين</option>
                                <option value="admins">المديرين فقط</option>
                                <option value="users">المستخدمين العاديين</option>
                            </select>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'إرسال الإشعار',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#6b7280',
                width: 600,
                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937',
                preConfirm: () => {
                    const title = document.getElementById('notificationTitle').value;
                    const message = document.getElementById('notificationMessage').value;
                    const target = document.getElementById('notificationTarget').value;

                    if (!title || !message) {
                        Swal.showValidationMessage('يرجى ملء جميع الحقول المطلوبة');
                        return false;
                    }

                    return { title, message, target };
                }
            }).then((result) => {
                if (result.isConfirmed) {
                    const { title, message, target } = result.value;

                    Swal.fire({
                        title: 'جاري الإرسال...',
                        html: `إرسال الإشعار "${title}" إلى ${target === 'all' ? 'جميع المستخدمين' : target === 'admins' ? 'المديرين' : 'المستخدمين العاديين'}`,
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                        background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                        color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                    });

                    setTimeout(() => {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم الإرسال بنجاح!',
                            text: `تم إرسال الإشعار "${title}" بنجاح`,
                            confirmButtonText: 'ممتاز',
                            confirmButtonColor: '#10b981',
                            background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                            color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                        });
                    }, 2000);
                }
            });
        }
        
        function viewReports() {
            showSection('reports');
        }
        
        function saveSettings() {
            Swal.fire({
                title: 'حفظ الإعدادات',
                text: 'هل تريد حفظ التغييرات التي أجريتها على الإعدادات؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، احفظ',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#667eea',
                cancelButtonColor: '#6b7280',
                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'جاري الحفظ...',
                        html: 'يتم حفظ الإعدادات، يرجى الانتظار',
                        allowOutsideClick: false,
                        didOpen: () => {
                            Swal.showLoading();
                        },
                        background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                        color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                    });

                    setTimeout(() => {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم الحفظ!',
                            text: 'تم حفظ جميع الإعدادات بنجاح',
                            timer: 2000,
                            showConfirmButton: false,
                            background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                            color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                        });
                    }, 1500);
                }
            });
        }
        
        // Initialize charts
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize dark mode
            initializeDarkMode();
            // Profit Chart (Bar Chart)
            const profitCtx = document.getElementById('profitChart').getContext('2d');
            new Chart(profitCtx, {
                type: 'bar',
                data: {
                    labels: ['Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa', 'Su'],
                    datasets: [{
                        label: 'الأرباح',
                        data: [65, 85, 70, 95, 120, 80, 90],
                        backgroundColor: '#667eea',
                        borderRadius: 4,
                        borderSkipped: false,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            display: false,
                            grid: {
                                display: false
                            }
                        }
                    }
                }
            });

            // Support Tracker Chart (Doughnut)
            const supportCtx = document.getElementById('supportChart').getContext('2d');
            new Chart(supportCtx, {
                type: 'doughnut',
                data: {
                    datasets: [{
                        data: [85, 15],
                        backgroundColor: ['#667eea', '#e5e7eb'],
                        borderWidth: 0,
                        cutout: '75%'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: true,
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            enabled: false
                        }
                    }
                }
            });

            // Sales Chart
            const salesCtx = document.getElementById('salesChart').getContext('2d');
            new Chart(salesCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'المبيعات',
                        data: [12000, 19000, 15000, 25000, 22000, 30000],
                        borderColor: '#667eea',
                        backgroundColor: 'rgba(102, 126, 234, 0.1)',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Users Chart
            const usersCtx = document.getElementById('usersChart').getContext('2d');
            new Chart(usersCtx, {
                type: 'doughnut',
                data: {
                    labels: ['مستخدمين نشطين', 'مستخدمين غير نشطين'],
                    datasets: [{
                        data: [847, 400],
                        backgroundColor: ['#10b981', '#ef4444'],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });

            // Update stats periodically
            setInterval(updateStats, 5000);

            // Animate support tracker percentage
            animateSupportPercentage();
        });
        
        // Update statistics
        function updateStats() {
            // Simulate real-time updates
            const totalUsers = document.getElementById('totalUsers');
            const currentValue = parseInt(totalUsers.textContent.replace(',', ''));
            const newValue = currentValue + Math.floor(Math.random() * 5);
            totalUsers.textContent = newValue.toLocaleString();

            // Update other stats similarly
            const todaySales = document.getElementById('todaySales');
            const currentSales = parseInt(todaySales.textContent.replace('$', '').replace(',', ''));
            const newSales = currentSales + Math.floor(Math.random() * 100);
            todaySales.textContent = '$' + newSales.toLocaleString();
        }

        // Animate support tracker percentage
        function animateSupportPercentage() {
            const percentageElement = document.querySelector('.support-percentage h2');
            let currentPercentage = 0;
            const targetPercentage = 85;
            const increment = 1;
            const duration = 2000; // 2 seconds
            const intervalTime = duration / targetPercentage;

            const interval = setInterval(() => {
                currentPercentage += increment;
                percentageElement.textContent = currentPercentage + '%';

                if (currentPercentage >= targetPercentage) {
                    clearInterval(interval);
                    percentageElement.textContent = targetPercentage + '%';
                }
            }, intervalTime);
        }

        // Additional functions for new sections
        function uploadMedia() {
            Swal.fire({
                title: 'رفع ملفات جديدة',
                html: `
                    <div class="text-start" style="direction: rtl;">
                        <div class="mb-3">
                            <label class="form-label">اختر الملفات:</label>
                            <input type="file" id="mediaFiles" class="form-control" multiple accept="image/*,video/*,.pdf,.doc,.docx">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المجلد:</label>
                            <select id="mediaFolder" class="form-select">
                                <option value="images">الصور</option>
                                <option value="videos">الفيديو</option>
                                <option value="documents">المستندات</option>
                            </select>
                        </div>
                    </div>
                `,
                showCancelButton: true,
                confirmButtonText: 'رفع الملفات',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#667eea',
                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
            }).then((result) => {
                if (result.isConfirmed) {
                    showNotification('تم رفع الملفات بنجاح', 'success');
                }
            });
        }

        function clearTempFiles() {
            Swal.fire({
                title: 'تنظيف الملفات المؤقتة',
                text: 'هل تريد مسح جميع الملفات المؤقتة؟',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، نظف',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#f59e0b',
                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'جاري التنظيف...',
                        allowOutsideClick: false,
                        didOpen: () => Swal.showLoading(),
                        background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                        color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                    });

                    setTimeout(() => {
                        Swal.fire({
                            icon: 'success',
                            title: 'تم التنظيف!',
                            text: 'تم مسح 245 MB من الملفات المؤقتة',
                            background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                            color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                        });
                    }, 3000);
                }
            });
        }

        function optimizeDatabase() {
            Swal.fire({
                title: 'تحسين قاعدة البيانات',
                text: 'هل تريد تحسين جميع جداول قاعدة البيانات؟',
                icon: 'question',
                showCancelButton: true,
                confirmButtonText: 'نعم، حسّن',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#10b981',
                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
            }).then((result) => {
                if (result.isConfirmed) {
                    let progress = 0;
                    const interval = setInterval(() => {
                        progress += 10;
                        Swal.update({
                            html: `جاري تحسين قاعدة البيانات... ${progress}%<br><div class="progress mt-2"><div class="progress-bar" style="width: ${progress}%"></div></div>`
                        });

                        if (progress >= 100) {
                            clearInterval(interval);
                            Swal.fire({
                                icon: 'success',
                                title: 'تم التحسين!',
                                text: 'تم تحسين قاعدة البيانات بنجاح وتوفير 12 MB',
                                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                            });
                        }
                    }, 500);
                }
            });
        }

        function restartSystem() {
            Swal.fire({
                title: 'إعادة تشغيل النظام',
                text: 'هل أنت متأكد من رغبتك في إعادة تشغيل النظام؟ سيتم قطع الاتصال مؤقتاً.',
                icon: 'warning',
                showCancelButton: true,
                confirmButtonText: 'نعم، أعد التشغيل',
                cancelButtonText: 'إلغاء',
                confirmButtonColor: '#ef4444',
                background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
            }).then((result) => {
                if (result.isConfirmed) {
                    Swal.fire({
                        title: 'جاري إعادة التشغيل...',
                        text: 'سيتم إعادة توجيهك تلقائياً بعد اكتمال العملية',
                        allowOutsideClick: false,
                        didOpen: () => Swal.showLoading(),
                        background: document.body.classList.contains('dark-mode') ? '#374151' : '#ffffff',
                        color: document.body.classList.contains('dark-mode') ? '#e5e7eb' : '#1f2937'
                    });

                    setTimeout(() => {
                        window.location.reload();
                    }, 5000);
                }
            });
        }

        function backupDatabase() {
            showNotification('جاري إنشاء نسخة احتياطية من قاعدة البيانات...', 'info');
            setTimeout(() => {
                showNotification('تم إنشاء النسخة الاحتياطية بنجاح', 'success');
            }, 3000);
        }

        function optimizeAllTables() {
            showNotification('جاري تحسين جميع الجداول...', 'info');
            setTimeout(() => {
                showNotification('تم تحسين جميع الجداول بنجاح', 'success');
            }, 4000);
        }

        function repairDatabase() {
            showNotification('جاري فحص وإصلاح قاعدة البيانات...', 'info');
            setTimeout(() => {
                showNotification('تم فحص وإصلاح قاعدة البيانات بنجاح', 'success');
            }, 5000);
        }

        // Dropdown toggle function
        function toggleDropdown(element) {
            const dropdown = element.parentElement;
            const isOpen = dropdown.classList.contains('open');

            // Close all other dropdowns
            document.querySelectorAll('.nav-dropdown.open').forEach(dd => {
                if (dd !== dropdown) {
                    dd.classList.remove('open');
                }
            });

            // Toggle current dropdown
            dropdown.classList.toggle('open', !isOpen);

            // Add ripple effect
            const ripple = document.createElement('span');
            ripple.classList.add('ripple');
            element.appendChild(ripple);

            const rect = element.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = '50%';
            ripple.style.top = '50%';
            ripple.style.transform = 'translate(-50%, -50%)';

            setTimeout(() => {
                ripple.remove();
            }, 600);
        }

        // Initialize Swiper
        function initializeSwiper() {
            const swiper = new Swiper('.analytics-swiper', {
                slidesPerView: 1,
                spaceBetween: 30,
                loop: true,
                autoplay: {
                    delay: 5000,
                    disableOnInteraction: false,
                },
                pagination: {
                    el: '.swiper-pagination',
                    clickable: true,
                },
                effect: 'fade',
                fadeEffect: {
                    crossFade: true
                },
                on: {
                    slideChange: function () {
                        // Add animation to metrics when slide changes
                        const activeSlide = this.slides[this.activeIndex];
                        const metrics = activeSlide.querySelectorAll('.analytics-metric');
                        metrics.forEach((metric, index) => {
                            metric.style.opacity = '0';
                            metric.style.transform = 'translateY(20px)';
                            setTimeout(() => {
                                metric.style.transition = 'all 0.5s ease';
                                metric.style.opacity = '1';
                                metric.style.transform = 'translateY(0)';
                            }, index * 100);
                        });
                    }
                }
            });
        }

        // Enhanced sidebar interactions
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Swiper
            initializeSwiper();
            // Add hover effects to profit details
            const profitDetails = document.querySelectorAll('.profit-detail');
            profitDetails.forEach(detail => {
                detail.addEventListener('mouseenter', function() {
                    this.style.transform = 'scale(1.05)';
                    this.style.transition = 'transform 0.3s ease';
                });

                detail.addEventListener('mouseleave', function() {
                    this.style.transform = 'scale(1)';
                });
            });

            // Smooth scrolling for sidebar navigation
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Add ripple effect
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);

                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = (e.clientX - rect.left - size / 2) + 'px';
                    ripple.style.top = (e.clientY - rect.top - size / 2) + 'px';

                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });

            // Auto-hide sidebar on mobile after selection
            if (window.innerWidth <= 768) {
                navLinks.forEach(link => {
                    link.addEventListener('click', function() {
                        setTimeout(() => {
                            document.getElementById('sidebar').classList.remove('show');
                        }, 300);
                    });
                });
            }

            // Keyboard navigation for sidebar
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey && e.key === 'b') {
                    e.preventDefault();
                    toggleSidebar();
                }
            });
        });
    </script>
</body>
</html>
