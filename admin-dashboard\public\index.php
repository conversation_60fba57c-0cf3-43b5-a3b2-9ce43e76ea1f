<?php
// Simple routing for admin dashboard
$request_uri = $_SERVER['REQUEST_URI'];
$path = parse_url($request_uri, PHP_URL_PATH);

// Remove leading slash
$path = ltrim($path, '/');

// Route handling
switch ($path) {
    case '':
    case 'admin':
        include 'welcome.php';
        break;
    case 'metatech':
        include 'metatech.php';
        break;
    case 'admin/login':
        include 'admin-login.php';
        break;
    case 'admin/dashboard':
        include 'admin-dashboard.php';
        break;
    case 'admin/demo':
        include 'demo.php';
        break;
    default:
        // Check if file exists
        if (file_exists($path . '.php')) {
            include $path . '.php';
        } else {
            // 404 page
            http_response_code(404);
            echo '<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="utf-8">
    <title>الصفحة غير موجودة</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
</head>
<body class="bg-light d-flex align-items-center justify-content-center" style="min-height: 100vh;">
    <div class="text-center">
        <h1 class="display-1">404</h1>
        <h2>الصفحة غير موجودة</h2>
        <a href="/" class="btn btn-primary">العودة للرئيسية</a>
    </div>
</body>
</html>';
        }
        break;
}
